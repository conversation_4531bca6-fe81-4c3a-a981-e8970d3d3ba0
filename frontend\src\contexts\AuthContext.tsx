import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authApi, setAuthLogout } from '@/lib/api';

interface User {
  isAuthenticated: boolean;
  token?: string;
  tokenExpiry?: number;
}

interface AuthContextType {
  user: User;
  login: (password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User>({ isAuthenticated: false });
  const [isLoading, setIsLoading] = useState(true);

  // 检查token是否过期
  const isTokenExpired = (tokenExpiry?: number): boolean => {
    if (!tokenExpiry) return true;
    return Date.now() > tokenExpiry;
  };

  // 初始化时检查本地存储的认证状态
  useEffect(() => {
    const checkAuth = () => {
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      const token = localStorage.getItem('authToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      
      if (isAuthenticated && token && tokenExpiry) {
        const expiry = parseInt(tokenExpiry, 10);
        
        if (!isTokenExpired(expiry)) {
          setUser({ 
            isAuthenticated: true, 
            token,
            tokenExpiry: expiry
          });
        } else {
          // Token已过期，清除认证状态
          logout();
        }
      } else {
        setUser({ isAuthenticated: false });
      }
      
      setIsLoading(false);
    };

    checkAuth();
    
    // 设置API的logout回调
    setAuthLogout(logout);
  }, []);

  const login = async (password: string): Promise<boolean> => {
    try {
      const response = await authApi.login(password, { showToast: false });
      
      if (response.success && response.token) {
        // Token有效期设为24小时
        const tokenExpiry = Date.now() + (24 * 60 * 60 * 1000);
        
        const newUser: User = {
          isAuthenticated: true,
          token: response.token,
          tokenExpiry,
        };
        
        setUser(newUser);
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('authToken', response.token);
        localStorage.setItem('tokenExpiry', tokenExpiry.toString());
        
        return true;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  };

  const logout = () => {
    setUser({ isAuthenticated: false });
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('authToken');
    localStorage.removeItem('tokenExpiry');
  };

  const value: AuthContextType = {
    user,
    login,
    logout,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
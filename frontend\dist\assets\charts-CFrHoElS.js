import{c as re}from"./utils-CUkY6B8V.js";import{d as Si,g as se,r as q,R as S}from"./react-vendor-DucYQ77b.js";var Fm=Array.isArray,We=Fm,zm=typeof Si=="object"&&Si&&Si.Object===Object&&Si,Gd=zm,Wm=Gd,Um=typeof self=="object"&&self&&self.Object===Object&&self,qm=Wm||Um||Function("return this")(),ht=qm,Hm=ht,Km=Hm.Symbol,yi=Km,Us=yi,Yd=Object.prototype,Gm=Yd.hasOwnProperty,Ym=Yd.toString,gn=Us?Us.toStringTag:void 0;function Xm(e){var t=Gm.call(e,gn),r=e[gn];try{e[gn]=void 0;var n=!0}catch{}var i=Ym.call(e);return n&&(t?e[gn]=r:delete e[gn]),i}var Vm=Xm,Zm=Object.prototype,Jm=Zm.toString;function Qm(e){return Jm.call(e)}var eg=Qm,qs=yi,tg=Vm,rg=eg,ng="[object Null]",ig="[object Undefined]",Hs=qs?qs.toStringTag:void 0;function ag(e){return e==null?e===void 0?ig:ng:Hs&&Hs in Object(e)?tg(e):rg(e)}var jt=ag;function og(e){return e!=null&&typeof e=="object"}var Mt=og,ug=jt,cg=Mt,sg="[object Symbol]";function lg(e){return typeof e=="symbol"||cg(e)&&ug(e)==sg}var tn=lg,fg=We,pg=tn,dg=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,hg=/^\w*$/;function vg(e,t){if(fg(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||pg(e)?!0:hg.test(e)||!dg.test(e)||t!=null&&e in Object(t)}var Dc=vg;function yg(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Bt=yg;const rn=se(Bt);var mg=jt,gg=Bt,bg="[object AsyncFunction]",xg="[object Function]",wg="[object GeneratorFunction]",Og="[object Proxy]";function Ag(e){if(!gg(e))return!1;var t=mg(e);return t==xg||t==wg||t==bg||t==Og}var Nc=Ag;const Z=se(Nc);var Sg=ht,Pg=Sg["__core-js_shared__"],_g=Pg,Mo=_g,Ks=function(){var e=/[^.]+$/.exec(Mo&&Mo.keys&&Mo.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function $g(e){return!!Ks&&Ks in e}var Tg=$g,Eg=Function.prototype,jg=Eg.toString;function Mg(e){if(e!=null){try{return jg.call(e)}catch{}try{return e+""}catch{}}return""}var Xd=Mg,Cg=Nc,Ig=Tg,kg=Bt,Dg=Xd,Ng=/[\\^$.*+?()[\]{}|]/g,Rg=/^\[object .+?Constructor\]$/,Lg=Function.prototype,Bg=Object.prototype,Fg=Lg.toString,zg=Bg.hasOwnProperty,Wg=RegExp("^"+Fg.call(zg).replace(Ng,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Ug(e){if(!kg(e)||Ig(e))return!1;var t=Cg(e)?Wg:Rg;return t.test(Dg(e))}var qg=Ug;function Hg(e,t){return e==null?void 0:e[t]}var Kg=Hg,Gg=qg,Yg=Kg;function Xg(e,t){var r=Yg(e,t);return Gg(r)?r:void 0}var lr=Xg,Vg=lr,Zg=Vg(Object,"create"),Ua=Zg,Gs=Ua;function Jg(){this.__data__=Gs?Gs(null):{},this.size=0}var Qg=Jg;function eb(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var tb=eb,rb=Ua,nb="__lodash_hash_undefined__",ib=Object.prototype,ab=ib.hasOwnProperty;function ob(e){var t=this.__data__;if(rb){var r=t[e];return r===nb?void 0:r}return ab.call(t,e)?t[e]:void 0}var ub=ob,cb=Ua,sb=Object.prototype,lb=sb.hasOwnProperty;function fb(e){var t=this.__data__;return cb?t[e]!==void 0:lb.call(t,e)}var pb=fb,db=Ua,hb="__lodash_hash_undefined__";function vb(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=db&&t===void 0?hb:t,this}var yb=vb,mb=Qg,gb=tb,bb=ub,xb=pb,wb=yb;function nn(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}nn.prototype.clear=mb;nn.prototype.delete=gb;nn.prototype.get=bb;nn.prototype.has=xb;nn.prototype.set=wb;var Ob=nn;function Ab(){this.__data__=[],this.size=0}var Sb=Ab;function Pb(e,t){return e===t||e!==e&&t!==t}var Rc=Pb,_b=Rc;function $b(e,t){for(var r=e.length;r--;)if(_b(e[r][0],t))return r;return-1}var qa=$b,Tb=qa,Eb=Array.prototype,jb=Eb.splice;function Mb(e){var t=this.__data__,r=Tb(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():jb.call(t,r,1),--this.size,!0}var Cb=Mb,Ib=qa;function kb(e){var t=this.__data__,r=Ib(t,e);return r<0?void 0:t[r][1]}var Db=kb,Nb=qa;function Rb(e){return Nb(this.__data__,e)>-1}var Lb=Rb,Bb=qa;function Fb(e,t){var r=this.__data__,n=Bb(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var zb=Fb,Wb=Sb,Ub=Cb,qb=Db,Hb=Lb,Kb=zb;function an(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}an.prototype.clear=Wb;an.prototype.delete=Ub;an.prototype.get=qb;an.prototype.has=Hb;an.prototype.set=Kb;var Ha=an,Gb=lr,Yb=ht,Xb=Gb(Yb,"Map"),Lc=Xb,Ys=Ob,Vb=Ha,Zb=Lc;function Jb(){this.size=0,this.__data__={hash:new Ys,map:new(Zb||Vb),string:new Ys}}var Qb=Jb;function e0(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var t0=e0,r0=t0;function n0(e,t){var r=e.__data__;return r0(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Ka=n0,i0=Ka;function a0(e){var t=i0(this,e).delete(e);return this.size-=t?1:0,t}var o0=a0,u0=Ka;function c0(e){return u0(this,e).get(e)}var s0=c0,l0=Ka;function f0(e){return l0(this,e).has(e)}var p0=f0,d0=Ka;function h0(e,t){var r=d0(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var v0=h0,y0=Qb,m0=o0,g0=s0,b0=p0,x0=v0;function on(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}on.prototype.clear=y0;on.prototype.delete=m0;on.prototype.get=g0;on.prototype.has=b0;on.prototype.set=x0;var Bc=on,Vd=Bc,w0="Expected a function";function Fc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(w0);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(Fc.Cache||Vd),r}Fc.Cache=Vd;var Zd=Fc;const O0=se(Zd);var A0=Zd,S0=500;function P0(e){var t=A0(e,function(n){return r.size===S0&&r.clear(),n}),r=t.cache;return t}var _0=P0,$0=_0,T0=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,E0=/\\(\\)?/g,j0=$0(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(T0,function(r,n,i,a){t.push(i?a.replace(E0,"$1"):n||r)}),t}),M0=j0;function C0(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var zc=C0,Xs=yi,I0=zc,k0=We,D0=tn,N0=1/0,Vs=Xs?Xs.prototype:void 0,Zs=Vs?Vs.toString:void 0;function Jd(e){if(typeof e=="string")return e;if(k0(e))return I0(e,Jd)+"";if(D0(e))return Zs?Zs.call(e):"";var t=e+"";return t=="0"&&1/e==-N0?"-0":t}var R0=Jd,L0=R0;function B0(e){return e==null?"":L0(e)}var Qd=B0,F0=We,z0=Dc,W0=M0,U0=Qd;function q0(e,t){return F0(e)?e:z0(e,t)?[e]:W0(U0(e))}var eh=q0,H0=tn,K0=1/0;function G0(e){if(typeof e=="string"||H0(e))return e;var t=e+"";return t=="0"&&1/e==-K0?"-0":t}var Ga=G0,Y0=eh,X0=Ga;function V0(e,t){t=Y0(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[X0(t[r++])];return r&&r==n?e:void 0}var Wc=V0,Z0=Wc;function J0(e,t,r){var n=e==null?void 0:Z0(e,t);return n===void 0?r:n}var th=J0;const Ge=se(th);function Q0(e){return e==null}var ex=Q0;const ee=se(ex);var tx=jt,rx=We,nx=Mt,ix="[object String]";function ax(e){return typeof e=="string"||!rx(e)&&nx(e)&&tx(e)==ix}var ox=ax;const mi=se(ox);var rh={exports:{}},oe={};/**
 * @license React
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),d=Symbol.for("react.offscreen"),y=!1,h=!1,v=!1,x=!1,b=!1,w;w=Symbol.for("react.module.reference");function g(W){return!!(typeof W=="string"||typeof W=="function"||W===r||W===i||b||W===n||W===s||W===f||x||W===d||y||h||v||typeof W=="object"&&W!==null&&(W.$$typeof===p||W.$$typeof===l||W.$$typeof===a||W.$$typeof===o||W.$$typeof===c||W.$$typeof===w||W.getModuleId!==void 0))}function m(W){if(typeof W=="object"&&W!==null){var pe=W.$$typeof;switch(pe){case e:var ye=W.type;switch(ye){case r:case i:case n:case s:case f:return ye;default:var Ie=ye&&ye.$$typeof;switch(Ie){case u:case o:case c:case p:case l:case a:return Ie;default:return pe}}case t:return pe}}}var O=o,A=a,P=e,j=c,M=r,T=p,C=l,_=t,$=i,k=n,I=s,D=f,R=!1,L=!1;function U(W){return R||(R=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")),!1}function B(W){return L||(L=!0,console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")),!1}function F(W){return m(W)===o}function H(W){return m(W)===a}function J(W){return typeof W=="object"&&W!==null&&W.$$typeof===e}function ne(W){return m(W)===c}function we(W){return m(W)===r}function Oe(W){return m(W)===p}function Ue(W){return m(W)===l}function K(W){return m(W)===t}function V(W){return m(W)===i}function Q(W){return m(W)===n}function E(W){return m(W)===s}function le(W){return m(W)===f}oe.ContextConsumer=O,oe.ContextProvider=A,oe.Element=P,oe.ForwardRef=j,oe.Fragment=M,oe.Lazy=T,oe.Memo=C,oe.Portal=_,oe.Profiler=$,oe.StrictMode=k,oe.Suspense=I,oe.SuspenseList=D,oe.isAsyncMode=U,oe.isConcurrentMode=B,oe.isContextConsumer=F,oe.isContextProvider=H,oe.isElement=J,oe.isForwardRef=ne,oe.isFragment=we,oe.isLazy=Oe,oe.isMemo=Ue,oe.isPortal=K,oe.isProfiler=V,oe.isStrictMode=Q,oe.isSuspense=E,oe.isSuspenseList=le,oe.isValidElementType=g,oe.typeOf=m})();rh.exports=oe;var nh=rh.exports,ux=jt,cx=Mt,sx="[object Number]";function lx(e){return typeof e=="number"||cx(e)&&ux(e)==sx}var ih=lx;const fx=se(ih);var px=ih;function dx(e){return px(e)&&e!=+e}var hx=dx;const un=se(hx);var Re=function(t){return t===0?0:t>0?1:-1},Jt=function(t){return mi(t)&&t.indexOf("%")===t.length-1},z=function(t){return fx(t)&&!un(t)},$e=function(t){return z(t)||mi(t)},vx=0,cn=function(t){var r=++vx;return"".concat(t||"").concat(r)},Le=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!z(t)&&!mi(t))return n;var a;if(Jt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return un(a)&&(a=n),i&&a>r&&(a=r),a},Dt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},yx=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Fe=function(t,r){return z(t)&&z(r)?function(n){return t+n*(r-t)}:function(){return r}};function zi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ge(n,t))===r})}function Ar(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function ou(e){"@babel/helpers - typeof";return ou=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ou(e)}var mx=["viewBox","children"],gx=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Js=["points","pathLength"],Co={svg:mx,polygon:Js,polyline:Js},Uc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Wi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!rn(n))return null;var i={};return Object.keys(n).forEach(function(a){Uc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},bx=function(t,r,n){return function(i){return t(r,n,i),null}},ar=function(t,r,n){if(!rn(t)||ou(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];Uc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=bx(o,r,n))}),i},xx=["children"],wx=["children"];function Qs(e,t){if(e==null)return{};var r=Ox(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ox(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function uu(e){"@babel/helpers - typeof";return uu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uu(e)}var el={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},St=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},tl=null,Io=null,qc=function e(t){if(t===tl&&Array.isArray(Io))return Io;var r=[];return q.Children.forEach(t,function(n){ee(n)||(nh.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Io=r,tl=t,r};function Qe(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return St(i)}):n=[St(t)],qc(e).forEach(function(i){var a=Ge(i,"type.displayName")||Ge(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function He(e,t){var r=Qe(e,t);return r&&r[0]}var rl=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!z(n)||n<=0||!z(i)||i<=0)},Ax=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Sx=function(t){return t&&t.type&&mi(t.type)&&Ax.indexOf(t.type)>=0},Px=function(t){return t&&uu(t)==="object"&&"clipDot"in t},_x=function(t,r,n,i){var a,o=(a=Co==null?void 0:Co[i])!==null&&a!==void 0?a:[];return!Z(t)&&(i&&o.includes(r)||gx.includes(r))||n&&Uc.includes(r)},Y=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!rn(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;_x((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},cu=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return nl(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!nl(a,o))return!1}return!0},nl=function(t,r){if(ee(t)&&ee(r))return!0;if(!ee(t)&&!ee(r)){var n=t.props||{},i=n.children,a=Qs(n,xx),o=r.props||{},u=o.children,c=Qs(o,wx);return i&&u?Ar(a,c)&&cu(i,u):!i&&!u?Ar(a,c):!1}return!1},il=function(t,r){var n=[],i={};return qc(t).forEach(function(a,o){if(Sx(a))n.push(a);else if(a){var u=St(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},$x=function(t){var r=t&&t.type;return r&&el[r]?el[r]:null},Tx=function(t,r){return qc(r).indexOf(t)},Ex=["children","width","height","viewBox","className","style","title","desc"];function su(){return su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},su.apply(this,arguments)}function jx(e,t){if(e==null)return{};var r=Mx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Mx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function lu(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=jx(e,Ex),f=i||{width:r,height:n,x:0,y:0},l=re("recharts-surface",a);return S.createElement("svg",su({},Y(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),S.createElement("title",null,u),S.createElement("desc",null,c),t)}var Cx=["children","className"];function fu(){return fu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fu.apply(this,arguments)}function Ix(e,t){if(e==null)return{};var r=kx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function kx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ie=S.forwardRef(function(e,t){var r=e.children,n=e.className,i=Ix(e,Cx),a=re("recharts-layer",n);return S.createElement("g",fu({className:a},Y(i,!0),{ref:t}),r)}),at=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a];if(typeof console<"u"&&console.warn&&(r===void 0&&console.warn("LogUtils requires an error message argument"),!t))if(r===void 0)console.warn("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var o=0;console.warn(r.replace(/%s/g,function(){return i[o++]}))}};function Dx(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var Nx=Dx,Rx=Nx;function Lx(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:Rx(e,t,r)}var Bx=Lx,Fx="\\ud800-\\udfff",zx="\\u0300-\\u036f",Wx="\\ufe20-\\ufe2f",Ux="\\u20d0-\\u20ff",qx=zx+Wx+Ux,Hx="\\ufe0e\\ufe0f",Kx="\\u200d",Gx=RegExp("["+Kx+Fx+qx+Hx+"]");function Yx(e){return Gx.test(e)}var ah=Yx;function Xx(e){return e.split("")}var Vx=Xx,oh="\\ud800-\\udfff",Zx="\\u0300-\\u036f",Jx="\\ufe20-\\ufe2f",Qx="\\u20d0-\\u20ff",ew=Zx+Jx+Qx,tw="\\ufe0e\\ufe0f",rw="["+oh+"]",pu="["+ew+"]",du="\\ud83c[\\udffb-\\udfff]",nw="(?:"+pu+"|"+du+")",uh="[^"+oh+"]",ch="(?:\\ud83c[\\udde6-\\uddff]){2}",sh="[\\ud800-\\udbff][\\udc00-\\udfff]",iw="\\u200d",lh=nw+"?",fh="["+tw+"]?",aw="(?:"+iw+"(?:"+[uh,ch,sh].join("|")+")"+fh+lh+")*",ow=fh+lh+aw,uw="(?:"+[uh+pu+"?",pu,ch,sh,rw].join("|")+")",cw=RegExp(du+"(?="+du+")|"+uw+ow,"g");function sw(e){return e.match(cw)||[]}var lw=sw,fw=Vx,pw=ah,dw=lw;function hw(e){return pw(e)?dw(e):fw(e)}var vw=hw,yw=Bx,mw=ah,gw=vw,bw=Qd;function xw(e){return function(t){t=bw(t);var r=mw(t)?gw(t):void 0,n=r?r[0]:t.charAt(0),i=r?yw(r,1).join(""):t.slice(1);return n[e]()+i}}var ww=xw,Ow=ww,Aw=Ow("toUpperCase"),Sw=Aw;const Ya=se(Sw);function he(e){return function(){return e}}const ph=Math.cos,Ui=Math.sin,ot=Math.sqrt,qi=Math.PI,Xa=2*qi,hu=Math.PI,vu=2*hu,Yt=1e-6,Pw=vu-Yt;function dh(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function _w(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return dh;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class $w{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?dh:_w(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,p=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Yt)if(!(Math.abs(l*c-s*f)>Yt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=n-o,y=i-u,h=c*c+s*s,v=d*d+y*y,x=Math.sqrt(h),b=Math.sqrt(p),w=a*Math.tan((hu-Math.acos((h+p-v)/(2*x*b)))/2),g=w/b,m=w/x;Math.abs(g-1)>Yt&&this._append`L${t+g*f},${r+g*l}`,this._append`A${a},${a},0,0,${+(l*d>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Yt||Math.abs(this._y1-f)>Yt)&&this._append`L${s},${f}`,n&&(p<0&&(p=p%vu+vu),p>Pw?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:p>Yt&&this._append`A${n},${n},0,${+(p>=hu)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Hc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new $w(t)}function Kc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function hh(e){this._context=e}hh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Va(e){return new hh(e)}function vh(e){return e[0]}function yh(e){return e[1]}function mh(e,t){var r=he(!0),n=null,i=Va,a=null,o=Hc(u);e=typeof e=="function"?e:e===void 0?vh:he(e),t=typeof t=="function"?t:t===void 0?yh:he(t);function u(c){var s,f=(c=Kc(c)).length,l,p=!1,d;for(n==null&&(a=i(d=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(l,s,c),+t(l,s,c));if(d)return a=null,d+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:he(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:he(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:he(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function Pi(e,t,r){var n=null,i=he(!0),a=null,o=Va,u=null,c=Hc(s);e=typeof e=="function"?e:e===void 0?vh:he(+e),t=typeof t=="function"?t:he(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?yh:he(+r);function s(l){var p,d,y,h=(l=Kc(l)).length,v,x=!1,b,w=new Array(h),g=new Array(h);for(a==null&&(u=o(b=c())),p=0;p<=h;++p){if(!(p<h&&i(v=l[p],p,l))===x)if(x=!x)d=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=p-1;y>=d;--y)u.point(w[y],g[y]);u.lineEnd(),u.areaEnd()}x&&(w[p]=+e(v,p,l),g[p]=+t(v,p,l),u.point(n?+n(v,p,l):w[p],r?+r(v,p,l):g[p]))}if(b)return u=null,b+""||null}function f(){return mh().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:he(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:he(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:he(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:he(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:he(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:he(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:he(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class gh{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function Tw(e){return new gh(e,!0)}function Ew(e){return new gh(e,!1)}const Gc={draw(e,t){const r=ot(t/qi);e.moveTo(r,0),e.arc(0,0,r,0,Xa)}},jw={draw(e,t){const r=ot(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},bh=ot(1/3),Mw=bh*2,Cw={draw(e,t){const r=ot(t/Mw),n=r*bh;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Iw={draw(e,t){const r=ot(t),n=-r/2;e.rect(n,n,r,r)}},kw=.8908130915292852,xh=Ui(qi/10)/Ui(7*qi/10),Dw=Ui(Xa/10)*xh,Nw=-ph(Xa/10)*xh,Rw={draw(e,t){const r=ot(t*kw),n=Dw*r,i=Nw*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=Xa*a/5,u=ph(o),c=Ui(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},ko=ot(3),Lw={draw(e,t){const r=-ot(t/(ko*3));e.moveTo(0,r*2),e.lineTo(-ko*r,-r),e.lineTo(ko*r,-r),e.closePath()}},Ye=-.5,Xe=ot(3)/2,yu=1/ot(12),Bw=(yu/2+1)*3,Fw={draw(e,t){const r=ot(t/Bw),n=r/2,i=r*yu,a=n,o=r*yu+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ye*n-Xe*i,Xe*n+Ye*i),e.lineTo(Ye*a-Xe*o,Xe*a+Ye*o),e.lineTo(Ye*u-Xe*c,Xe*u+Ye*c),e.lineTo(Ye*n+Xe*i,Ye*i-Xe*n),e.lineTo(Ye*a+Xe*o,Ye*o-Xe*a),e.lineTo(Ye*u+Xe*c,Ye*c-Xe*u),e.closePath()}};function zw(e,t){let r=null,n=Hc(i);e=typeof e=="function"?e:he(e||Gc),t=typeof t=="function"?t:he(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:he(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:he(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function Hi(){}function Ki(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function wh(e){this._context=e}wh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ki(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Ww(e){return new wh(e)}function Oh(e){this._context=e}Oh.prototype={areaStart:Hi,areaEnd:Hi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Uw(e){return new Oh(e)}function Ah(e){this._context=e}Ah.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ki(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function qw(e){return new Ah(e)}function Sh(e){this._context=e}Sh.prototype={areaStart:Hi,areaEnd:Hi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Hw(e){return new Sh(e)}function al(e){return e<0?-1:1}function ol(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(al(a)+al(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function ul(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Do(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Gi(e){this._context=e}Gi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Do(this,this._t0,ul(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Do(this,ul(this,r=ol(this,e,t)),r);break;default:Do(this,this._t0,r=ol(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Ph(e){this._context=new _h(e)}(Ph.prototype=Object.create(Gi.prototype)).point=function(e,t){Gi.prototype.point.call(this,t,e)};function _h(e){this._context=e}_h.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Kw(e){return new Gi(e)}function Gw(e){return new Ph(e)}function $h(e){this._context=e}$h.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=cl(e),i=cl(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function cl(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Yw(e){return new $h(e)}function Za(e,t){this._context=e,this._t=t}Za.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Xw(e){return new Za(e,.5)}function Vw(e){return new Za(e,0)}function Zw(e){return new Za(e,1)}function Tr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function mu(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Jw(e,t){return e[t]}function Qw(e){const t=[];return t.key=e,t}function eO(){var e=he([]),t=mu,r=Tr,n=Jw;function i(a){var o=Array.from(e.apply(this,arguments),Qw),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Kc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:he(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:he(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?mu:typeof a=="function"?a:he(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Tr,i):r},i}function tO(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Tr(e,t)}}function rO(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}Tr(e,t)}}function nO(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,p=f[n-1][1]||0,d=(l-p)/2,y=0;y<u;++y){var h=e[t[y]],v=h[n][1]||0,x=h[n-1][1]||0;d+=v-x}c+=l,s+=d*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,Tr(e,t)}}function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}var iO=["type","size","sizeType"];function gu(){return gu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gu.apply(this,arguments)}function sl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ll(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sl(Object(r),!0).forEach(function(n){aO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function aO(e,t,r){return t=oO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oO(e){var t=uO(e,"string");return Nn(t)=="symbol"?t:t+""}function uO(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function cO(e,t){if(e==null)return{};var r=sO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function sO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Th={symbolCircle:Gc,symbolCross:jw,symbolDiamond:Cw,symbolSquare:Iw,symbolStar:Rw,symbolTriangle:Lw,symbolWye:Fw},lO=Math.PI/180,fO=function(t){var r="symbol".concat(Ya(t));return Th[r]||Gc},pO=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*lO;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},dO=function(t,r){Th["symbol".concat(Ya(t))]=r},Yc=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=cO(t,iO),s=ll(ll({},c),{},{type:n,size:a,sizeType:u}),f=function(){var v=fO(n),x=zw().type(v).size(pO(a,u,n));return x()},l=s.className,p=s.cx,d=s.cy,y=Y(s,!0);return p===+p&&d===+d&&a===+a?S.createElement("path",gu({},y,{className:re("recharts-symbols",l),transform:"translate(".concat(p,", ").concat(d,")"),d:f()})):null};Yc.registerSymbol=dO;function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function bu(){return bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bu.apply(this,arguments)}function fl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hO(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fl(Object(r),!0).forEach(function(n){Rn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function yO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,jh(n.key),n)}}function mO(e,t,r){return t&&yO(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function gO(e,t,r){return t=Yi(t),bO(e,Eh()?Reflect.construct(t,r||[],Yi(e).constructor):t.apply(e,r))}function bO(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xO(e)}function xO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Eh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Eh=function(){return!!e})()}function Yi(e){return Yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Yi(e)}function wO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xu(e,t)}function xu(e,t){return xu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xu(e,t)}function Rn(e,t,r){return t=jh(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jh(e){var t=OO(e,"string");return Er(t)=="symbol"?t:t+""}function OO(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ve=32,Xc=function(e){function t(){return vO(this,t),gO(this,t,arguments)}return wO(t,e),mO(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ve/2,o=Ve/6,u=Ve/3,c=n.inactive?i:n.color;if(n.type==="plainline")return S.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ve,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return S.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ve,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return S.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ve/8,"h").concat(Ve,"v").concat(Ve*3/4,"h").concat(-Ve,"z"),className:"recharts-legend-icon"});if(S.isValidElement(n.legendIcon)){var s=hO({},n);return delete s.legendIcon,S.cloneElement(n.legendIcon,s)}return S.createElement(Yc,{fill:c,cx:a,cy:a,size:Ve,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Ve,height:Ve},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(d,y){var h=d.formatter||c,v=re(Rn(Rn({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",d.inactive));if(d.type==="none")return null;var x=Z(d.value)?null:d.value;at(!Z(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var b=d.inactive?s:d.color;return S.createElement("li",bu({className:v,style:l,key:"legend-item-".concat(y)},ar(n.props,d,y)),S.createElement(lu,{width:o,height:o,viewBox:f,style:p},n.renderIcon(d)),S.createElement("span",{className:"recharts-legend-item-text",style:{color:b}},h?h(x,d,y):x))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return S.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);Rn(Xc,"displayName","Legend");Rn(Xc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var AO=Ha;function SO(){this.__data__=new AO,this.size=0}var PO=SO;function _O(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var $O=_O;function TO(e){return this.__data__.get(e)}var EO=TO;function jO(e){return this.__data__.has(e)}var MO=jO,CO=Ha,IO=Lc,kO=Bc,DO=200;function NO(e,t){var r=this.__data__;if(r instanceof CO){var n=r.__data__;if(!IO||n.length<DO-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new kO(n)}return r.set(e,t),this.size=r.size,this}var RO=NO,LO=Ha,BO=PO,FO=$O,zO=EO,WO=MO,UO=RO;function sn(e){var t=this.__data__=new LO(e);this.size=t.size}sn.prototype.clear=BO;sn.prototype.delete=FO;sn.prototype.get=zO;sn.prototype.has=WO;sn.prototype.set=UO;var Mh=sn,qO="__lodash_hash_undefined__";function HO(e){return this.__data__.set(e,qO),this}var KO=HO;function GO(e){return this.__data__.has(e)}var YO=GO,XO=Bc,VO=KO,ZO=YO;function Xi(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new XO;++t<r;)this.add(e[t])}Xi.prototype.add=Xi.prototype.push=VO;Xi.prototype.has=ZO;var Ch=Xi;function JO(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Ih=JO;function QO(e,t){return e.has(t)}var kh=QO,e1=Ch,t1=Ih,r1=kh,n1=1,i1=2;function a1(e,t,r,n,i,a){var o=r&n1,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var s=a.get(e),f=a.get(t);if(s&&f)return s==t&&f==e;var l=-1,p=!0,d=r&i1?new e1:void 0;for(a.set(e,t),a.set(t,e);++l<u;){var y=e[l],h=t[l];if(n)var v=o?n(h,y,l,t,e,a):n(y,h,l,e,t,a);if(v!==void 0){if(v)continue;p=!1;break}if(d){if(!t1(t,function(x,b){if(!r1(d,b)&&(y===x||i(y,x,r,n,a)))return d.push(b)})){p=!1;break}}else if(!(y===h||i(y,h,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Dh=a1,o1=ht,u1=o1.Uint8Array,c1=u1;function s1(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var l1=s1;function f1(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var Vc=f1,pl=yi,dl=c1,p1=Rc,d1=Dh,h1=l1,v1=Vc,y1=1,m1=2,g1="[object Boolean]",b1="[object Date]",x1="[object Error]",w1="[object Map]",O1="[object Number]",A1="[object RegExp]",S1="[object Set]",P1="[object String]",_1="[object Symbol]",$1="[object ArrayBuffer]",T1="[object DataView]",hl=pl?pl.prototype:void 0,No=hl?hl.valueOf:void 0;function E1(e,t,r,n,i,a,o){switch(r){case T1:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case $1:return!(e.byteLength!=t.byteLength||!a(new dl(e),new dl(t)));case g1:case b1:case O1:return p1(+e,+t);case x1:return e.name==t.name&&e.message==t.message;case A1:case P1:return e==t+"";case w1:var u=h1;case S1:var c=n&y1;if(u||(u=v1),e.size!=t.size&&!c)return!1;var s=o.get(e);if(s)return s==t;n|=m1,o.set(e,t);var f=d1(u(e),u(t),n,i,a,o);return o.delete(e),f;case _1:if(No)return No.call(e)==No.call(t)}return!1}var j1=E1;function M1(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var Nh=M1,C1=Nh,I1=We;function k1(e,t,r){var n=t(e);return I1(e)?n:C1(n,r(e))}var D1=k1;function N1(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var R1=N1;function L1(){return[]}var B1=L1,F1=R1,z1=B1,W1=Object.prototype,U1=W1.propertyIsEnumerable,vl=Object.getOwnPropertySymbols,q1=vl?function(e){return e==null?[]:(e=Object(e),F1(vl(e),function(t){return U1.call(e,t)}))}:z1,H1=q1;function K1(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var G1=K1,Y1=jt,X1=Mt,V1="[object Arguments]";function Z1(e){return X1(e)&&Y1(e)==V1}var J1=Z1,yl=J1,Q1=Mt,Rh=Object.prototype,eA=Rh.hasOwnProperty,tA=Rh.propertyIsEnumerable,rA=yl(function(){return arguments}())?yl:function(e){return Q1(e)&&eA.call(e,"callee")&&!tA.call(e,"callee")},Zc=rA,Vi={exports:{}};function nA(){return!1}var iA=nA;Vi.exports;(function(e,t){var r=ht,n=iA,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(Vi,Vi.exports);var Lh=Vi.exports,aA=9007199254740991,oA=/^(?:0|[1-9]\d*)$/;function uA(e,t){var r=typeof e;return t=t??aA,!!t&&(r=="number"||r!="symbol"&&oA.test(e))&&e>-1&&e%1==0&&e<t}var Jc=uA,cA=9007199254740991;function sA(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=cA}var Qc=sA,lA=jt,fA=Qc,pA=Mt,dA="[object Arguments]",hA="[object Array]",vA="[object Boolean]",yA="[object Date]",mA="[object Error]",gA="[object Function]",bA="[object Map]",xA="[object Number]",wA="[object Object]",OA="[object RegExp]",AA="[object Set]",SA="[object String]",PA="[object WeakMap]",_A="[object ArrayBuffer]",$A="[object DataView]",TA="[object Float32Array]",EA="[object Float64Array]",jA="[object Int8Array]",MA="[object Int16Array]",CA="[object Int32Array]",IA="[object Uint8Array]",kA="[object Uint8ClampedArray]",DA="[object Uint16Array]",NA="[object Uint32Array]",me={};me[TA]=me[EA]=me[jA]=me[MA]=me[CA]=me[IA]=me[kA]=me[DA]=me[NA]=!0;me[dA]=me[hA]=me[_A]=me[vA]=me[$A]=me[yA]=me[mA]=me[gA]=me[bA]=me[xA]=me[wA]=me[OA]=me[AA]=me[SA]=me[PA]=!1;function RA(e){return pA(e)&&fA(e.length)&&!!me[lA(e)]}var LA=RA;function BA(e){return function(t){return e(t)}}var Bh=BA,Zi={exports:{}};Zi.exports;(function(e,t){var r=Gd,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Zi,Zi.exports);var FA=Zi.exports,zA=LA,WA=Bh,ml=FA,gl=ml&&ml.isTypedArray,UA=gl?WA(gl):zA,Fh=UA,qA=G1,HA=Zc,KA=We,GA=Lh,YA=Jc,XA=Fh,VA=Object.prototype,ZA=VA.hasOwnProperty;function JA(e,t){var r=KA(e),n=!r&&HA(e),i=!r&&!n&&GA(e),a=!r&&!n&&!i&&XA(e),o=r||n||i||a,u=o?qA(e.length,String):[],c=u.length;for(var s in e)(t||ZA.call(e,s))&&!(o&&(s=="length"||i&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||YA(s,c)))&&u.push(s);return u}var QA=JA,eS=Object.prototype;function tS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||eS;return e===r}var rS=tS;function nS(e,t){return function(r){return e(t(r))}}var zh=nS,iS=zh,aS=iS(Object.keys,Object),oS=aS,uS=rS,cS=oS,sS=Object.prototype,lS=sS.hasOwnProperty;function fS(e){if(!uS(e))return cS(e);var t=[];for(var r in Object(e))lS.call(e,r)&&r!="constructor"&&t.push(r);return t}var pS=fS,dS=Nc,hS=Qc;function vS(e){return e!=null&&hS(e.length)&&!dS(e)}var gi=vS,yS=QA,mS=pS,gS=gi;function bS(e){return gS(e)?yS(e):mS(e)}var Ja=bS,xS=D1,wS=H1,OS=Ja;function AS(e){return xS(e,OS,wS)}var SS=AS,bl=SS,PS=1,_S=Object.prototype,$S=_S.hasOwnProperty;function TS(e,t,r,n,i,a){var o=r&PS,u=bl(e),c=u.length,s=bl(t),f=s.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var p=u[l];if(!(o?p in t:$S.call(t,p)))return!1}var d=a.get(e),y=a.get(t);if(d&&y)return d==t&&y==e;var h=!0;a.set(e,t),a.set(t,e);for(var v=o;++l<c;){p=u[l];var x=e[p],b=t[p];if(n)var w=o?n(b,x,p,t,e,a):n(x,b,p,e,t,a);if(!(w===void 0?x===b||i(x,b,r,n,a):w)){h=!1;break}v||(v=p=="constructor")}if(h&&!v){var g=e.constructor,m=t.constructor;g!=m&&"constructor"in e&&"constructor"in t&&!(typeof g=="function"&&g instanceof g&&typeof m=="function"&&m instanceof m)&&(h=!1)}return a.delete(e),a.delete(t),h}var ES=TS,jS=lr,MS=ht,CS=jS(MS,"DataView"),IS=CS,kS=lr,DS=ht,NS=kS(DS,"Promise"),RS=NS,LS=lr,BS=ht,FS=LS(BS,"Set"),Wh=FS,zS=lr,WS=ht,US=zS(WS,"WeakMap"),qS=US,wu=IS,Ou=Lc,Au=RS,Su=Wh,Pu=qS,Uh=jt,ln=Xd,xl="[object Map]",HS="[object Object]",wl="[object Promise]",Ol="[object Set]",Al="[object WeakMap]",Sl="[object DataView]",KS=ln(wu),GS=ln(Ou),YS=ln(Au),XS=ln(Su),VS=ln(Pu),Xt=Uh;(wu&&Xt(new wu(new ArrayBuffer(1)))!=Sl||Ou&&Xt(new Ou)!=xl||Au&&Xt(Au.resolve())!=wl||Su&&Xt(new Su)!=Ol||Pu&&Xt(new Pu)!=Al)&&(Xt=function(e){var t=Uh(e),r=t==HS?e.constructor:void 0,n=r?ln(r):"";if(n)switch(n){case KS:return Sl;case GS:return xl;case YS:return wl;case XS:return Ol;case VS:return Al}return t});var ZS=Xt,Ro=Mh,JS=Dh,QS=j1,eP=ES,Pl=ZS,_l=We,$l=Lh,tP=Fh,rP=1,Tl="[object Arguments]",El="[object Array]",_i="[object Object]",nP=Object.prototype,jl=nP.hasOwnProperty;function iP(e,t,r,n,i,a){var o=_l(e),u=_l(t),c=o?El:Pl(e),s=u?El:Pl(t);c=c==Tl?_i:c,s=s==Tl?_i:s;var f=c==_i,l=s==_i,p=c==s;if(p&&$l(e)){if(!$l(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new Ro),o||tP(e)?JS(e,t,r,n,i,a):QS(e,t,c,r,n,i,a);if(!(r&rP)){var d=f&&jl.call(e,"__wrapped__"),y=l&&jl.call(t,"__wrapped__");if(d||y){var h=d?e.value():e,v=y?t.value():t;return a||(a=new Ro),i(h,v,r,n,a)}}return p?(a||(a=new Ro),eP(e,t,r,n,i,a)):!1}var aP=iP,oP=aP,Ml=Mt;function qh(e,t,r,n,i){return e===t?!0:e==null||t==null||!Ml(e)&&!Ml(t)?e!==e&&t!==t:oP(e,t,r,n,qh,i)}var es=qh,uP=Mh,cP=es,sP=1,lP=2;function fP(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],s=e[c],f=u[1];if(o&&u[2]){if(s===void 0&&!(c in e))return!1}else{var l=new uP;if(n)var p=n(s,f,c,e,t,l);if(!(p===void 0?cP(f,s,sP|lP,n,l):p))return!1}}return!0}var pP=fP,dP=Bt;function hP(e){return e===e&&!dP(e)}var Hh=hP,vP=Hh,yP=Ja;function mP(e){for(var t=yP(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,vP(i)]}return t}var gP=mP;function bP(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var Kh=bP,xP=pP,wP=gP,OP=Kh;function AP(e){var t=wP(e);return t.length==1&&t[0][2]?OP(t[0][0],t[0][1]):function(r){return r===e||xP(r,e,t)}}var SP=AP;function PP(e,t){return e!=null&&t in Object(e)}var _P=PP,$P=eh,TP=Zc,EP=We,jP=Jc,MP=Qc,CP=Ga;function IP(e,t,r){t=$P(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=CP(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&MP(i)&&jP(o,i)&&(EP(e)||TP(e)))}var kP=IP,DP=_P,NP=kP;function RP(e,t){return e!=null&&NP(e,t,DP)}var LP=RP,BP=es,FP=th,zP=LP,WP=Dc,UP=Hh,qP=Kh,HP=Ga,KP=1,GP=2;function YP(e,t){return WP(e)&&UP(t)?qP(HP(e),t):function(r){var n=FP(r,e);return n===void 0&&n===t?zP(r,e):BP(t,n,KP|GP)}}var XP=YP;function VP(e){return e}var fn=VP;function ZP(e){return function(t){return t==null?void 0:t[e]}}var JP=ZP,QP=Wc;function e_(e){return function(t){return QP(t,e)}}var t_=e_,r_=JP,n_=t_,i_=Dc,a_=Ga;function o_(e){return i_(e)?r_(a_(e)):n_(e)}var u_=o_,c_=SP,s_=XP,l_=fn,f_=We,p_=u_;function d_(e){return typeof e=="function"?e:e==null?l_:typeof e=="object"?f_(e)?s_(e[0],e[1]):c_(e):p_(e)}var vt=d_;function h_(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var Gh=h_;function v_(e){return e!==e}var y_=v_;function m_(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var g_=m_,b_=Gh,x_=y_,w_=g_;function O_(e,t,r){return t===t?w_(e,t,r):b_(e,x_,r)}var A_=O_,S_=A_;function P_(e,t){var r=e==null?0:e.length;return!!r&&S_(e,t,0)>-1}var __=P_;function $_(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var T_=$_;function E_(){}var j_=E_,Lo=Wh,M_=j_,C_=Vc,I_=1/0,k_=Lo&&1/C_(new Lo([,-0]))[1]==I_?function(e){return new Lo(e)}:M_,D_=k_,N_=Ch,R_=__,L_=T_,B_=kh,F_=D_,z_=Vc,W_=200;function U_(e,t,r){var n=-1,i=R_,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=L_;else if(a>=W_){var s=t?null:F_(e);if(s)return z_(s);o=!1,i=B_,c=new N_}else c=t?[]:u;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var p=c.length;p--;)if(c[p]===l)continue e;t&&c.push(l),u.push(f)}else i(c,l,r)||(c!==u&&c.push(l),u.push(f))}return u}var q_=U_,H_=vt,K_=q_;function G_(e,t){return e&&e.length?K_(e,H_(t)):[]}var Y_=G_;const Cl=se(Y_);function Yh(e,t,r){return t===!0?Cl(e,r):Z(t)?Cl(e,t):e}function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}var X_=["ref"];function Il(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Il(Object(r),!0).forEach(function(n){Qa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Il(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function V_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vh(n.key),n)}}function Z_(e,t,r){return t&&kl(e.prototype,t),r&&kl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function J_(e,t,r){return t=Ji(t),Q_(e,Xh()?Reflect.construct(t,r||[],Ji(e).constructor):t.apply(e,r))}function Q_(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return e$(e)}function e$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xh=function(){return!!e})()}function Ji(e){return Ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ji(e)}function t$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_u(e,t)}function _u(e,t){return _u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_u(e,t)}function Qa(e,t,r){return t=Vh(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vh(e){var t=r$(e,"string");return jr(t)=="symbol"?t:t+""}function r$(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function n$(e,t){if(e==null)return{};var r=i$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function a$(e){return e.value}function o$(e,t){if(S.isValidElement(e))return S.cloneElement(e,t);if(typeof e=="function")return S.createElement(e,t);t.ref;var r=n$(t,X_);return S.createElement(Xc,r)}var Dl=1,Sr=function(e){function t(){var r;V_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=J_(this,t,[].concat(i)),Qa(r,"lastBoundingBox",{width:-1,height:-1}),r}return t$(t,e),Z_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Dl||Math.abs(i.height-this.lastBoundingBox.height)>Dl)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?gt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var d=this.getBBoxSnapshot();l={left:((s||0)-d.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();p={top:((f||0)-y.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return gt(gt({},l),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=gt(gt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return S.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(d){n.wrapperNode=d}},o$(a,gt(gt({},this.props),{},{payload:Yh(f,s,a$)})))}}],[{key:"getWithHeight",value:function(n,i){var a=gt(gt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&z(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);Qa(Sr,"displayName","Legend");Qa(Sr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Nl=yi,u$=Zc,c$=We,Rl=Nl?Nl.isConcatSpreadable:void 0;function s$(e){return c$(e)||u$(e)||!!(Rl&&e&&e[Rl])}var l$=s$,f$=Nh,p$=l$;function Zh(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=p$),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?Zh(u,t-1,r,n,i):f$(i,u):n||(i[i.length]=u)}return i}var Jh=Zh;function d$(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var h$=d$,v$=h$,y$=v$(),m$=y$,g$=m$,b$=Ja;function x$(e,t){return e&&g$(e,t,b$)}var Qh=x$,w$=gi;function O$(e,t){return function(r,n){if(r==null)return r;if(!w$(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var A$=O$,S$=Qh,P$=A$,_$=P$(S$),ts=_$,$$=ts,T$=gi;function E$(e,t){var r=-1,n=T$(e)?Array(e.length):[];return $$(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var ev=E$;function j$(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var M$=j$,Ll=tn;function C$(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=Ll(e),o=t!==void 0,u=t===null,c=t===t,s=Ll(t);if(!u&&!s&&!a&&e>t||a&&o&&c&&!u&&!s||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!s&&e<t||s&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var I$=C$,k$=I$;function D$(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=k$(i[n],a[n]);if(c){if(n>=u)return c;var s=r[n];return c*(s=="desc"?-1:1)}}return e.index-t.index}var N$=D$,Bo=zc,R$=Wc,L$=vt,B$=ev,F$=M$,z$=Bh,W$=N$,U$=fn,q$=We;function H$(e,t,r){t.length?t=Bo(t,function(a){return q$(a)?function(o){return R$(o,a.length===1?a[0]:a)}:a}):t=[U$];var n=-1;t=Bo(t,z$(L$));var i=B$(e,function(a,o,u){var c=Bo(t,function(s){return s(a)});return{criteria:c,index:++n,value:a}});return F$(i,function(a,o){return W$(a,o,r)})}var K$=H$;function G$(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var Y$=G$,X$=Y$,Bl=Math.max;function V$(e,t,r){return t=Bl(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=Bl(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),X$(e,this,u)}}var Z$=V$;function J$(e){return function(){return e}}var Q$=J$,eT=lr,tT=function(){try{var e=eT(Object,"defineProperty");return e({},"",{}),e}catch{}}(),tv=tT,rT=Q$,Fl=tv,nT=fn,iT=Fl?function(e,t){return Fl(e,"toString",{configurable:!0,enumerable:!1,value:rT(t),writable:!0})}:nT,aT=iT,oT=800,uT=16,cT=Date.now;function sT(e){var t=0,r=0;return function(){var n=cT(),i=uT-(n-r);if(r=n,i>0){if(++t>=oT)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var lT=sT,fT=aT,pT=lT,dT=pT(fT),hT=dT,vT=fn,yT=Z$,mT=hT;function gT(e,t){return mT(yT(e,t,vT),e+"")}var bT=gT,xT=Rc,wT=gi,OT=Jc,AT=Bt;function ST(e,t,r){if(!AT(r))return!1;var n=typeof t;return(n=="number"?wT(r)&&OT(t,r.length):n=="string"&&t in r)?xT(r[t],e):!1}var eo=ST,PT=Jh,_T=K$,$T=bT,zl=eo,TT=$T(function(e,t){if(e==null)return[];var r=t.length;return r>1&&zl(e,t[0],t[1])?t=[]:r>2&&zl(t[0],t[1],t[2])&&(t=[t[0]]),_T(e,PT(t,1),[])}),ET=TT;const rs=se(ET);function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function $u(){return $u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$u.apply(this,arguments)}function jT(e,t){return kT(e)||IT(e,t)||CT(e,t)||MT()}function MT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function CT(e,t){if(e){if(typeof e=="string")return Wl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wl(e,t)}}function Wl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function IT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function kT(e){if(Array.isArray(e))return e}function Ul(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ul(Object(r),!0).forEach(function(n){DT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ul(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DT(e,t,r){return t=NT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function NT(e){var t=RT(e,"string");return Ln(t)=="symbol"?t:t+""}function RT(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LT(e){return Array.isArray(e)&&$e(e[0])&&$e(e[1])?e.join(" ~ "):e}var BT=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,p=t.itemSorter,d=t.wrapperClassName,y=t.labelClassName,h=t.label,v=t.labelFormatter,x=t.accessibilityLayer,b=x===void 0?!1:x,w=function(){if(f&&f.length){var C={padding:0,margin:0},_=(p?rs(f,p):f).map(function($,k){if($.type==="none")return null;var I=Fo({display:"block",paddingTop:4,paddingBottom:4,color:$.color||"#000"},u),D=$.formatter||l||LT,R=$.value,L=$.name,U=R,B=L;if(D&&U!=null&&B!=null){var F=D(R,L,$,k,f);if(Array.isArray(F)){var H=jT(F,2);U=H[0],B=H[1]}else U=F}return S.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(k),style:I},$e(B)?S.createElement("span",{className:"recharts-tooltip-item-name"},B):null,$e(B)?S.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,S.createElement("span",{className:"recharts-tooltip-item-value"},U),S.createElement("span",{className:"recharts-tooltip-item-unit"},$.unit||""))});return S.createElement("ul",{className:"recharts-tooltip-item-list",style:C},_)}return null},g=Fo({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Fo({margin:0},s),O=!ee(h),A=O?h:"",P=re("recharts-default-tooltip",d),j=re("recharts-tooltip-label",y);O&&v&&f!==void 0&&f!==null&&(A=v(h,f));var M=b?{role:"status","aria-live":"assertive"}:{};return S.createElement("div",$u({className:P,style:g},M),S.createElement("p",{className:j,style:m},S.isValidElement(A)?A:"".concat(A)),w())};function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function $i(e,t,r){return t=FT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FT(e){var t=zT(e,"string");return Bn(t)=="symbol"?t:t+""}function zT(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var bn="recharts-tooltip-wrapper",WT={visibility:"hidden"};function UT(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return re(bn,$i($i($i($i({},"".concat(bn,"-right"),z(r)&&t&&z(t.x)&&r>=t.x),"".concat(bn,"-left"),z(r)&&t&&z(t.x)&&r<t.x),"".concat(bn,"-bottom"),z(n)&&t&&z(t.y)&&n>=t.y),"".concat(bn,"-top"),z(n)&&t&&z(t.y)&&n<t.y))}function ql(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&z(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var p=f,d=c[n];return p<d?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,h=c[n]+s;return y>h?Math.max(f,c[n]):Math.max(l,c[n])}function qT(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function HT(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=ql({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=ql({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=qT({translateX:f,translateY:l,useTranslate3d:u})):s=WT,{cssProperties:s,cssClasses:UT({translateX:f,translateY:l,coordinate:r})}}function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function Hl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hl(Object(r),!0).forEach(function(n){Eu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function GT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nv(n.key),n)}}function YT(e,t,r){return t&&GT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function XT(e,t,r){return t=Qi(t),VT(e,rv()?Reflect.construct(t,r||[],Qi(e).constructor):t.apply(e,r))}function VT(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ZT(e)}function ZT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rv=function(){return!!e})()}function Qi(e){return Qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Qi(e)}function JT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tu(e,t)}function Tu(e,t){return Tu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tu(e,t)}function Eu(e,t,r){return t=nv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nv(e){var t=QT(e,"string");return Mr(t)=="symbol"?t:t+""}function QT(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Gl=1,eE=function(e){function t(){var r;KT(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=XT(this,t,[].concat(i)),Eu(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Eu(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return JT(t,e),YT(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Gl||Math.abs(n.height-this.state.lastBoundingBox.height)>Gl)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,p=i.isAnimationActive,d=i.offset,y=i.position,h=i.reverseDirection,v=i.useTranslate3d,x=i.viewBox,b=i.wrapperStyle,w=HT({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:d,position:y,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:v,viewBox:x}),g=w.cssClasses,m=w.cssProperties,O=Kl(Kl({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},b);return S.createElement("div",{tabIndex:-1,className:g,style:O,ref:function(P){n.wrapperNode=P}},s)}}])}(q.PureComponent),tE=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},st={isSsr:tE(),get:function(t){return st[t]},set:function(t,r){if(typeof t=="string")st[t]=r;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(i){st[i]=t[i]})}}};function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Yl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yl(Object(r),!0).forEach(function(n){ns(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nE(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,av(n.key),n)}}function iE(e,t,r){return t&&nE(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function aE(e,t,r){return t=ea(t),oE(e,iv()?Reflect.construct(t,r||[],ea(e).constructor):t.apply(e,r))}function oE(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return uE(e)}function uE(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function iv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(iv=function(){return!!e})()}function ea(e){return ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ea(e)}function cE(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ju(e,t)}function ju(e,t){return ju=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ju(e,t)}function ns(e,t,r){return t=av(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function av(e){var t=sE(e,"string");return Cr(t)=="symbol"?t:t+""}function sE(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function lE(e){return e.dataKey}function fE(e,t){return S.isValidElement(e)?S.cloneElement(e,t):typeof e=="function"?S.createElement(e,t):S.createElement(BT,t)}var bt=function(e){function t(){return rE(this,t),aE(this,t,arguments)}return cE(t,e),iE(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,p=i.isAnimationActive,d=i.offset,y=i.payload,h=i.payloadUniqBy,v=i.position,x=i.reverseDirection,b=i.useTranslate3d,w=i.viewBox,g=i.wrapperStyle,m=y??[];l&&m.length&&(m=Yh(y.filter(function(A){return A.value!=null&&(A.hide!==!0||n.props.includeHidden)}),h,lE));var O=m.length>0;return S.createElement(eE,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:O,offset:d,position:v,reverseDirection:x,useTranslate3d:b,viewBox:w,wrapperStyle:g},fE(s,Xl(Xl({},this.props),{},{payload:m})))}}])}(q.PureComponent);ns(bt,"displayName","Tooltip");ns(bt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!st.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var pE=ht,dE=function(){return pE.Date.now()},hE=dE,vE=/\s/;function yE(e){for(var t=e.length;t--&&vE.test(e.charAt(t)););return t}var mE=yE,gE=mE,bE=/^\s+/;function xE(e){return e&&e.slice(0,gE(e)+1).replace(bE,"")}var wE=xE,OE=wE,Vl=Bt,AE=tn,Zl=NaN,SE=/^[-+]0x[0-9a-f]+$/i,PE=/^0b[01]+$/i,_E=/^0o[0-7]+$/i,$E=parseInt;function TE(e){if(typeof e=="number")return e;if(AE(e))return Zl;if(Vl(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Vl(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=OE(e);var r=PE.test(e);return r||_E.test(e)?$E(e.slice(2),r?2:8):SE.test(e)?Zl:+e}var ov=TE,EE=Bt,zo=hE,Jl=ov,jE="Expected a function",ME=Math.max,CE=Math.min;function IE(e,t,r){var n,i,a,o,u,c,s=0,f=!1,l=!1,p=!0;if(typeof e!="function")throw new TypeError(jE);t=Jl(t)||0,EE(r)&&(f=!!r.leading,l="maxWait"in r,a=l?ME(Jl(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function d(O){var A=n,P=i;return n=i=void 0,s=O,o=e.apply(P,A),o}function y(O){return s=O,u=setTimeout(x,t),f?d(O):o}function h(O){var A=O-c,P=O-s,j=t-A;return l?CE(j,a-P):j}function v(O){var A=O-c,P=O-s;return c===void 0||A>=t||A<0||l&&P>=a}function x(){var O=zo();if(v(O))return b(O);u=setTimeout(x,h(O))}function b(O){return u=void 0,p&&n?d(O):(n=i=void 0,o)}function w(){u!==void 0&&clearTimeout(u),s=0,n=c=i=u=void 0}function g(){return u===void 0?o:b(zo())}function m(){var O=zo(),A=v(O);if(n=arguments,i=this,c=O,A){if(u===void 0)return y(c);if(l)return clearTimeout(u),u=setTimeout(x,t),d(c)}return u===void 0&&(u=setTimeout(x,t)),o}return m.cancel=w,m.flush=g,m}var kE=IE,DE=kE,NE=Bt,RE="Expected a function";function LE(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(RE);return NE(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),DE(e,t,{leading:n,maxWait:t,trailing:i})}var BE=LE;const uv=se(BE);function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Ql(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ti(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ql(Object(r),!0).forEach(function(n){FE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ql(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FE(e,t,r){return t=zE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zE(e){var t=WE(e,"string");return Fn(t)=="symbol"?t:t+""}function WE(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function UE(e,t){return GE(e)||KE(e,t)||HE(e,t)||qE()}function qE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HE(e,t){if(e){if(typeof e=="string")return ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ef(e,t)}}function ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function KE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function GE(e){if(Array.isArray(e))return e}var fW=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,p=e.maxHeight,d=e.children,y=e.debounce,h=y===void 0?0:y,v=e.id,x=e.className,b=e.onResize,w=e.style,g=w===void 0?{}:w,m=q.useRef(null),O=q.useRef();O.current=b,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var A=q.useState({containerWidth:i.width,containerHeight:i.height}),P=UE(A,2),j=P[0],M=P[1],T=q.useCallback(function(_,$){M(function(k){var I=Math.round(_),D=Math.round($);return k.containerWidth===I&&k.containerHeight===D?k:{containerWidth:I,containerHeight:D}})},[]);q.useEffect(function(){var _=function(L){var U,B=L[0].contentRect,F=B.width,H=B.height;T(F,H),(U=O.current)===null||U===void 0||U.call(O,F,H)};h>0&&(_=uv(_,h,{trailing:!0,leading:!1}));var $=new ResizeObserver(_),k=m.current.getBoundingClientRect(),I=k.width,D=k.height;return T(I,D),$.observe(m.current),function(){$.disconnect()}},[T,h]);var C=q.useMemo(function(){var _=j.containerWidth,$=j.containerHeight;if(_<0||$<0)return null;at(Jt(o)||Jt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),at(!r||r>0,"The aspect(%s) must be greater than zero.",r);var k=Jt(o)?_:o,I=Jt(c)?$:c;r&&r>0&&(k?I=k/r:I&&(k=I*r),p&&I>p&&(I=p)),at(k>0||I>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,k,I,o,c,f,l,r);var D=!Array.isArray(d)&&St(d.type).endsWith("Chart");return S.Children.map(d,function(R){return nh.isElement(R)?q.cloneElement(R,Ti({width:k,height:I},D?{style:Ti({height:"100%",width:"100%",maxHeight:I,maxWidth:k},R.props.style)}:{})):R})},[r,d,c,p,l,f,j,o]);return S.createElement("div",{id:v?"".concat(v):void 0,className:re("recharts-responsive-container",x),style:Ti(Ti({},g),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:p}),ref:m},C)}),is=function(t){return null};is.displayName="Cell";function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tf(Object(r),!0).forEach(function(n){YE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YE(e,t,r){return t=XE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XE(e){var t=VE(e,"string");return zn(t)=="symbol"?t:t+""}function VE(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hr={widthCache:{},cacheCount:0},ZE=2e3,JE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},rf="recharts_measurement_span";function QE(e){var t=Mu({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var En=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||st.isSsr)return{width:0,height:0};var n=QE(r),i=JSON.stringify({text:t,copyStyle:n});if(hr.widthCache[i])return hr.widthCache[i];try{var a=document.getElementById(rf);a||(a=document.createElement("span"),a.setAttribute("id",rf),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Mu(Mu({},JE),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return hr.widthCache[i]=c,++hr.cacheCount>ZE&&(hr.cacheCount=0,hr.widthCache={}),c}catch{return{width:0,height:0}}},ej=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function ta(e,t){return ij(e)||nj(e,t)||rj(e,t)||tj()}function tj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rj(e,t){if(e){if(typeof e=="string")return nf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nf(e,t)}}function nf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function nj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function ij(e){if(Array.isArray(e))return e}function aj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function af(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uj(n.key),n)}}function oj(e,t,r){return t&&af(e.prototype,t),r&&af(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function uj(e){var t=cj(e,"string");return Wn(t)=="symbol"?t:t+""}function cj(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var of=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,uf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,sj=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,lj=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,cv={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},fj=Object.keys(cv),br="NaN";function pj(e,t){return e*cv[t]}var Ei=function(){function e(t,r){aj(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!sj.test(r)&&(this.num=NaN,this.unit=""),fj.includes(r)&&(this.num=pj(t,r),this.unit="px")}return oj(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=lj.exec(r))!==null&&n!==void 0?n:[],a=ta(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function sv(e){if(e.includes(br))return br;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=of.exec(t))!==null&&r!==void 0?r:[],i=ta(n,4),a=i[1],o=i[2],u=i[3],c=Ei.parse(a??""),s=Ei.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return br;t=t.replace(of,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,p=(l=uf.exec(t))!==null&&l!==void 0?l:[],d=ta(p,4),y=d[1],h=d[2],v=d[3],x=Ei.parse(y??""),b=Ei.parse(v??""),w=h==="+"?x.add(b):x.subtract(b);if(w.isNaN())return br;t=t.replace(uf,w.toString())}return t}var cf=/\(([^()]*)\)/;function dj(e){for(var t=e;t.includes("(");){var r=cf.exec(t),n=ta(r,2),i=n[1];t=t.replace(cf,sv(i))}return t}function hj(e){var t=e.replace(/\s+/g,"");return t=dj(t),t=sv(t),t}function vj(e){try{return hj(e)}catch{return br}}function Wo(e){var t=vj(e.slice(5,-1));return t===br?"":t}var yj=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],mj=["dx","dy","angle","className","breakAll"];function Cu(){return Cu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cu.apply(this,arguments)}function sf(e,t){if(e==null)return{};var r=gj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function gj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function lf(e,t){return Oj(e)||wj(e,t)||xj(e,t)||bj()}function bj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xj(e,t){if(e){if(typeof e=="string")return ff(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ff(e,t)}}function ff(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function wj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Oj(e){if(Array.isArray(e))return e}var lv=/[ \f\n\r\t\v\u2028\u2029]+/,fv=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];ee(r)||(n?a=r.toString().split(""):a=r.toString().split(lv));var o=a.map(function(c){return{word:c,width:En(c,i).width}}),u=n?0:En(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},Aj=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=z(o),l=u,p=function(){var k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return k.reduce(function(I,D){var R=D.word,L=D.width,U=I[I.length-1];if(U&&(i==null||a||U.width+L+n<Number(i)))U.words.push(R),U.width+=L+n;else{var B={words:[R],width:L};I.push(B)}return I},[])},d=p(r),y=function(k){return k.reduce(function(I,D){return I.width>D.width?I:D})};if(!f)return d;for(var h="…",v=function(k){var I=l.slice(0,k),D=fv({breakAll:s,style:c,children:I+h}).wordsWithComputedWidth,R=p(D),L=R.length>o||y(R).width>Number(i);return[L,R]},x=0,b=l.length-1,w=0,g;x<=b&&w<=l.length-1;){var m=Math.floor((x+b)/2),O=m-1,A=v(O),P=lf(A,2),j=P[0],M=P[1],T=v(m),C=lf(T,1),_=C[0];if(!j&&!_&&(x=m+1),j&&_&&(b=m-1),!j&&_){g=M;break}w++}return g||d},pf=function(t){var r=ee(t)?[]:t.toString().split(lv);return[{words:r}]},Sj=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!st.isSsr){var c,s,f=fv({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,p=f.spaceWidth;c=l,s=p}else return pf(i);return Aj({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return pf(i)},df="#808080",or=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,p=t.textAnchor,d=p===void 0?"start":p,y=t.verticalAnchor,h=y===void 0?"end":y,v=t.fill,x=v===void 0?df:v,b=sf(t,yj),w=q.useMemo(function(){return Sj({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:l,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,l,b.style,b.width]),g=b.dx,m=b.dy,O=b.angle,A=b.className,P=b.breakAll,j=sf(b,mj);if(!$e(n)||!$e(a))return null;var M=n+(z(g)?g:0),T=a+(z(m)?m:0),C;switch(h){case"start":C=Wo("calc(".concat(s,")"));break;case"middle":C=Wo("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:C=Wo("calc(".concat(w.length-1," * -").concat(u,")"));break}var _=[];if(l){var $=w[0].width,k=b.width;_.push("scale(".concat((z(k)?k/$:1)/$,")"))}return O&&_.push("rotate(".concat(O,", ").concat(M,", ").concat(T,")")),_.length&&(j.transform=_.join(" ")),S.createElement("text",Cu({},Y(j,!0),{x:M,y:T,className:re("recharts-text",A),textAnchor:d,fill:x.includes("url")?df:x}),w.map(function(I,D){var R=I.words.join(P?"":" ");return S.createElement("tspan",{x:M,dy:D===0?C:u,key:"".concat(R,"-").concat(D)},R)}))};function Lt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Pj(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function as(e){let t,r,n;e.length!==2?(t=Lt,r=(u,c)=>Lt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Lt||e===Pj?e:_j,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function _j(){return 0}function pv(e){return e===null?NaN:+e}function*$j(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const Tj=as(Lt),bi=Tj.right;as(pv).center;class hf extends Map{constructor(t,r=Mj){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(vf(this,t))}has(t){return super.has(vf(this,t))}set(t,r){return super.set(Ej(this,t),r)}delete(t){return super.delete(jj(this,t))}}function vf({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function Ej({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function jj({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function Mj(e){return e!==null&&typeof e=="object"?e.valueOf():e}function Cj(e=Lt){if(e===Lt)return dv;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function dv(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const Ij=Math.sqrt(50),kj=Math.sqrt(10),Dj=Math.sqrt(2);function ra(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=Ij?10:a>=kj?5:a>=Dj?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?ra(e,t,r*2):[u,c,s]}function Iu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?ra(t,e,r):ra(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function ku(e,t,r){return t=+t,e=+e,r=+r,ra(e,t,r)[2]}function Du(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?ku(t,e,r):ku(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function yf(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function mf(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function hv(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?dv:Cj(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),d=Math.max(r,Math.floor(t-s*l/c+p)),y=Math.min(n,Math.floor(t+(c-s)*l/c+p));hv(e,t,d,y,i)}const a=e[t];let o=r,u=n;for(xn(e,r,t),i(e[n],a)>0&&xn(e,r,n);o<u;){for(xn(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?xn(e,r,u):(++u,xn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function xn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Nj(e,t,r){if(e=Float64Array.from($j(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return mf(e);if(t>=1)return yf(e);var n,i=(n-1)*t,a=Math.floor(i),o=yf(hv(e,a).subarray(0,a+1)),u=mf(e.subarray(a+1));return o+(u-o)*(i-a)}}function Rj(e,t,r=pv){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function Lj(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function tt(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Ct(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Nu=Symbol("implicit");function os(){var e=new hf,t=[],r=[],n=Nu;function i(a){let o=e.get(a);if(o===void 0){if(n!==Nu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new hf;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return os(t,r).unknown(n)},tt.apply(i,arguments),i}function Un(){var e=os().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var p=t().length,d=i<n,y=d?i:n,h=d?n:i;a=(h-y)/Math.max(1,p-c+s*2),u&&(a=Math.floor(a)),y+=(h-y-a*(p-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var v=Lj(p).map(function(x){return y+a*x});return r(d?v.reverse():v)}return e.domain=function(p){return arguments.length?(t(p),l()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,l()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,s=+p),l()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),l()):c},e.paddingOuter=function(p){return arguments.length?(s=+p,l()):s},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),l()):f},e.copy=function(){return Un(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},tt.apply(l(),arguments)}function vv(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return vv(t())},e}function jn(){return vv(Un.apply(null,arguments).paddingInner(1))}function us(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function yv(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function xi(){}var qn=.7,na=1/qn,Pr="\\s*([+-]?\\d+)\\s*",Hn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Bj=/^#([0-9a-f]{3,8})$/,Fj=new RegExp(`^rgb\\(${Pr},${Pr},${Pr}\\)$`),zj=new RegExp(`^rgb\\(${lt},${lt},${lt}\\)$`),Wj=new RegExp(`^rgba\\(${Pr},${Pr},${Pr},${Hn}\\)$`),Uj=new RegExp(`^rgba\\(${lt},${lt},${lt},${Hn}\\)$`),qj=new RegExp(`^hsl\\(${Hn},${lt},${lt}\\)$`),Hj=new RegExp(`^hsla\\(${Hn},${lt},${lt},${Hn}\\)$`),gf={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};us(xi,Kn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:bf,formatHex:bf,formatHex8:Kj,formatHsl:Gj,formatRgb:xf,toString:xf});function bf(){return this.rgb().formatHex()}function Kj(){return this.rgb().formatHex8()}function Gj(){return mv(this).formatHsl()}function xf(){return this.rgb().formatRgb()}function Kn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Bj.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?wf(t):r===3?new ze(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?ji(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?ji(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Fj.exec(e))?new ze(t[1],t[2],t[3],1):(t=zj.exec(e))?new ze(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Wj.exec(e))?ji(t[1],t[2],t[3],t[4]):(t=Uj.exec(e))?ji(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=qj.exec(e))?Sf(t[1],t[2]/100,t[3]/100,1):(t=Hj.exec(e))?Sf(t[1],t[2]/100,t[3]/100,t[4]):gf.hasOwnProperty(e)?wf(gf[e]):e==="transparent"?new ze(NaN,NaN,NaN,0):null}function wf(e){return new ze(e>>16&255,e>>8&255,e&255,1)}function ji(e,t,r,n){return n<=0&&(e=t=r=NaN),new ze(e,t,r,n)}function Yj(e){return e instanceof xi||(e=Kn(e)),e?(e=e.rgb(),new ze(e.r,e.g,e.b,e.opacity)):new ze}function Ru(e,t,r,n){return arguments.length===1?Yj(e):new ze(e,t,r,n??1)}function ze(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}us(ze,Ru,yv(xi,{brighter(e){return e=e==null?na:Math.pow(na,e),new ze(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?qn:Math.pow(qn,e),new ze(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ze(nr(this.r),nr(this.g),nr(this.b),ia(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Of,formatHex:Of,formatHex8:Xj,formatRgb:Af,toString:Af}));function Of(){return`#${Qt(this.r)}${Qt(this.g)}${Qt(this.b)}`}function Xj(){return`#${Qt(this.r)}${Qt(this.g)}${Qt(this.b)}${Qt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Af(){const e=ia(this.opacity);return`${e===1?"rgb(":"rgba("}${nr(this.r)}, ${nr(this.g)}, ${nr(this.b)}${e===1?")":`, ${e})`}`}function ia(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function nr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Qt(e){return e=nr(e),(e<16?"0":"")+e.toString(16)}function Sf(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new it(e,t,r,n)}function mv(e){if(e instanceof it)return new it(e.h,e.s,e.l,e.opacity);if(e instanceof xi||(e=Kn(e)),!e)return new it;if(e instanceof it)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new it(o,u,c,e.opacity)}function Vj(e,t,r,n){return arguments.length===1?mv(e):new it(e,t,r,n??1)}function it(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}us(it,Vj,yv(xi,{brighter(e){return e=e==null?na:Math.pow(na,e),new it(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?qn:Math.pow(qn,e),new it(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new ze(Uo(e>=240?e-240:e+120,i,n),Uo(e,i,n),Uo(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new it(Pf(this.h),Mi(this.s),Mi(this.l),ia(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ia(this.opacity);return`${e===1?"hsl(":"hsla("}${Pf(this.h)}, ${Mi(this.s)*100}%, ${Mi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Pf(e){return e=(e||0)%360,e<0?e+360:e}function Mi(e){return Math.max(0,Math.min(1,e||0))}function Uo(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const cs=e=>()=>e;function Zj(e,t){return function(r){return e+r*t}}function Jj(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function Qj(e){return(e=+e)==1?gv:function(t,r){return r-t?Jj(t,r,e):cs(isNaN(t)?r:t)}}function gv(e,t){var r=t-e;return r?Zj(e,r):cs(isNaN(e)?t:e)}const _f=function e(t){var r=Qj(t);function n(i,a){var o=r((i=Ru(i)).r,(a=Ru(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=gv(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function eM(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function tM(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function rM(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=pn(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function nM(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function aa(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function iM(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=pn(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Lu=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,qo=new RegExp(Lu.source,"g");function aM(e){return function(){return e}}function oM(e){return function(t){return e(t)+""}}function uM(e,t){var r=Lu.lastIndex=qo.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Lu.exec(e))&&(i=qo.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:aa(n,i)})),r=qo.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?oM(c[0].x):aM(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function pn(e,t){var r=typeof t,n;return t==null||r==="boolean"?cs(t):(r==="number"?aa:r==="string"?(n=Kn(t))?(t=n,_f):uM:t instanceof Kn?_f:t instanceof Date?nM:tM(t)?eM:Array.isArray(t)?rM:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?iM:aa)(e,t)}function ss(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function cM(e,t){t===void 0&&(t=e,e=pn);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function sM(e){return function(){return e}}function oa(e){return+e}var $f=[0,1];function Be(e){return e}function Bu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:sM(isNaN(t)?NaN:.5)}function lM(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function fM(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Bu(i,n),a=r(o,a)):(n=Bu(n,i),a=r(a,o)),function(u){return a(n(u))}}function pM(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Bu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=bi(e,u,1,n)-1;return a[c](i[c](u))}}function wi(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function to(){var e=$f,t=$f,r=pn,n,i,a,o=Be,u,c,s;function f(){var p=Math.min(e.length,t.length);return o!==Be&&(o=lM(e[0],e[p-1])),u=p>2?pM:fM,c=s=null,l}function l(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return l.invert=function(p){return o(i((s||(s=u(t,e.map(n),aa)))(p)))},l.domain=function(p){return arguments.length?(e=Array.from(p,oa),f()):e.slice()},l.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},l.rangeRound=function(p){return t=Array.from(p),r=ss,f()},l.clamp=function(p){return arguments.length?(o=p?!0:Be,f()):o!==Be},l.interpolate=function(p){return arguments.length?(r=p,f()):r},l.unknown=function(p){return arguments.length?(a=p,l):a},function(p,d){return n=p,i=d,f()}}function ls(){return to()(Be,Be)}function dM(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function ua(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Ir(e){return e=ua(Math.abs(e)),e?e[1]:NaN}function hM(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function vM(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var yM=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Gn(e){if(!(t=yM.exec(e)))throw new Error("invalid format: "+e);var t;return new fs({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Gn.prototype=fs.prototype;function fs(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}fs.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function mM(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var bv;function gM(e,t){var r=ua(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(bv=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+ua(e,Math.max(0,t+a-1))[0]}function Tf(e,t){var r=ua(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Ef={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:dM,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Tf(e*100,t),r:Tf,s:gM,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function jf(e){return e}var Mf=Array.prototype.map,Cf=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function bM(e){var t=e.grouping===void 0||e.thousands===void 0?jf:hM(Mf.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?jf:vM(Mf.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=Gn(l);var p=l.fill,d=l.align,y=l.sign,h=l.symbol,v=l.zero,x=l.width,b=l.comma,w=l.precision,g=l.trim,m=l.type;m==="n"?(b=!0,m="g"):Ef[m]||(w===void 0&&(w=12),g=!0,m="g"),(v||p==="0"&&d==="=")&&(v=!0,p="0",d="=");var O=h==="$"?r:h==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",A=h==="$"?n:/[%p]/.test(m)?o:"",P=Ef[m],j=/[defgprs%]/.test(m);w=w===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function M(T){var C=O,_=A,$,k,I;if(m==="c")_=P(T)+_,T="";else{T=+T;var D=T<0||1/T<0;if(T=isNaN(T)?c:P(Math.abs(T),w),g&&(T=mM(T)),D&&+T==0&&y!=="+"&&(D=!1),C=(D?y==="("?y:u:y==="-"||y==="("?"":y)+C,_=(m==="s"?Cf[8+bv/3]:"")+_+(D&&y==="("?")":""),j){for($=-1,k=T.length;++$<k;)if(I=T.charCodeAt($),48>I||I>57){_=(I===46?i+T.slice($+1):T.slice($))+_,T=T.slice(0,$);break}}}b&&!v&&(T=t(T,1/0));var R=C.length+T.length+_.length,L=R<x?new Array(x-R+1).join(p):"";switch(b&&v&&(T=t(L+T,L.length?x-_.length:1/0),L=""),d){case"<":T=C+T+_+L;break;case"=":T=C+L+T+_;break;case"^":T=L.slice(0,R=L.length>>1)+C+T+_+L.slice(R);break;default:T=L+C+T+_;break}return a(T)}return M.toString=function(){return l+""},M}function f(l,p){var d=s((l=Gn(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(Ir(p)/3)))*3,h=Math.pow(10,-y),v=Cf[8+y/3];return function(x){return d(h*x)+v}}return{format:s,formatPrefix:f}}var Ci,ps,xv;xM({thousands:",",grouping:[3],currency:["$",""]});function xM(e){return Ci=bM(e),ps=Ci.format,xv=Ci.formatPrefix,Ci}function wM(e){return Math.max(0,-Ir(Math.abs(e)))}function OM(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Ir(t)/3)))*3-Ir(Math.abs(e)))}function AM(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Ir(t)-Ir(e))+1}function wv(e,t,r,n){var i=Du(e,t,r),a;switch(n=Gn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=OM(i,o))&&(n.precision=a),xv(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=AM(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=wM(i))&&(n.precision=a-(n.type==="%")*2);break}}return ps(n)}function Ft(e){var t=e.domain;return e.ticks=function(r){var n=t();return Iu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return wv(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=ku(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function ca(){var e=ls();return e.copy=function(){return wi(e,ca())},tt.apply(e,arguments),Ft(e)}function Ov(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,oa),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Ov(e).unknown(t)},e=arguments.length?Array.from(e,oa):[0,1],Ft(r)}function Av(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function If(e){return Math.log(e)}function kf(e){return Math.exp(e)}function SM(e){return-Math.log(-e)}function PM(e){return-Math.exp(-e)}function _M(e){return isFinite(e)?+("1e"+e):e<0?0:e}function $M(e){return e===10?_M:e===Math.E?Math.exp:t=>Math.pow(e,t)}function TM(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Df(e){return(t,r)=>-e(-t,r)}function ds(e){const t=e(If,kf),r=t.domain;let n=10,i,a;function o(){return i=TM(n),a=$M(n),r()[0]<0?(i=Df(i),a=Df(a),e(SM,PM)):e(If,kf),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let p=i(s),d=i(f),y,h;const v=u==null?10:+u;let x=[];if(!(n%1)&&d-p<v){if(p=Math.floor(p),d=Math.ceil(d),s>0){for(;p<=d;++p)for(y=1;y<n;++y)if(h=p<0?y/a(-p):y*a(p),!(h<s)){if(h>f)break;x.push(h)}}else for(;p<=d;++p)for(y=n-1;y>=1;--y)if(h=p>0?y/a(-p):y*a(p),!(h<s)){if(h>f)break;x.push(h)}x.length*2<v&&(x=Iu(s,f,v))}else x=Iu(p,d,Math.min(d-p,v)).map(a);return l?x.reverse():x},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Gn(c)).precision==null&&(c.trim=!0),c=ps(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(Av(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function Sv(){const e=ds(to()).domain([1,10]);return e.copy=()=>wi(e,Sv()).base(e.base()),tt.apply(e,arguments),e}function Nf(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Rf(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function hs(e){var t=1,r=e(Nf(t),Rf(t));return r.constant=function(n){return arguments.length?e(Nf(t=+n),Rf(t)):t},Ft(r)}function Pv(){var e=hs(to());return e.copy=function(){return wi(e,Pv()).constant(e.constant())},tt.apply(e,arguments)}function Lf(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function EM(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function jM(e){return e<0?-e*e:e*e}function vs(e){var t=e(Be,Be),r=1;function n(){return r===1?e(Be,Be):r===.5?e(EM,jM):e(Lf(r),Lf(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Ft(t)}function ys(){var e=vs(to());return e.copy=function(){return wi(e,ys()).exponent(e.exponent())},tt.apply(e,arguments),e}function MM(){return ys.apply(null,arguments).exponent(.5)}function Bf(e){return Math.sign(e)*e*e}function CM(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function _v(){var e=ls(),t=[0,1],r=!1,n;function i(a){var o=CM(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(Bf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,oa)).map(Bf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return _v(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},tt.apply(i,arguments),Ft(i)}function $v(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=Rj(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[bi(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Lt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return $v().domain(e).range(t).unknown(n)},tt.apply(a,arguments)}function Tv(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[bi(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Tv().domain([e,t]).range(i).unknown(a)},tt.apply(Ft(o),arguments)}function Ev(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[bi(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Ev().domain(e).range(t).unknown(r)},tt.apply(i,arguments)}const Ho=new Date,Ko=new Date;function Te(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Te(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Ho.setTime(+a),Ko.setTime(+o),e(Ho),e(Ko),Math.floor(r(Ho,Ko))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const sa=Te(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);sa.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Te(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):sa);sa.range;const wt=1e3,Je=wt*60,Ot=Je*60,$t=Ot*24,ms=$t*7,Ff=$t*30,Go=$t*365,er=Te(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*wt)},(e,t)=>(t-e)/wt,e=>e.getUTCSeconds());er.range;const gs=Te(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*wt)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getMinutes());gs.range;const bs=Te(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getUTCMinutes());bs.range;const xs=Te(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*wt-e.getMinutes()*Je)},(e,t)=>{e.setTime(+e+t*Ot)},(e,t)=>(t-e)/Ot,e=>e.getHours());xs.range;const ws=Te(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Ot)},(e,t)=>(t-e)/Ot,e=>e.getUTCHours());ws.range;const Oi=Te(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Je)/$t,e=>e.getDate()-1);Oi.range;const ro=Te(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/$t,e=>e.getUTCDate()-1);ro.range;const jv=Te(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/$t,e=>Math.floor(e/$t));jv.range;function fr(e){return Te(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Je)/ms)}const no=fr(0),la=fr(1),IM=fr(2),kM=fr(3),kr=fr(4),DM=fr(5),NM=fr(6);no.range;la.range;IM.range;kM.range;kr.range;DM.range;NM.range;function pr(e){return Te(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/ms)}const io=pr(0),fa=pr(1),RM=pr(2),LM=pr(3),Dr=pr(4),BM=pr(5),FM=pr(6);io.range;fa.range;RM.range;LM.range;Dr.range;BM.range;FM.range;const Os=Te(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Os.range;const As=Te(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());As.range;const Tt=Te(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());Tt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Te(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});Tt.range;const Et=Te(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Et.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Te(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});Et.range;function Mv(e,t,r,n,i,a){const o=[[er,1,wt],[er,5,5*wt],[er,15,15*wt],[er,30,30*wt],[a,1,Je],[a,5,5*Je],[a,15,15*Je],[a,30,30*Je],[i,1,Ot],[i,3,3*Ot],[i,6,6*Ot],[i,12,12*Ot],[n,1,$t],[n,2,2*$t],[r,1,ms],[t,1,Ff],[t,3,3*Ff],[e,1,Go]];function u(s,f,l){const p=f<s;p&&([s,f]=[f,s]);const d=l&&typeof l.range=="function"?l:c(s,f,l),y=d?d.range(s,+f+1):[];return p?y.reverse():y}function c(s,f,l){const p=Math.abs(f-s)/l,d=as(([,,v])=>v).right(o,p);if(d===o.length)return e.every(Du(s/Go,f/Go,l));if(d===0)return sa.every(Math.max(Du(s,f,l),1));const[y,h]=o[p/o[d-1][2]<o[d][2]/p?d-1:d];return y.every(h)}return[u,c]}const[zM,WM]=Mv(Et,As,io,jv,ws,bs),[UM,qM]=Mv(Tt,Os,no,Oi,xs,gs);function Yo(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Xo(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function wn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function HM(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=On(i),f=An(i),l=On(a),p=An(a),d=On(o),y=An(o),h=On(u),v=An(u),x=On(c),b=An(c),w={a:D,A:R,b:L,B:U,c:null,d:Kf,e:Kf,f:hC,g:SC,G:_C,H:fC,I:pC,j:dC,L:Cv,m:vC,M:yC,p:B,q:F,Q:Xf,s:Vf,S:mC,u:gC,U:bC,V:xC,w:wC,W:OC,x:null,X:null,y:AC,Y:PC,Z:$C,"%":Yf},g={a:H,A:J,b:ne,B:we,c:null,d:Gf,e:Gf,f:MC,g:zC,G:UC,H:TC,I:EC,j:jC,L:kv,m:CC,M:IC,p:Oe,q:Ue,Q:Xf,s:Vf,S:kC,u:DC,U:NC,V:RC,w:LC,W:BC,x:null,X:null,y:FC,Y:WC,Z:qC,"%":Yf},m={a:M,A:T,b:C,B:_,c:$,d:qf,e:qf,f:uC,g:Uf,G:Wf,H:Hf,I:Hf,j:nC,L:oC,m:rC,M:iC,p:j,q:tC,Q:sC,s:lC,S:aC,u:VM,U:ZM,V:JM,w:XM,W:QM,x:k,X:I,y:Uf,Y:Wf,Z:eC,"%":cC};w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),g.x=O(r,g),g.X=O(n,g),g.c=O(t,g);function O(K,V){return function(Q){var E=[],le=-1,W=0,pe=K.length,ye,Ie,yt;for(Q instanceof Date||(Q=new Date(+Q));++le<pe;)K.charCodeAt(le)===37&&(E.push(K.slice(W,le)),(Ie=zf[ye=K.charAt(++le)])!=null?ye=K.charAt(++le):Ie=ye==="e"?" ":"0",(yt=V[ye])&&(ye=yt(Q,Ie)),E.push(ye),W=le+1);return E.push(K.slice(W,le)),E.join("")}}function A(K,V){return function(Q){var E=wn(1900,void 0,1),le=P(E,K,Q+="",0),W,pe;if(le!=Q.length)return null;if("Q"in E)return new Date(E.Q);if("s"in E)return new Date(E.s*1e3+("L"in E?E.L:0));if(V&&!("Z"in E)&&(E.Z=0),"p"in E&&(E.H=E.H%12+E.p*12),E.m===void 0&&(E.m="q"in E?E.q:0),"V"in E){if(E.V<1||E.V>53)return null;"w"in E||(E.w=1),"Z"in E?(W=Xo(wn(E.y,0,1)),pe=W.getUTCDay(),W=pe>4||pe===0?fa.ceil(W):fa(W),W=ro.offset(W,(E.V-1)*7),E.y=W.getUTCFullYear(),E.m=W.getUTCMonth(),E.d=W.getUTCDate()+(E.w+6)%7):(W=Yo(wn(E.y,0,1)),pe=W.getDay(),W=pe>4||pe===0?la.ceil(W):la(W),W=Oi.offset(W,(E.V-1)*7),E.y=W.getFullYear(),E.m=W.getMonth(),E.d=W.getDate()+(E.w+6)%7)}else("W"in E||"U"in E)&&("w"in E||(E.w="u"in E?E.u%7:"W"in E?1:0),pe="Z"in E?Xo(wn(E.y,0,1)).getUTCDay():Yo(wn(E.y,0,1)).getDay(),E.m=0,E.d="W"in E?(E.w+6)%7+E.W*7-(pe+5)%7:E.w+E.U*7-(pe+6)%7);return"Z"in E?(E.H+=E.Z/100|0,E.M+=E.Z%100,Xo(E)):Yo(E)}}function P(K,V,Q,E){for(var le=0,W=V.length,pe=Q.length,ye,Ie;le<W;){if(E>=pe)return-1;if(ye=V.charCodeAt(le++),ye===37){if(ye=V.charAt(le++),Ie=m[ye in zf?V.charAt(le++):ye],!Ie||(E=Ie(K,Q,E))<0)return-1}else if(ye!=Q.charCodeAt(E++))return-1}return E}function j(K,V,Q){var E=s.exec(V.slice(Q));return E?(K.p=f.get(E[0].toLowerCase()),Q+E[0].length):-1}function M(K,V,Q){var E=d.exec(V.slice(Q));return E?(K.w=y.get(E[0].toLowerCase()),Q+E[0].length):-1}function T(K,V,Q){var E=l.exec(V.slice(Q));return E?(K.w=p.get(E[0].toLowerCase()),Q+E[0].length):-1}function C(K,V,Q){var E=x.exec(V.slice(Q));return E?(K.m=b.get(E[0].toLowerCase()),Q+E[0].length):-1}function _(K,V,Q){var E=h.exec(V.slice(Q));return E?(K.m=v.get(E[0].toLowerCase()),Q+E[0].length):-1}function $(K,V,Q){return P(K,t,V,Q)}function k(K,V,Q){return P(K,r,V,Q)}function I(K,V,Q){return P(K,n,V,Q)}function D(K){return o[K.getDay()]}function R(K){return a[K.getDay()]}function L(K){return c[K.getMonth()]}function U(K){return u[K.getMonth()]}function B(K){return i[+(K.getHours()>=12)]}function F(K){return 1+~~(K.getMonth()/3)}function H(K){return o[K.getUTCDay()]}function J(K){return a[K.getUTCDay()]}function ne(K){return c[K.getUTCMonth()]}function we(K){return u[K.getUTCMonth()]}function Oe(K){return i[+(K.getUTCHours()>=12)]}function Ue(K){return 1+~~(K.getUTCMonth()/3)}return{format:function(K){var V=O(K+="",w);return V.toString=function(){return K},V},parse:function(K){var V=A(K+="",!1);return V.toString=function(){return K},V},utcFormat:function(K){var V=O(K+="",g);return V.toString=function(){return K},V},utcParse:function(K){var V=A(K+="",!0);return V.toString=function(){return K},V}}}var zf={"-":"",_:" ",0:"0"},Me=/^\s*\d+/,KM=/^%/,GM=/[\\^$*+?|[\]().{}]/g;function ae(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function YM(e){return e.replace(GM,"\\$&")}function On(e){return new RegExp("^(?:"+e.map(YM).join("|")+")","i")}function An(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function XM(e,t,r){var n=Me.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function VM(e,t,r){var n=Me.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function ZM(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function JM(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function QM(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Wf(e,t,r){var n=Me.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Uf(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eC(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function tC(e,t,r){var n=Me.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function rC(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function qf(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function nC(e,t,r){var n=Me.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Hf(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function iC(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aC(e,t,r){var n=Me.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function oC(e,t,r){var n=Me.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function uC(e,t,r){var n=Me.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cC(e,t,r){var n=KM.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function sC(e,t,r){var n=Me.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function lC(e,t,r){var n=Me.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Kf(e,t){return ae(e.getDate(),t,2)}function fC(e,t){return ae(e.getHours(),t,2)}function pC(e,t){return ae(e.getHours()%12||12,t,2)}function dC(e,t){return ae(1+Oi.count(Tt(e),e),t,3)}function Cv(e,t){return ae(e.getMilliseconds(),t,3)}function hC(e,t){return Cv(e,t)+"000"}function vC(e,t){return ae(e.getMonth()+1,t,2)}function yC(e,t){return ae(e.getMinutes(),t,2)}function mC(e,t){return ae(e.getSeconds(),t,2)}function gC(e){var t=e.getDay();return t===0?7:t}function bC(e,t){return ae(no.count(Tt(e)-1,e),t,2)}function Iv(e){var t=e.getDay();return t>=4||t===0?kr(e):kr.ceil(e)}function xC(e,t){return e=Iv(e),ae(kr.count(Tt(e),e)+(Tt(e).getDay()===4),t,2)}function wC(e){return e.getDay()}function OC(e,t){return ae(la.count(Tt(e)-1,e),t,2)}function AC(e,t){return ae(e.getFullYear()%100,t,2)}function SC(e,t){return e=Iv(e),ae(e.getFullYear()%100,t,2)}function PC(e,t){return ae(e.getFullYear()%1e4,t,4)}function _C(e,t){var r=e.getDay();return e=r>=4||r===0?kr(e):kr.ceil(e),ae(e.getFullYear()%1e4,t,4)}function $C(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ae(t/60|0,"0",2)+ae(t%60,"0",2)}function Gf(e,t){return ae(e.getUTCDate(),t,2)}function TC(e,t){return ae(e.getUTCHours(),t,2)}function EC(e,t){return ae(e.getUTCHours()%12||12,t,2)}function jC(e,t){return ae(1+ro.count(Et(e),e),t,3)}function kv(e,t){return ae(e.getUTCMilliseconds(),t,3)}function MC(e,t){return kv(e,t)+"000"}function CC(e,t){return ae(e.getUTCMonth()+1,t,2)}function IC(e,t){return ae(e.getUTCMinutes(),t,2)}function kC(e,t){return ae(e.getUTCSeconds(),t,2)}function DC(e){var t=e.getUTCDay();return t===0?7:t}function NC(e,t){return ae(io.count(Et(e)-1,e),t,2)}function Dv(e){var t=e.getUTCDay();return t>=4||t===0?Dr(e):Dr.ceil(e)}function RC(e,t){return e=Dv(e),ae(Dr.count(Et(e),e)+(Et(e).getUTCDay()===4),t,2)}function LC(e){return e.getUTCDay()}function BC(e,t){return ae(fa.count(Et(e)-1,e),t,2)}function FC(e,t){return ae(e.getUTCFullYear()%100,t,2)}function zC(e,t){return e=Dv(e),ae(e.getUTCFullYear()%100,t,2)}function WC(e,t){return ae(e.getUTCFullYear()%1e4,t,4)}function UC(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Dr(e):Dr.ceil(e),ae(e.getUTCFullYear()%1e4,t,4)}function qC(){return"+0000"}function Yf(){return"%"}function Xf(e){return+e}function Vf(e){return Math.floor(+e/1e3)}var vr,Nv,Rv;HC({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function HC(e){return vr=HM(e),Nv=vr.format,vr.parse,Rv=vr.utcFormat,vr.utcParse,vr}function KC(e){return new Date(e)}function GC(e){return e instanceof Date?+e:+new Date(+e)}function Ss(e,t,r,n,i,a,o,u,c,s){var f=ls(),l=f.invert,p=f.domain,d=s(".%L"),y=s(":%S"),h=s("%I:%M"),v=s("%I %p"),x=s("%a %d"),b=s("%b %d"),w=s("%B"),g=s("%Y");function m(O){return(c(O)<O?d:u(O)<O?y:o(O)<O?h:a(O)<O?v:n(O)<O?i(O)<O?x:b:r(O)<O?w:g)(O)}return f.invert=function(O){return new Date(l(O))},f.domain=function(O){return arguments.length?p(Array.from(O,GC)):p().map(KC)},f.ticks=function(O){var A=p();return e(A[0],A[A.length-1],O??10)},f.tickFormat=function(O,A){return A==null?m:s(A)},f.nice=function(O){var A=p();return(!O||typeof O.range!="function")&&(O=t(A[0],A[A.length-1],O??10)),O?p(Av(A,O)):f},f.copy=function(){return wi(f,Ss(e,t,r,n,i,a,o,u,c,s))},f}function YC(){return tt.apply(Ss(UM,qM,Tt,Os,no,Oi,xs,gs,er,Nv).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function XC(){return tt.apply(Ss(zM,WM,Et,As,io,ro,ws,bs,er,Rv).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ao(){var e=0,t=1,r,n,i,a,o=Be,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(p){var d,y;return arguments.length?([d,y]=p,o=l(d,y),s):[o(0),o(1)]}}return s.range=f(pn),s.rangeRound=f(ss),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function zt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Lv(){var e=Ft(ao()(Be));return e.copy=function(){return zt(e,Lv())},Ct.apply(e,arguments)}function Bv(){var e=ds(ao()).domain([1,10]);return e.copy=function(){return zt(e,Bv()).base(e.base())},Ct.apply(e,arguments)}function Fv(){var e=hs(ao());return e.copy=function(){return zt(e,Fv()).constant(e.constant())},Ct.apply(e,arguments)}function Ps(){var e=vs(ao());return e.copy=function(){return zt(e,Ps()).exponent(e.exponent())},Ct.apply(e,arguments)}function VC(){return Ps.apply(null,arguments).exponent(.5)}function zv(){var e=[],t=Be;function r(n){if(n!=null&&!isNaN(n=+n))return t((bi(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Lt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>Nj(e,a/n))},r.copy=function(){return zv(t).domain(e)},Ct.apply(r,arguments)}function oo(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=Be,f,l=!1,p;function d(h){return isNaN(h=+h)?p:(h=.5+((h=+f(h))-a)*(n*h<n*a?u:c),s(l?Math.max(0,Math.min(1,h)):h))}d.domain=function(h){return arguments.length?([e,t,r]=h,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d):[e,t,r]},d.clamp=function(h){return arguments.length?(l=!!h,d):l},d.interpolator=function(h){return arguments.length?(s=h,d):s};function y(h){return function(v){var x,b,w;return arguments.length?([x,b,w]=v,s=cM(h,[x,b,w]),d):[s(0),s(.5),s(1)]}}return d.range=y(pn),d.rangeRound=y(ss),d.unknown=function(h){return arguments.length?(p=h,d):p},function(h){return f=h,i=h(e),a=h(t),o=h(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d}}function Wv(){var e=Ft(oo()(Be));return e.copy=function(){return zt(e,Wv())},Ct.apply(e,arguments)}function Uv(){var e=ds(oo()).domain([.1,1,10]);return e.copy=function(){return zt(e,Uv()).base(e.base())},Ct.apply(e,arguments)}function qv(){var e=hs(oo());return e.copy=function(){return zt(e,qv()).constant(e.constant())},Ct.apply(e,arguments)}function _s(){var e=vs(oo());return e.copy=function(){return zt(e,_s()).exponent(e.exponent())},Ct.apply(e,arguments)}function ZC(){return _s.apply(null,arguments).exponent(.5)}const Zf=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Un,scaleDiverging:Wv,scaleDivergingLog:Uv,scaleDivergingPow:_s,scaleDivergingSqrt:ZC,scaleDivergingSymlog:qv,scaleIdentity:Ov,scaleImplicit:Nu,scaleLinear:ca,scaleLog:Sv,scaleOrdinal:os,scalePoint:jn,scalePow:ys,scaleQuantile:$v,scaleQuantize:Tv,scaleRadial:_v,scaleSequential:Lv,scaleSequentialLog:Bv,scaleSequentialPow:Ps,scaleSequentialQuantile:zv,scaleSequentialSqrt:VC,scaleSequentialSymlog:Fv,scaleSqrt:MM,scaleSymlog:Pv,scaleThreshold:Ev,scaleTime:YC,scaleUtc:XC,tickFormat:wv},Symbol.toStringTag,{value:"Module"}));var JC=tn;function QC(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!JC(o):r(o,u)))var u=o,c=a}return c}var uo=QC;function eI(e,t){return e>t}var Hv=eI,tI=uo,rI=Hv,nI=fn;function iI(e){return e&&e.length?tI(e,nI,rI):void 0}var aI=iI;const Nt=se(aI);function oI(e,t){return e<t}var Kv=oI,uI=uo,cI=Kv,sI=fn;function lI(e){return e&&e.length?uI(e,sI,cI):void 0}var fI=lI;const co=se(fI);var pI=zc,dI=vt,hI=ev,vI=We;function yI(e,t){var r=vI(e)?pI:hI;return r(e,dI(t))}var mI=yI,gI=Jh,bI=mI;function xI(e,t){return gI(bI(e,t),1)}var wI=xI;const OI=se(wI);var AI=es;function SI(e,t){return AI(e,t)}var PI=SI;const Nr=se(PI);var dn=1e9,_I={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Ts,be=!0,et="[DecimalError] ",ir=et+"Invalid argument: ",$s=et+"Exponent out of range: ",hn=Math.floor,Vt=Math.pow,$I=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Ke,Ee=1e7,ge=7,Gv=9007199254740991,pa=hn(Gv/ge),G={};G.absoluteValue=G.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};G.comparedTo=G.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};G.decimalPlaces=G.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*ge;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};G.dividedBy=G.div=function(e){return Pt(this,new this.constructor(e))};G.dividedToIntegerBy=G.idiv=function(e){var t=this,r=t.constructor;return fe(Pt(t,new r(e),0,1),r.precision)};G.equals=G.eq=function(e){return!this.cmp(e)};G.exponent=function(){return Se(this)};G.greaterThan=G.gt=function(e){return this.cmp(e)>0};G.greaterThanOrEqualTo=G.gte=function(e){return this.cmp(e)>=0};G.isInteger=G.isint=function(){return this.e>this.d.length-2};G.isNegative=G.isneg=function(){return this.s<0};G.isPositive=G.ispos=function(){return this.s>0};G.isZero=function(){return this.s===0};G.lessThan=G.lt=function(e){return this.cmp(e)<0};G.lessThanOrEqualTo=G.lte=function(e){return this.cmp(e)<1};G.logarithm=G.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Ke))throw Error(et+"NaN");if(r.s<1)throw Error(et+(r.s?"NaN":"-Infinity"));return r.eq(Ke)?new n(0):(be=!1,t=Pt(Yn(r,a),Yn(e,a),a),be=!0,fe(t,i))};G.minus=G.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Vv(t,e):Yv(t,(e.s=-e.s,e))};G.modulo=G.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(et+"NaN");return r.s?(be=!1,t=Pt(r,e,0,1).times(e),be=!0,r.minus(t)):fe(new n(r),i)};G.naturalExponential=G.exp=function(){return Xv(this)};G.naturalLogarithm=G.ln=function(){return Yn(this)};G.negated=G.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};G.plus=G.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Yv(t,e):Vv(t,(e.s=-e.s,e))};G.precision=G.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(ir+e);if(t=Se(i)+1,n=i.d.length-1,r=n*ge+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};G.squareRoot=G.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(et+"NaN")}for(e=Se(u),be=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ut(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=hn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(Pt(u,a,o+2)).times(.5),ut(a.d).slice(0,o)===(t=ut(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(fe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return be=!0,fe(n,r)};G.times=G.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,p=f.d,d=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,s=d.length,c<s&&(a=p,p=d,d=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+d[n]*p[i-n-1]+t,a[i--]=u%Ee|0,t=u/Ee|0;a[i]=(a[i]+t)%Ee|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,be?fe(e,l.precision):e};G.toDecimalPlaces=G.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(pt(e,0,dn),t===void 0?t=n.rounding:pt(t,0,8),fe(r,e+Se(r)+1,t))};G.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ur(n,!0):(pt(e,0,dn),t===void 0?t=i.rounding:pt(t,0,8),n=fe(new i(n),e+1,t),r=ur(n,!0,e+1)),r};G.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?ur(i):(pt(e,0,dn),t===void 0?t=a.rounding:pt(t,0,8),n=fe(new a(i),e+Se(i)+1,t),r=ur(n.abs(),!1,e+Se(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};G.toInteger=G.toint=function(){var e=this,t=e.constructor;return fe(new t(e),Se(e)+1,t.rounding)};G.toNumber=function(){return+this};G.toPower=G.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Ke);if(u=new c(u),!u.s){if(e.s<1)throw Error(et+"Infinity");return u}if(u.eq(Ke))return u;if(n=c.precision,e.eq(Ke))return fe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Gv){for(i=new c(Ke),t=Math.ceil(n/ge+4),be=!1;r%2&&(i=i.times(u),Qf(i.d,t)),r=hn(r/2),r!==0;)u=u.times(u),Qf(u.d,t);return be=!0,e.s<0?new c(Ke).div(i):fe(i,n)}}else if(a<0)throw Error(et+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,be=!1,i=e.times(Yn(u,n+s)),be=!0,i=Xv(i),i.s=a,i};G.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=Se(i),n=ur(i,r<=a.toExpNeg||r>=a.toExpPos)):(pt(e,1,dn),t===void 0?t=a.rounding:pt(t,0,8),i=fe(new a(i),e,t),r=Se(i),n=ur(i,e<=r||r<=a.toExpNeg,e)),n};G.toSignificantDigits=G.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(pt(e,1,dn),t===void 0?t=n.rounding:pt(t,0,8)),fe(new n(r),e,t)};G.toString=G.valueOf=G.val=G.toJSON=G[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=Se(e),r=e.constructor;return ur(e,t<=r.toExpNeg||t>=r.toExpPos)};function Yv(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),be?fe(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/ge),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Ee|0,c[a]%=Ee;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,be?fe(t,l):t}function pt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ir+e)}function ut(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=ge-n.length,r&&(a+=kt(r)),a+=n;o=e[t],n=o+"",r=ge-n.length,r&&(a+=kt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var Pt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Ee|0,o=a/Ee|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Ee+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,p,d,y,h,v,x,b,w,g,m,O,A,P,j=n.constructor,M=n.s==i.s?1:-1,T=n.d,C=i.d;if(!n.s)return new j(n);if(!i.s)throw Error(et+"Division by zero");for(c=n.e-i.e,A=C.length,m=T.length,d=new j(M),y=d.d=[],s=0;C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--c,a==null?b=a=j.precision:o?b=a+(Se(n)-Se(i))+1:b=a,b<0)return new j(0);if(b=b/ge+2|0,s=0,A==1)for(f=0,C=C[0],b++;(s<m||f)&&b--;s++)w=f*Ee+(T[s]||0),y[s]=w/C|0,f=w%C|0;else{for(f=Ee/(C[0]+1)|0,f>1&&(C=e(C,f),T=e(T,f),A=C.length,m=T.length),g=A,h=T.slice(0,A),v=h.length;v<A;)h[v++]=0;P=C.slice(),P.unshift(0),O=C[0],C[1]>=Ee/2&&++O;do f=0,u=t(C,h,A,v),u<0?(x=h[0],A!=v&&(x=x*Ee+(h[1]||0)),f=x/O|0,f>1?(f>=Ee&&(f=Ee-1),l=e(C,f),p=l.length,v=h.length,u=t(l,h,p,v),u==1&&(f--,r(l,A<p?P:C,p))):(f==0&&(u=f=1),l=C.slice()),p=l.length,p<v&&l.unshift(0),r(h,l,v),u==-1&&(v=h.length,u=t(C,h,A,v),u<1&&(f++,r(h,A<v?P:C,v))),v=h.length):u===0&&(f++,h=[0]),y[s++]=f,u&&h[0]?h[v++]=T[g]||0:(h=[T[g]],v=1);while((g++<m||h[0]!==void 0)&&b--)}return y[0]||y.shift(),d.e=c,fe(d,o?a+Se(d)+1:a)}}();function Xv(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(Se(e)>16)throw Error($s+Se(e));if(!e.s)return new f(Ke);for(t==null?(be=!1,u=l):u=t,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Vt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Ke),f.precision=u;;){if(i=fe(i.times(e),u),r=r.times(++c),o=a.plus(Pt(i,r,u)),ut(o.d).slice(0,u)===ut(a.d).slice(0,u)){for(;s--;)a=fe(a.times(a),u);return f.precision=l,t==null?(be=!0,fe(a,l)):a}a=o}}function Se(e){for(var t=e.e*ge,r=e.d[0];r>=10;r/=10)t++;return t}function Vo(e,t,r){if(t>e.LN10.sd())throw be=!0,r&&(e.precision=r),Error(et+"LN10 precision limit exceeded");return fe(new e(e.LN10),t)}function kt(e){for(var t="";e--;)t+="0";return t}function Yn(e,t){var r,n,i,a,o,u,c,s,f,l=1,p=10,d=e,y=d.d,h=d.constructor,v=h.precision;if(d.s<1)throw Error(et+(d.s?"NaN":"-Infinity"));if(d.eq(Ke))return new h(0);if(t==null?(be=!1,s=v):s=t,d.eq(10))return t==null&&(be=!0),Vo(h,s);if(s+=p,h.precision=s,r=ut(y),n=r.charAt(0),a=Se(d),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(e),r=ut(d.d),n=r.charAt(0),l++;a=Se(d),n>1?(d=new h("0."+r),a++):d=new h(n+"."+r.slice(1))}else return c=Vo(h,s+2,v).times(a+""),d=Yn(new h(n+"."+r.slice(1)),s-p).plus(c),h.precision=v,t==null?(be=!0,fe(d,v)):d;for(u=o=d=Pt(d.minus(Ke),d.plus(Ke),s),f=fe(d.times(d),s),i=3;;){if(o=fe(o.times(f),s),c=u.plus(Pt(o,new h(i),s)),ut(c.d).slice(0,s)===ut(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Vo(h,s+2,v).times(a+""))),u=Pt(u,new h(l),s),h.precision=v,t==null?(be=!0,fe(u,v)):u;u=c,i+=2}}function Jf(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=hn(r/ge),e.d=[],n=(r+1)%ge,r<0&&(n+=ge),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=ge;n<i;)e.d.push(+t.slice(n,n+=ge));t=t.slice(n),n=ge-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),be&&(e.e>pa||e.e<-pa))throw Error($s+r)}else e.s=0,e.e=0,e.d=[0];return e}function fe(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=ge,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/ge),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=ge,i=n-ge+o}if(r!==void 0&&(a=Vt(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Vt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=Se(e),l.length=1,t=t-a-1,l[0]=Vt(10,(ge-t%ge)%ge),e.e=hn(-t/ge)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Vt(10,ge-n),l[f]=i>0?(s/Vt(10,o-i)%Vt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Ee&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Ee)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(be&&(e.e>pa||e.e<-pa))throw Error($s+Se(e));return e}function Vv(e,t){var r,n,i,a,o,u,c,s,f,l,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),be?fe(t,d):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(d/ge),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Ee-1;--c[a],c[i]+=Ee}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,be?fe(t,d):t):new p(0)}function ur(e,t,r){var n,i=Se(e),a=ut(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+kt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+kt(-i-1)+a,r&&(n=r-o)>0&&(a+=kt(n))):i>=o?(a+=kt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+kt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=kt(n))),e.s<0?"-"+a:a}function Qf(e,t){if(e.length>t)return e.length=t,!0}function Zv(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(ir+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Jf(o,a.toString())}else if(typeof a!="string")throw Error(ir+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,$I.test(a))Jf(o,a);else throw Error(ir+a)}if(i.prototype=G,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Zv,i.config=i.set=TI,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function TI(e){if(!e||typeof e!="object")throw Error(et+"Object expected");var t,r,n,i=["precision",1,dn,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(hn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(ir+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(ir+r+": "+n);return this}var Ts=Zv(_I);Ke=new Ts(1);const ce=Ts;function EI(e){return II(e)||CI(e)||MI(e)||jI()}function jI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function MI(e,t){if(e){if(typeof e=="string")return Fu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fu(e,t)}}function CI(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function II(e){if(Array.isArray(e))return Fu(e)}function Fu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var kI=function(t){return t},Jv={"@@functional/placeholder":!0},Qv=function(t){return t===Jv},ep=function(t){return function r(){return arguments.length===0||arguments.length===1&&Qv(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},DI=function e(t,r){return t===1?r:ep(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Jv}).length;return o>=t?r.apply(void 0,i):e(t-o,ep(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Qv(l)?c.shift():l});return r.apply(void 0,EI(f).concat(c))}))})},so=function(t){return DI(t.length,t)},zu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},NI=so(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),RI=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return kI;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},Wu=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},ey=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function LI(e){var t;return e===0?t=1:t=Math.floor(new ce(e).abs().log(10).toNumber())+1,t}function BI(e,t,r){for(var n=new ce(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var FI=so(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),zI=so(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),WI=so(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const lo={rangeStep:BI,getDigitCount:LI,interpolateNumber:FI,uninterpolateNumber:zI,uninterpolateTruncation:WI};function Uu(e){return HI(e)||qI(e)||ty(e)||UI()}function UI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qI(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function HI(e){if(Array.isArray(e))return qu(e)}function Xn(e,t){return YI(e)||GI(e,t)||ty(e,t)||KI()}function KI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ty(e,t){if(e){if(typeof e=="string")return qu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qu(e,t)}}function qu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function GI(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function YI(e){if(Array.isArray(e))return e}function ry(e){var t=Xn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function ny(e,t,r){if(e.lte(0))return new ce(0);var n=lo.getDigitCount(e.toNumber()),i=new ce(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ce(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ce(Math.ceil(c))}function XI(e,t,r){var n=1,i=new ce(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ce(10).pow(lo.getDigitCount(e)-1),i=new ce(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ce(Math.floor(e)))}else e===0?i=new ce(Math.floor((t-1)/2)):r||(i=new ce(Math.floor(e)));var o=Math.floor((t-1)/2),u=RI(NI(function(c){return i.add(new ce(c-o).mul(n)).toNumber()}),zu);return u(0,t)}function iy(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ce(0),tickMin:new ce(0),tickMax:new ce(0)};var a=ny(new ce(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ce(0):(o=new ce(e).add(t).div(2),o=o.sub(new ce(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ce(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?iy(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ce(u).mul(a)),tickMax:o.add(new ce(c).mul(a))})}function VI(e){var t=Xn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=ry([r,n]),c=Xn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Uu(zu(0,i-1).map(function(){return 1/0}))):[].concat(Uu(zu(0,i-1).map(function(){return-1/0})),[f]);return r>n?Wu(l):l}if(s===f)return XI(s,i,a);var p=iy(s,f,o,a),d=p.step,y=p.tickMin,h=p.tickMax,v=lo.rangeStep(y,h.add(new ce(.1).mul(d)),d);return r>n?Wu(v):v}function ZI(e,t){var r=Xn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=ry([n,i]),u=Xn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=ny(new ce(s).sub(c).div(f-1),a,0),p=[].concat(Uu(lo.rangeStep(new ce(c),new ce(s).sub(new ce(.99).mul(l)),l)),[s]);return n>i?Wu(p):p}var JI=ey(VI),QI=ey(ZI),tp="Invariant failed";function cr(e,t){var r=typeof t=="function"?t():t,n=r?"".concat(tp,": ").concat(r):tp;throw new Error(n)}var ek=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function da(){return da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},da.apply(this,arguments)}function tk(e,t){return ak(e)||ik(e,t)||nk(e,t)||rk()}function rk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nk(e,t){if(e){if(typeof e=="string")return rp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rp(e,t)}}function rp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ik(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function ak(e){if(Array.isArray(e))return e}function ok(e,t){if(e==null)return{};var r=uk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function uk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uy(n.key),n)}}function lk(e,t,r){return t&&sk(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function fk(e,t,r){return t=ha(t),pk(e,ay()?Reflect.construct(t,r||[],ha(e).constructor):t.apply(e,r))}function pk(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dk(e)}function dk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ay(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ay=function(){return!!e})()}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ha(e)}function hk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hu(e,t)}function Hu(e,t){return Hu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Hu(e,t)}function oy(e,t,r){return t=uy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uy(e){var t=vk(e,"string");return Rr(t)=="symbol"?t:t+""}function vk(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fo=function(e){function t(){return ck(this,t),fk(this,t,arguments)}return hk(t,e),lk(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,p=ok(n,ek),d=Y(p,!1);this.props.direction==="x"&&f.type!=="number"&&cr(!1,'ErrorBar requires Axis type property to be "number".');var y=c.map(function(h){var v=s(h,u),x=v.x,b=v.y,w=v.value,g=v.errorVal;if(!g)return null;var m=[],O,A;if(Array.isArray(g)){var P=tk(g,2);O=P[0],A=P[1]}else O=A=g;if(a==="vertical"){var j=f.scale,M=b+i,T=M+o,C=M-o,_=j(w-O),$=j(w+A);m.push({x1:$,y1:T,x2:$,y2:C}),m.push({x1:_,y1:M,x2:$,y2:M}),m.push({x1:_,y1:T,x2:_,y2:C})}else if(a==="horizontal"){var k=l.scale,I=x+i,D=I-o,R=I+o,L=k(w-O),U=k(w+A);m.push({x1:D,y1:U,x2:R,y2:U}),m.push({x1:I,y1:L,x2:I,y2:U}),m.push({x1:D,y1:L,x2:R,y2:L})}return S.createElement(ie,da({className:"recharts-errorBar",key:"bar-".concat(m.map(function(B){return"".concat(B.x1,"-").concat(B.x2,"-").concat(B.y1,"-").concat(B.y2)}))},d),m.map(function(B){return S.createElement("line",da({},B,{key:"line-".concat(B.x1,"-").concat(B.x2,"-").concat(B.y1,"-").concat(B.y2)}))}))});return S.createElement(ie,{className:"recharts-errorBars"},y)}}])}(S.Component);oy(fo,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});oy(fo,"displayName","ErrorBar");function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}function np(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ht(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?np(Object(r),!0).forEach(function(n){yk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):np(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yk(e,t,r){return t=mk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mk(e){var t=gk(e,"string");return Vn(t)=="symbol"?t:t+""}function gk(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var cy=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=He(r,Sr);if(!o)return null;var u=Sr.defaultProps,c=u!==void 0?Ht(Ht({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var p=l.item,d=l.props,y=d.sectors||d.data||[];return f.concat(y.map(function(h){return{type:o.props.iconType||p.props.legendType,value:h.name,color:h.fill,payload:h}}))},[]):s=(n||[]).map(function(f){var l=f.item,p=l.type.defaultProps,d=p!==void 0?Ht(Ht({},p),l.props):{},y=d.dataKey,h=d.name,v=d.legendType,x=d.hide;return{inactive:x,dataKey:y,type:c.iconType||v||"square",color:Es(l),value:h||y,payload:d}}),Ht(Ht(Ht({},c),Sr.getWithHeight(o,i)),{},{payload:s,item:o})};function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function ip(e){return Ok(e)||wk(e)||xk(e)||bk()}function bk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xk(e,t){if(e){if(typeof e=="string")return Ku(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ku(e,t)}}function wk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ok(e){if(Array.isArray(e))return Ku(e)}function Ku(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ap(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ap(Object(r),!0).forEach(function(n){_r(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ap(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _r(e,t,r){return t=Ak(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ak(e){var t=Sk(e,"string");return Zn(t)=="symbol"?t:t+""}function Sk(e,t){if(Zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _e(e,t,r){return ee(e)||ee(t)?r:$e(t)?Ge(e,t,r):Z(t)?t(e):r}function Mn(e,t,r,n){var i=OI(e,function(u){return _e(u,t)});if(r==="number"){var a=i.filter(function(u){return z(u)||parseFloat(u)});return a.length?[co(a),Nt(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!ee(u)}):i;return o.map(function(u){return $e(u)||u instanceof Date?u:""})}var Pk=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,p=s>=u-1?i[0].coordinate:i[s+1].coordinate,d=void 0;if(Re(l-f)!==Re(p-l)){var y=[];if(Re(p-l)===Re(c[1]-c[0])){d=p;var h=l+c[1]-c[0];y[0]=Math.min(h,(h+f)/2),y[1]=Math.max(h,(h+f)/2)}else{d=f;var v=p+c[1]-c[0];y[0]=Math.min(l,(v+l)/2),y[1]=Math.max(l,(v+l)/2)}var x=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(t>x[0]&&t<=x[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var b=Math.min(f,p),w=Math.max(f,p);if(t>(b+l)/2&&t<=(w+l)/2){o=i[s].index;break}}}else for(var g=0;g<u;g++)if(g===0&&t<=(n[g].coordinate+n[g+1].coordinate)/2||g>0&&g<u-1&&t>(n[g].coordinate+n[g-1].coordinate)/2&&t<=(n[g].coordinate+n[g+1].coordinate)/2||g===u-1&&t>(n[g].coordinate+n[g-1].coordinate)/2){o=n[g].index;break}return o},Es=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?xe(xe({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},_k=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),p=0,d=l.length;p<d;p++){var y=f[l[p]],h=y.items,v=y.cateAxisId,x=h.filter(function(A){return St(A.type).indexOf("Bar")>=0});if(x&&x.length){var b=x[0].type.defaultProps,w=b!==void 0?xe(xe({},b),x[0].props):x[0].props,g=w.barSize,m=w[v];o[m]||(o[m]=[]);var O=ee(g)?r:g;o[m].push({item:x[0],stackList:x.slice(1),barSize:ee(O)?void 0:Le(O,n,0)})}}return o},$k=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Le(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var p=!1,d=i/c,y=o.reduce(function(g,m){return g+m.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&d>0&&(p=!0,d*=.9,y=c*d);var h=(i-y)/2>>0,v={offset:h-s,size:0};f=o.reduce(function(g,m){var O={item:m.item,position:{offset:v.offset+v.size+s,size:p?d:m.barSize}},A=[].concat(ip(g),[O]);return v=A[A.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){A.push({item:P,position:v})}),A},l)}else{var x=Le(n,i,0,!0);i-2*x-(c-1)*s<=0&&(s=0);var b=(i-2*x-(c-1)*s)/c;b>1&&(b>>=0);var w=u===+u?Math.min(b,u):b;f=o.reduce(function(g,m,O){var A=[].concat(ip(g),[{item:m.item,position:{offset:x+(b+s)*O+(b-w)/2,size:w}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){A.push({item:P,position:A[A.length-1].position})}),A},l)}return f},Tk=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=cy({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,p=f.height,d=s.align,y=s.verticalAlign,h=s.layout;if((h==="vertical"||h==="horizontal"&&y==="middle")&&d!=="center"&&z(t[d]))return xe(xe({},t),{},_r({},d,t[d]+(l||0)));if((h==="horizontal"||h==="vertical"&&d==="center")&&y!=="middle"&&z(t[y]))return xe(xe({},t),{},_r({},y,t[y]+(p||0)))}return t},Ek=function(t,r,n){return ee(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},sy=function(t,r,n,i,a){var o=r.props.children,u=Qe(o,fo).filter(function(s){return Ek(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=_e(f,n);if(ee(l))return s;var p=Array.isArray(l)?[co(l),Nt(l)]:[l,l],d=c.reduce(function(y,h){var v=_e(f,h,0),x=p[0]-Math.abs(Array.isArray(v)?v[0]:v),b=p[1]+Math.abs(Array.isArray(v)?v[1]:v);return[Math.min(x,y[0]),Math.max(b,y[1])]},[1/0,-1/0]);return[Math.min(d[0],s[0]),Math.max(d[1],s[1])]},[1/0,-1/0])}return null},jk=function(t,r,n,i,a){var o=r.map(function(u){return sy(t,u,n,a,i)}).filter(function(u){return!ee(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},ly=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&sy(t,c,s,i)||Mn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},fy=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},py=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},At=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Re(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var p=a?a.indexOf(l):l;return{coordinate:i(p)+s,value:l,offset:s}});return f.filter(function(l){return!un(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,p){return{coordinate:i(l)+s,value:l,index:p,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,p){return{coordinate:i(l)+s,value:a?a[l]:l,index:p,offset:s}})},Zo=new WeakMap,Ii=function(t,r){if(typeof r!="function")return t;Zo.has(t)||Zo.set(t,new WeakMap);var n=Zo.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},dy=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Un(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:ca(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:jn(),realScaleType:"point"}:a==="category"?{scale:Un(),realScaleType:"band"}:{scale:ca(),realScaleType:"linear"};if(mi(i)){var c="scale".concat(Ya(i));return{scale:(Zf[c]||jn)(),realScaleType:Zf[c]?c:"point"}}return Z(i)?{scale:i}:{scale:jn(),realScaleType:"point"}},op=1e-4,hy=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-op,o=Math.max(i[0],i[1])+op,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},Mk=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},Ck=function(t,r){if(!r||r.length!==2||!z(r[0])||!z(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!z(t[0])||t[0]<n)&&(a[0]=n),(!z(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},Ik=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=un(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},kk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=un(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},Dk={sign:Ik,expand:tO,none:Tr,silhouette:rO,wiggle:nO,positive:kk},Nk=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=Dk[n],o=eO().keys(i).value(function(u,c){return+_e(u,c,0)}).order(mu).offset(a);return o(t)},Rk=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,p){var d,y=(d=p.type)!==null&&d!==void 0&&d.defaultProps?xe(xe({},p.type.defaultProps),p.props):p.props,h=y.stackId,v=y.hide;if(v)return l;var x=y[n],b=l[x]||{hasStack:!1,stackGroups:{}};if($e(h)){var w=b.stackGroups[h]||{numericAxisId:n,cateAxisId:i,items:[]};w.items.push(p),b.hasStack=!0,b.stackGroups[h]=w}else b.stackGroups[cn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return xe(xe({},l),{},_r({},x,b))},c),f={};return Object.keys(s).reduce(function(l,p){var d=s[p];if(d.hasStack){var y={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(h,v){var x=d.stackGroups[v];return xe(xe({},h),{},_r({},v,{numericAxisId:n,cateAxisId:i,items:x.items,stackedData:Nk(t,x.items,a)}))},y)}return xe(xe({},l),{},_r({},p,d))},f)},vy=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=JI(s,a,u);return t.domain([co(f),Nt(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),p=QI(l,a,u);return{niceTicks:p}}return null};function up(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!ee(i[t.dataKey])){var u=zi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=_e(i,ee(o)?t.dataKey:o);return ee(c)?null:t.scale(c)}var cp=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=_e(o,r.dataKey,r.domain[u]);return ee(c)?null:r.scale(c)-a/2+i},Lk=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Bk=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?xe(xe({},t.type.defaultProps),t.props):t.props,a=i.stackId;if($e(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},Fk=function(t){return t.reduce(function(r,n){return[co(n.concat([r[0]]).filter(z)),Nt(n.concat([r[1]]).filter(z))]},[1/0,-1/0])},yy=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=Fk(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},sp=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lp=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Gu=function(t,r,n){if(Z(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(z(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(sp.test(t[0])){var a=+sp.exec(t[0])[1];i[0]=r[0]-a}else Z(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(z(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(lp.test(t[1])){var o=+lp.exec(t[1])[1];i[1]=r[1]+o}else Z(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},va=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=rs(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},fp=function(t,r,n){return!t||!t.length||Nr(t,Ge(n,"type.defaultProps.domain"))?r:t},my=function(t,r){var n=t.type.defaultProps?xe(xe({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return xe(xe({},Y(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Es(t),value:_e(r,i),type:c,payload:r,chartType:s,hide:f})};function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pp(Object(r),!0).forEach(function(n){gy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gy(e,t,r){return t=zk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zk(e){var t=Wk(e,"string");return Jn(t)=="symbol"?t:t+""}function Wk(e,t){if(Jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Uk(e,t){return Gk(e)||Kk(e,t)||Hk(e,t)||qk()}function qk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Hk(e,t){if(e){if(typeof e=="string")return dp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dp(e,t)}}function dp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Kk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Gk(e){if(Array.isArray(e))return e}var ya=Math.PI/180,Yk=function(t){return t*180/Math.PI},ve=function(t,r,n,i){return{x:t+Math.cos(-ya*i)*n,y:r+Math.sin(-ya*i)*n}},by=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Xk=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Le(t.cx,o,o/2),l=Le(t.cy,u,u/2),p=by(o,u,n),d=Le(t.innerRadius,p,0),y=Le(t.outerRadius,p,p*.8),h=Object.keys(r);return h.reduce(function(v,x){var b=r[x],w=b.domain,g=b.reversed,m;if(ee(b.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[d,y]),g&&(m=[m[1],m[0]]);else{m=b.range;var O=m,A=Uk(O,2);c=A[0],s=A[1]}var P=dy(b,a),j=P.realScaleType,M=P.scale;M.domain(w).range(m),hy(M);var T=vy(M,xt(xt({},b),{},{realScaleType:j})),C=xt(xt(xt({},b),T),{},{range:m,radius:y,realScaleType:j,scale:M,cx:f,cy:l,innerRadius:d,outerRadius:y,startAngle:c,endAngle:s});return xt(xt({},v),{},gy({},x,C))},{})},Vk=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},Zk=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=Vk({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:Yk(s),angleInRadian:s}},Jk=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},Qk=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},hp=function(t,r){var n=t.x,i=t.y,a=Zk({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=Jk(r),l=f.startAngle,p=f.endAngle,d=u,y;if(l<=p){for(;d>p;)d-=360;for(;d<l;)d+=360;y=d>=l&&d<=p}else{for(;d>l;)d-=360;for(;d<p;)d+=360;y=d>=p&&d<=l}return y?xt(xt({},r),{},{radius:o,angle:Qk(d,r)}):null},xy=function(t){return!q.isValidElement(t)&&!Z(t)&&typeof t!="boolean"?t.className:""};function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}var e2=["offset"];function t2(e){return a2(e)||i2(e)||n2(e)||r2()}function r2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function n2(e,t){if(e){if(typeof e=="string")return Yu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yu(e,t)}}function i2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function a2(e){if(Array.isArray(e))return Yu(e)}function Yu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function o2(e,t){if(e==null)return{};var r=u2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function u2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vp(Object(r),!0).forEach(function(n){c2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c2(e,t,r){return t=s2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s2(e){var t=l2(e,"string");return Qn(t)=="symbol"?t:t+""}function l2(e,t){if(Qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ei(){return ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ei.apply(this,arguments)}var f2=function(t){var r=t.value,n=t.formatter,i=ee(t.children)?r:t.children;return Z(n)?n(i):i},p2=function(t,r){var n=Re(r-t),i=Math.min(Math.abs(r-t),360);return n*i},d2=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,p=c.outerRadius,d=c.startAngle,y=c.endAngle,h=c.clockWise,v=(l+p)/2,x=p2(d,y),b=x>=0?1:-1,w,g;i==="insideStart"?(w=d+b*o,g=h):i==="insideEnd"?(w=y-b*o,g=!h):i==="end"&&(w=y+b*o,g=h),g=x<=0?g:!g;var m=ve(s,f,v,w),O=ve(s,f,v,w+(g?1:-1)*359),A="M".concat(m.x,",").concat(m.y,`
    A`).concat(v,",").concat(v,",0,1,").concat(g?0:1,`,
    `).concat(O.x,",").concat(O.y),P=ee(t.id)?cn("recharts-radial-line-"):t.id;return S.createElement("text",ei({},n,{dominantBaseline:"central",className:re("recharts-radial-bar-label",u)}),S.createElement("defs",null,S.createElement("path",{id:P,d:A})),S.createElement("textPath",{xlinkHref:"#".concat(P)},r))},h2=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,p=(f+l)/2;if(i==="outside"){var d=ve(o,u,s+n,p),y=d.x,h=d.y;return{x:y,y:h,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var v=(c+s)/2,x=ve(o,u,v,p),b=x.x,w=x.y;return{x:b,y:w,textAnchor:"middle",verticalAnchor:"middle"}},v2=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,p=l*i,d=l>0?"end":"start",y=l>0?"start":"end",h=s>=0?1:-1,v=h*i,x=h>0?"end":"start",b=h>0?"start":"end";if(a==="top"){var w={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:d};return Pe(Pe({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var g={x:u+s/2,y:c+f+p,textAnchor:"middle",verticalAnchor:y};return Pe(Pe({},g),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-v,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return Pe(Pe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var O={x:u+s+v,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return Pe(Pe({},O),n?{width:Math.max(n.x+n.width-O.x,0),height:f}:{})}var A=n?{width:s,height:f}:{};return a==="insideLeft"?Pe({x:u+v,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},A):a==="insideRight"?Pe({x:u+s-v,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},A):a==="insideTop"?Pe({x:u+s/2,y:c+p,textAnchor:"middle",verticalAnchor:y},A):a==="insideBottom"?Pe({x:u+s/2,y:c+f-p,textAnchor:"middle",verticalAnchor:d},A):a==="insideTopLeft"?Pe({x:u+v,y:c+p,textAnchor:b,verticalAnchor:y},A):a==="insideTopRight"?Pe({x:u+s-v,y:c+p,textAnchor:x,verticalAnchor:y},A):a==="insideBottomLeft"?Pe({x:u+v,y:c+f-p,textAnchor:b,verticalAnchor:d},A):a==="insideBottomRight"?Pe({x:u+s-v,y:c+f-p,textAnchor:x,verticalAnchor:d},A):rn(a)&&(z(a.x)||Jt(a.x))&&(z(a.y)||Jt(a.y))?Pe({x:u+Le(a.x,s),y:c+Le(a.y,f),textAnchor:"end",verticalAnchor:"end"},A):Pe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},A)},y2=function(t){return"cx"in t&&z(t.cx)};function je(e){var t=e.offset,r=t===void 0?5:t,n=o2(e,e2),i=Pe({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,p=i.textBreakAll;if(!a||ee(u)&&ee(c)&&!q.isValidElement(s)&&!Z(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var d;if(Z(s)){if(d=q.createElement(s,i),q.isValidElement(d))return d}else d=f2(i);var y=y2(a),h=Y(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return d2(i,d,h);var v=y?h2(i):v2(i);return S.createElement(or,ei({className:re("recharts-label",l)},h,v,{breakAll:p}),d)}je.displayName="Label";var wy=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,p=t.y,d=t.top,y=t.left,h=t.width,v=t.height,x=t.clockWise,b=t.labelViewBox;if(b)return b;if(z(h)&&z(v)){if(z(l)&&z(p))return{x:l,y:p,width:h,height:v};if(z(d)&&z(y))return{x:d,y,width:h,height:v}}return z(l)&&z(p)?{x:l,y:p,width:0,height:0}:z(r)&&z(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:x}:t.viewBox?t.viewBox:{}},m2=function(t,r){return t?t===!0?S.createElement(je,{key:"label-implicit",viewBox:r}):$e(t)?S.createElement(je,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===je?q.cloneElement(t,{key:"label-implicit",viewBox:r}):S.createElement(je,{key:"label-implicit",content:t,viewBox:r}):Z(t)?S.createElement(je,{key:"label-implicit",content:t,viewBox:r}):rn(t)?S.createElement(je,ei({viewBox:r},t,{key:"label-implicit"})):null:null},g2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=wy(t),o=Qe(i,je).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=m2(t.label,r||a);return[u].concat(t2(o))};je.parseViewBox=wy;je.renderCallByParent=g2;function b2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var x2=b2;const w2=se(x2);function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}var O2=["valueAccessor"],A2=["data","dataKey","clockWise","id","textBreakAll"];function S2(e){return T2(e)||$2(e)||_2(e)||P2()}function P2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _2(e,t){if(e){if(typeof e=="string")return Xu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xu(e,t)}}function $2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function T2(e){if(Array.isArray(e))return Xu(e)}function Xu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ma(){return ma=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ma.apply(this,arguments)}function yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yp(Object(r),!0).forEach(function(n){E2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function E2(e,t,r){return t=j2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function j2(e){var t=M2(e,"string");return ti(t)=="symbol"?t:t+""}function M2(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function gp(e,t){if(e==null)return{};var r=C2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function C2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var I2=function(t){return Array.isArray(t.value)?w2(t.value):t.value};function _t(e){var t=e.valueAccessor,r=t===void 0?I2:t,n=gp(e,O2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=gp(n,A2);return!i||!i.length?null:S.createElement(ie,{className:"recharts-label-list"},i.map(function(f,l){var p=ee(a)?r(f,l):_e(f&&f.payload,a),d=ee(u)?{}:{id:"".concat(u,"-").concat(l)};return S.createElement(je,ma({},Y(f,!0),s,d,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:je.parseViewBox(ee(o)?f:mp(mp({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}_t.displayName="LabelList";function k2(e,t){return e?e===!0?S.createElement(_t,{key:"labelList-implicit",data:t}):S.isValidElement(e)||Z(e)?S.createElement(_t,{key:"labelList-implicit",data:t,content:e}):rn(e)?S.createElement(_t,ma({data:t},e,{key:"labelList-implicit"})):null:null}function D2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Qe(n,_t).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=k2(e.label,t);return[a].concat(S2(i))}_t.renderCallByParent=D2;function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}function Vu(){return Vu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vu.apply(this,arguments)}function bp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bp(Object(r),!0).forEach(function(n){N2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function N2(e,t,r){return t=R2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function R2(e){var t=L2(e,"string");return ri(t)=="symbol"?t:t+""}function L2(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var B2=function(t,r){var n=Re(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},ki=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/ya,p=s?a:a+o*l,d=ve(r,n,f,p),y=ve(r,n,i,p),h=s?a-o*l:a,v=ve(r,n,f*Math.cos(l*ya),h);return{center:d,circleTangency:y,lineTangency:v,theta:l}},Oy=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=B2(o,u),s=o+c,f=ve(r,n,a,o),l=ve(r,n,a,s),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var d=ve(r,n,i,o),y=ve(r,n,i,s);p+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},F2=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Re(f-s),p=ki({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),d=p.circleTangency,y=p.lineTangency,h=p.theta,v=ki({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),x=v.circleTangency,b=v.lineTangency,w=v.theta,g=c?Math.abs(s-f):Math.abs(s-f)-h-w;if(g<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Oy({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(g>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
  `);if(i>0){var O=ki({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=O.circleTangency,P=O.lineTangency,j=O.theta,M=ki({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),T=M.circleTangency,C=M.lineTangency,_=M.theta,$=c?Math.abs(s-f):Math.abs(s-f)-j-_;if($<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(C.x,",").concat(C.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(T.x,",").concat(T.y,`
      A`).concat(i,",").concat(i,",0,").concat(+($>180),",").concat(+(l>0),",").concat(A.x,",").concat(A.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},z2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Ay=function(t){var r=xp(xp({},z2),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,p=r.className;if(o<a||f===l)return null;var d=re("recharts-sector",p),y=o-a,h=Le(u,y,0,!0),v;return h>0&&Math.abs(f-l)<360?v=F2({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(h,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):v=Oy({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),S.createElement("path",Vu({},Y(r,!0),{className:d,d:v,role:"img"}))};function ni(e){"@babel/helpers - typeof";return ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(e)}function Zu(){return Zu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zu.apply(this,arguments)}function wp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Op(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wp(Object(r),!0).forEach(function(n){W2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function W2(e,t,r){return t=U2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function U2(e){var t=q2(e,"string");return ni(t)=="symbol"?t:t+""}function q2(e,t){if(ni(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ni(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ap={curveBasisClosed:Uw,curveBasisOpen:qw,curveBasis:Ww,curveBumpX:Tw,curveBumpY:Ew,curveLinearClosed:Hw,curveLinear:Va,curveMonotoneX:Kw,curveMonotoneY:Gw,curveNatural:Yw,curveStep:Xw,curveStepAfter:Zw,curveStepBefore:Vw},Di=function(t){return t.x===+t.x&&t.y===+t.y},Sn=function(t){return t.x},Pn=function(t){return t.y},H2=function(t,r){if(Z(t))return t;var n="curve".concat(Ya(t));return(n==="curveMonotone"||n==="curveBump")&&r?Ap["".concat(n).concat(r==="vertical"?"Y":"X")]:Ap[n]||Va},K2=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=H2(n,u),l=s?a.filter(function(h){return Di(h)}):a,p;if(Array.isArray(o)){var d=s?o.filter(function(h){return Di(h)}):o,y=l.map(function(h,v){return Op(Op({},h),{},{base:d[v]})});return u==="vertical"?p=Pi().y(Pn).x1(Sn).x0(function(h){return h.base.x}):p=Pi().x(Sn).y1(Pn).y0(function(h){return h.base.y}),p.defined(Di).curve(f),p(y)}return u==="vertical"&&z(o)?p=Pi().y(Pn).x1(Sn).x0(o):z(o)?p=Pi().x(Sn).y1(Pn).y0(o):p=mh().x(Sn).y(Pn),p.defined(Di).curve(f),p(l)},$r=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?K2(t):i;return S.createElement("path",Zu({},Y(t,!1),Wi(t),{className:re("recharts-curve",r),d:o,ref:a}))},Sy={exports:{}},Py={exports:{}},ue={};/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=typeof Symbol=="function"&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,i=e?Symbol.for("react.strict_mode"):60108,a=e?Symbol.for("react.profiler"):60114,o=e?Symbol.for("react.provider"):60109,u=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,s=e?Symbol.for("react.concurrent_mode"):60111,f=e?Symbol.for("react.forward_ref"):60112,l=e?Symbol.for("react.suspense"):60113,p=e?Symbol.for("react.suspense_list"):60120,d=e?Symbol.for("react.memo"):60115,y=e?Symbol.for("react.lazy"):60116,h=e?Symbol.for("react.block"):60121,v=e?Symbol.for("react.fundamental"):60117,x=e?Symbol.for("react.responder"):60118,b=e?Symbol.for("react.scope"):60119;function w(E){return typeof E=="string"||typeof E=="function"||E===n||E===s||E===a||E===i||E===l||E===p||typeof E=="object"&&E!==null&&(E.$$typeof===y||E.$$typeof===d||E.$$typeof===o||E.$$typeof===u||E.$$typeof===f||E.$$typeof===v||E.$$typeof===x||E.$$typeof===b||E.$$typeof===h)}function g(E){if(typeof E=="object"&&E!==null){var le=E.$$typeof;switch(le){case t:var W=E.type;switch(W){case c:case s:case n:case a:case i:case l:return W;default:var pe=W&&W.$$typeof;switch(pe){case u:case f:case y:case d:case o:return pe;default:return le}}case r:return le}}}var m=c,O=s,A=u,P=o,j=t,M=f,T=n,C=y,_=d,$=r,k=a,I=i,D=l,R=!1;function L(E){return R||(R=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),U(E)||g(E)===c}function U(E){return g(E)===s}function B(E){return g(E)===u}function F(E){return g(E)===o}function H(E){return typeof E=="object"&&E!==null&&E.$$typeof===t}function J(E){return g(E)===f}function ne(E){return g(E)===n}function we(E){return g(E)===y}function Oe(E){return g(E)===d}function Ue(E){return g(E)===r}function K(E){return g(E)===a}function V(E){return g(E)===i}function Q(E){return g(E)===l}ue.AsyncMode=m,ue.ConcurrentMode=O,ue.ContextConsumer=A,ue.ContextProvider=P,ue.Element=j,ue.ForwardRef=M,ue.Fragment=T,ue.Lazy=C,ue.Memo=_,ue.Portal=$,ue.Profiler=k,ue.StrictMode=I,ue.Suspense=D,ue.isAsyncMode=L,ue.isConcurrentMode=U,ue.isContextConsumer=B,ue.isContextProvider=F,ue.isElement=H,ue.isForwardRef=J,ue.isFragment=ne,ue.isLazy=we,ue.isMemo=Oe,ue.isPortal=Ue,ue.isProfiler=K,ue.isStrictMode=V,ue.isSuspense=Q,ue.isValidElementType=w,ue.typeOf=g})();Py.exports=ue;var _y=Py.exports;/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var Sp=Object.getOwnPropertySymbols,G2=Object.prototype.hasOwnProperty,Y2=Object.prototype.propertyIsEnumerable;function X2(e){if(e==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}function V2(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de",Object.getOwnPropertyNames(e)[0]==="5")return!1;for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;var n=Object.getOwnPropertyNames(t).map(function(a){return t[a]});if(n.join("")!=="**********")return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach(function(a){i[a]=a}),Object.keys(Object.assign({},i)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}var Z2=V2()?Object.assign:function(e,t){for(var r,n=X2(e),i,a=1;a<arguments.length;a++){r=Object(arguments[a]);for(var o in r)G2.call(r,o)&&(n[o]=r[o]);if(Sp){i=Sp(r);for(var u=0;u<i.length;u++)Y2.call(r,i[u])&&(n[i[u]]=r[i[u]])}}return n},J2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",$y=J2,Ty=Function.call.bind(Object.prototype.hasOwnProperty),Ju=function(){};{var Q2=$y,Qu={},eD=Ty;Ju=function(e){var t="Warning: "+e;typeof console<"u"&&console.error(t);try{throw new Error(t)}catch{}}}function Ey(e,t,r,n,i){for(var a in e)if(eD(e,a)){var o;try{if(typeof e[a]!="function"){var u=Error((n||"React class")+": "+r+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[a]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw u.name="Invariant Violation",u}o=e[a](t,a,n,r,null,Q2)}catch(s){o=s}if(o&&!(o instanceof Error)&&Ju((n||"React class")+": type specification of "+r+" `"+a+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof o+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),o instanceof Error&&!(o.message in Qu)){Qu[o.message]=!0;var c=i?i():"";Ju("Failed "+r+" type: "+o.message+(c??""))}}}Ey.resetWarningCache=function(){Qu={}};var tD=Ey,rD=_y,nD=Z2,yr=$y,Jo=Ty,Pp=tD,gr=function(){};gr=function(e){var t="Warning: "+e;typeof console<"u"&&console.error(t);try{throw new Error(t)}catch{}};function Ni(){return null}var iD=function(e,t){var r=typeof Symbol=="function"&&Symbol.iterator,n="@@iterator";function i(_){var $=_&&(r&&_[r]||_[n]);if(typeof $=="function")return $}var a="<<anonymous>>",o={array:f("array"),bigint:f("bigint"),bool:f("boolean"),func:f("function"),number:f("number"),object:f("object"),string:f("string"),symbol:f("symbol"),any:l(),arrayOf:p,element:d(),elementType:y(),instanceOf:h,node:w(),objectOf:x,oneOf:v,oneOfType:b,shape:m,exact:O};function u(_,$){return _===$?_!==0||1/_===1/$:_!==_&&$!==$}function c(_,$){this.message=_,this.data=$&&typeof $=="object"?$:{},this.stack=""}c.prototype=Error.prototype;function s(_){var $={},k=0;function I(R,L,U,B,F,H,J){if(B=B||a,H=H||U,J!==yr){if(t){var ne=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw ne.name="Invariant Violation",ne}else if(typeof console<"u"){var we=B+":"+U;!$[we]&&k<3&&(gr("You are manually calling a React.PropTypes validation function for the `"+H+"` prop on `"+B+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),$[we]=!0,k++)}}return L[U]==null?R?L[U]===null?new c("The "+F+" `"+H+"` is marked as required "+("in `"+B+"`, but its value is `null`.")):new c("The "+F+" `"+H+"` is marked as required in "+("`"+B+"`, but its value is `undefined`.")):null:_(L,U,B,F,H)}var D=I.bind(null,!1);return D.isRequired=I.bind(null,!0),D}function f(_){function $(k,I,D,R,L,U){var B=k[I],F=j(B);if(F!==_){var H=M(B);return new c("Invalid "+R+" `"+L+"` of type "+("`"+H+"` supplied to `"+D+"`, expected ")+("`"+_+"`."),{expectedType:_})}return null}return s($)}function l(){return s(Ni)}function p(_){function $(k,I,D,R,L){if(typeof _!="function")return new c("Property `"+L+"` of component `"+D+"` has invalid PropType notation inside arrayOf.");var U=k[I];if(!Array.isArray(U)){var B=j(U);return new c("Invalid "+R+" `"+L+"` of type "+("`"+B+"` supplied to `"+D+"`, expected an array."))}for(var F=0;F<U.length;F++){var H=_(U,F,D,R,L+"["+F+"]",yr);if(H instanceof Error)return H}return null}return s($)}function d(){function _($,k,I,D,R){var L=$[k];if(!e(L)){var U=j(L);return new c("Invalid "+D+" `"+R+"` of type "+("`"+U+"` supplied to `"+I+"`, expected a single ReactElement."))}return null}return s(_)}function y(){function _($,k,I,D,R){var L=$[k];if(!rD.isValidElementType(L)){var U=j(L);return new c("Invalid "+D+" `"+R+"` of type "+("`"+U+"` supplied to `"+I+"`, expected a single ReactElement type."))}return null}return s(_)}function h(_){function $(k,I,D,R,L){if(!(k[I]instanceof _)){var U=_.name||a,B=C(k[I]);return new c("Invalid "+R+" `"+L+"` of type "+("`"+B+"` supplied to `"+D+"`, expected ")+("instance of `"+U+"`."))}return null}return s($)}function v(_){if(!Array.isArray(_))return arguments.length>1?gr("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):gr("Invalid argument supplied to oneOf, expected an array."),Ni;function $(k,I,D,R,L){for(var U=k[I],B=0;B<_.length;B++)if(u(U,_[B]))return null;var F=JSON.stringify(_,function(J,ne){var we=M(ne);return we==="symbol"?String(ne):ne});return new c("Invalid "+R+" `"+L+"` of value `"+String(U)+"` "+("supplied to `"+D+"`, expected one of "+F+"."))}return s($)}function x(_){function $(k,I,D,R,L){if(typeof _!="function")return new c("Property `"+L+"` of component `"+D+"` has invalid PropType notation inside objectOf.");var U=k[I],B=j(U);if(B!=="object")return new c("Invalid "+R+" `"+L+"` of type "+("`"+B+"` supplied to `"+D+"`, expected an object."));for(var F in U)if(Jo(U,F)){var H=_(U,F,D,R,L+"."+F,yr);if(H instanceof Error)return H}return null}return s($)}function b(_){if(!Array.isArray(_))return gr("Invalid argument supplied to oneOfType, expected an instance of array."),Ni;for(var $=0;$<_.length;$++){var k=_[$];if(typeof k!="function")return gr("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+T(k)+" at index "+$+"."),Ni}function I(D,R,L,U,B){for(var F=[],H=0;H<_.length;H++){var J=_[H],ne=J(D,R,L,U,B,yr);if(ne==null)return null;ne.data&&Jo(ne.data,"expectedType")&&F.push(ne.data.expectedType)}var we=F.length>0?", expected one of type ["+F.join(", ")+"]":"";return new c("Invalid "+U+" `"+B+"` supplied to "+("`"+L+"`"+we+"."))}return s(I)}function w(){function _($,k,I,D,R){return A($[k])?null:new c("Invalid "+D+" `"+R+"` supplied to "+("`"+I+"`, expected a ReactNode."))}return s(_)}function g(_,$,k,I,D){return new c((_||"React class")+": "+$+" type `"+k+"."+I+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+D+"`.")}function m(_){function $(k,I,D,R,L){var U=k[I],B=j(U);if(B!=="object")return new c("Invalid "+R+" `"+L+"` of type `"+B+"` "+("supplied to `"+D+"`, expected `object`."));for(var F in _){var H=_[F];if(typeof H!="function")return g(D,R,L,F,M(H));var J=H(U,F,D,R,L+"."+F,yr);if(J)return J}return null}return s($)}function O(_){function $(k,I,D,R,L){var U=k[I],B=j(U);if(B!=="object")return new c("Invalid "+R+" `"+L+"` of type `"+B+"` "+("supplied to `"+D+"`, expected `object`."));var F=nD({},k[I],_);for(var H in F){var J=_[H];if(Jo(_,H)&&typeof J!="function")return g(D,R,L,H,M(J));if(!J)return new c("Invalid "+R+" `"+L+"` key `"+H+"` supplied to `"+D+"`.\nBad object: "+JSON.stringify(k[I],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(_),null,"  "));var ne=J(U,H,D,R,L+"."+H,yr);if(ne)return ne}return null}return s($)}function A(_){switch(typeof _){case"number":case"string":case"undefined":return!0;case"boolean":return!_;case"object":if(Array.isArray(_))return _.every(A);if(_===null||e(_))return!0;var $=i(_);if($){var k=$.call(_),I;if($!==_.entries){for(;!(I=k.next()).done;)if(!A(I.value))return!1}else for(;!(I=k.next()).done;){var D=I.value;if(D&&!A(D[1]))return!1}}else return!1;return!0;default:return!1}}function P(_,$){return _==="symbol"?!0:$?$["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&$ instanceof Symbol:!1}function j(_){var $=typeof _;return Array.isArray(_)?"array":_ instanceof RegExp?"object":P($,_)?"symbol":$}function M(_){if(typeof _>"u"||_===null)return""+_;var $=j(_);if($==="object"){if(_ instanceof Date)return"date";if(_ instanceof RegExp)return"regexp"}return $}function T(_){var $=M(_);switch($){case"array":case"object":return"an "+$;case"boolean":case"date":case"regexp":return"a "+$;default:return $}}function C(_){return!_.constructor||!_.constructor.name?a:_.constructor.name}return o.checkPropTypes=Pp,o.resetWarningCache=Pp.resetWarningCache,o.PropTypes=o,o};{var aD=_y,oD=!0;Sy.exports=iD(aD.isElement,oD)}var uD=Sy.exports;const te=se(uD);var cD=Object.getOwnPropertyNames,sD=Object.getOwnPropertySymbols,lD=Object.prototype.hasOwnProperty;function _p(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Ri(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function $p(e){return cD(e).concat(sD(e))}var jy=Object.hasOwn||function(e,t){return lD.call(e,t)};function vn(e,t){return e||t?e===t:e===t||e!==e&&t!==t}var My="_owner",Tp=Object.getOwnPropertyDescriptor,Ep=Object.keys;function fD(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function pD(e,t){return vn(e.getTime(),t.getTime())}function jp(e,t,r){if(e.size!==t.size)return!1;for(var n={},i=e.entries(),a=0,o,u;(o=i.next())&&!o.done;){for(var c=t.entries(),s=!1,f=0;(u=c.next())&&!u.done;){var l=o.value,p=l[0],d=l[1],y=u.value,h=y[0],v=y[1];!s&&!n[f]&&(s=r.equals(p,h,a,f,e,t,r)&&r.equals(d,v,p,h,e,t,r))&&(n[f]=!0),f++}if(!s)return!1;a++}return!0}function dD(e,t,r){var n=Ep(e),i=n.length;if(Ep(t).length!==i)return!1;for(var a;i-- >0;)if(a=n[i],a===My&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof||!jy(t,a)||!r.equals(e[a],t[a],a,a,e,t,r))return!1;return!0}function _n(e,t,r){var n=$p(e),i=n.length;if($p(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],a===My&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof||!jy(t,a)||!r.equals(e[a],t[a],a,a,e,t,r)||(o=Tp(e,a),u=Tp(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function hD(e,t){return vn(e.valueOf(),t.valueOf())}function vD(e,t){return e.source===t.source&&e.flags===t.flags}function Mp(e,t,r){if(e.size!==t.size)return!1;for(var n={},i=e.values(),a,o;(a=i.next())&&!a.done;){for(var u=t.values(),c=!1,s=0;(o=u.next())&&!o.done;)!c&&!n[s]&&(c=r.equals(a.value,o.value,a.value,o.value,e,t,r))&&(n[s]=!0),s++;if(!c)return!1}return!0}function yD(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}var mD="[object Arguments]",gD="[object Boolean]",bD="[object Date]",xD="[object Map]",wD="[object Number]",OD="[object Object]",AD="[object RegExp]",SD="[object Set]",PD="[object String]",_D=Array.isArray,Cp=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Ip=Object.assign,$D=Object.prototype.toString.call.bind(Object.prototype.toString);function TD(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areMapsEqual,i=e.areObjectsEqual,a=e.arePrimitiveWrappersEqual,o=e.areRegExpsEqual,u=e.areSetsEqual,c=e.areTypedArraysEqual;return function(f,l,p){if(f===l)return!0;if(f==null||l==null||typeof f!="object"||typeof l!="object")return f!==f&&l!==l;var d=f.constructor;if(d!==l.constructor)return!1;if(d===Object)return i(f,l,p);if(_D(f))return t(f,l,p);if(Cp!=null&&Cp(f))return c(f,l,p);if(d===Date)return r(f,l,p);if(d===RegExp)return o(f,l,p);if(d===Map)return n(f,l,p);if(d===Set)return u(f,l,p);var y=$D(f);return y===bD?r(f,l,p):y===AD?o(f,l,p):y===xD?n(f,l,p):y===SD?u(f,l,p):y===OD?typeof f.then!="function"&&typeof l.then!="function"&&i(f,l,p):y===mD?i(f,l,p):y===gD||y===wD||y===PD?a(f,l,p):!1}}function ED(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?_n:fD,areDatesEqual:pD,areMapsEqual:n?_p(jp,_n):jp,areObjectsEqual:n?_n:dD,arePrimitiveWrappersEqual:hD,areRegExpsEqual:vD,areSetsEqual:n?_p(Mp,_n):Mp,areTypedArraysEqual:n?_n:yD};if(r&&(i=Ip({},i,r(i))),t){var a=Ri(i.areArraysEqual),o=Ri(i.areMapsEqual),u=Ri(i.areObjectsEqual),c=Ri(i.areSetsEqual);i=Ip({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function jD(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function MD(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,p=l===void 0?t?new WeakMap:void 0:l,d=f.meta;return r(c,s,{cache:p,equals:i,meta:d,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var CD=Wt();Wt({strict:!0});Wt({circular:!0});Wt({circular:!0,strict:!0});Wt({createInternalComparator:function(){return vn}});Wt({strict:!0,createInternalComparator:function(){return vn}});Wt({circular:!0,createInternalComparator:function(){return vn}});Wt({circular:!0,createInternalComparator:function(){return vn},strict:!0});function Wt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=ED(e),c=TD(u),s=n?n(c):jD(c);return MD({circular:r,comparator:c,createState:i,equals:s,strict:o})}function ID(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function kp(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):ID(i)};requestAnimationFrame(n)}function ec(e){"@babel/helpers - typeof";return ec=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ec(e)}function kD(e){return LD(e)||RD(e)||ND(e)||DD()}function DD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ND(e,t){if(e){if(typeof e=="string")return Dp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dp(e,t)}}function Dp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function RD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function LD(e){if(Array.isArray(e))return e}function BD(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=kD(o),c=u[0],s=u.slice(1);if(typeof c=="number"){kp(i.bind(null,s),c);return}i(c),kp(i.bind(null,s));return}ec(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function ii(e){"@babel/helpers - typeof";return ii=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(e)}function Np(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Np(Object(r),!0).forEach(function(n){Cy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Np(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Cy(e,t,r){return t=FD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FD(e){var t=zD(e,"string");return ii(t)==="symbol"?t:String(t)}function zD(e,t){if(ii(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ii(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var WD=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},UD=function(t){return t},qD=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},Cn=function(t,r){return Object.keys(r).reduce(function(n,i){return Rp(Rp({},n),{},Cy({},i,t(i,r[i])))},{})},Lp=function(t,r,n){return t.map(function(i){return"".concat(qD(i)," ").concat(r,"ms ").concat(n)}).join(",")},ga=function(t,r,n,i,a,o,u,c){if(typeof console<"u"&&console.warn&&(r===void 0&&console.warn("LogUtils requires an error message argument"),!t))if(r===void 0)console.warn("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[n,i,a,o,u,c],f=0;console.warn(r.replace(/%s/g,function(){return s[f++]}))}};function HD(e,t){return YD(e)||GD(e,t)||Iy(e,t)||KD()}function KD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function YD(e){if(Array.isArray(e))return e}function XD(e){return JD(e)||ZD(e)||Iy(e)||VD()}function VD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Iy(e,t){if(e){if(typeof e=="string")return tc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tc(e,t)}}function ZD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function JD(e){if(Array.isArray(e))return tc(e)}function tc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ba=1e-4,ky=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Dy=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Bp=function(t,r){return function(n){var i=ky(t,r);return Dy(i,n)}},QD=function(t,r){return function(n){var i=ky(t,r),a=[].concat(XD(i.map(function(o,u){return o*u}).slice(1)),[0]);return Dy(a,n)}},Fp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(v){return parseFloat(v)}),f=HD(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}else ga(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",r)}}ga([i,o,a,u].every(function(v){return typeof v=="number"&&v>=0&&v<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",r);var l=Bp(i,o),p=Bp(a,u),d=QD(i,o),y=function(x){return x>1?1:x<0?0:x},h=function(x){for(var b=x>1?1:x,w=b,g=0;g<8;++g){var m=l(w)-b,O=d(w);if(Math.abs(m-b)<ba||O<ba)return p(w);w=y(w-m/O)}return p(w)};return h.isStepper=!1,h},eN=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,p){var d=-(f-l)*n,y=p*a,h=p+(d-y)*u/1e3,v=p*u/1e3+f;return Math.abs(v-l)<ba&&Math.abs(h)<ba?[l,0]:[v,h]};return c.isStepper=!0,c.dt=u,c},tN=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Fp(i);case"spring":return eN();default:if(i.split("(")[0]==="cubic-bezier")return Fp(i);ga(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",r)}return typeof i=="function"?i:(ga(!1,"[configEasing]: first argument type should be function or string, instead received %s",r),null)};function ai(e){"@babel/helpers - typeof";return ai=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ai(e)}function zp(e){return iN(e)||nN(e)||Ny(e)||rN()}function rN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function iN(e){if(Array.isArray(e))return nc(e)}function Wp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wp(Object(r),!0).forEach(function(n){rc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rc(e,t,r){return t=aN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aN(e){var t=oN(e,"string");return ai(t)==="symbol"?t:String(t)}function oN(e,t){if(ai(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ai(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uN(e,t){return lN(e)||sN(e,t)||Ny(e,t)||cN()}function cN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ny(e,t){if(e){if(typeof e=="string")return nc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(e,t)}}function nc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function sN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function lN(e){if(Array.isArray(e))return e}var xa=function(t,r,n){return t+(r-t)*n},ic=function(t){var r=t.from,n=t.to;return r!==n},fN=function e(t,r,n){var i=Cn(function(a,o){if(ic(o)){var u=t(o.from,o.to,o.velocity),c=uN(u,2),s=c[0],f=c[1];return Ce(Ce({},o),{},{from:s,velocity:f})}return o},r);return n<1?Cn(function(a,o){return ic(o)?Ce(Ce({},o),{},{velocity:xa(o.velocity,i[a].velocity,n),from:xa(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const pN=function(e,t,r,n,i){var a=WD(e,t),o=a.reduce(function(v,x){return Ce(Ce({},v),{},rc({},x,[e[x],t[x]]))},{}),u=a.reduce(function(v,x){return Ce(Ce({},v),{},rc({},x,{from:e[x],velocity:0,to:t[x]}))},{}),c=-1,s,f,l=function(){return null},p=function(){return Cn(function(x,b){return b.from},u)},d=function(){return!Object.values(u).filter(ic).length},y=function(x){s||(s=x);var b=x-s,w=b/r.dt;u=fN(r,u,w),i(Ce(Ce(Ce({},e),t),p())),s=x,d()||(c=requestAnimationFrame(l))},h=function(x){f||(f=x);var b=(x-f)/n,w=Cn(function(m,O){return xa.apply(void 0,zp(O).concat([r(b)]))},o);if(i(Ce(Ce(Ce({},e),t),w)),b<1)c=requestAnimationFrame(l);else{var g=Cn(function(m,O){return xa.apply(void 0,zp(O).concat([r(1)]))},o);i(Ce(Ce(Ce({},e),t),g))}};return l=r.isStepper?y:h,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}var dN=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function hN(e,t){if(e==null)return{};var r=vN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vN(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Qo(e){return bN(e)||gN(e)||mN(e)||yN()}function yN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mN(e,t){if(e){if(typeof e=="string")return ac(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ac(e,t)}}function gN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function bN(e){if(Array.isArray(e))return ac(e)}function ac(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Up(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Up(Object(r),!0).forEach(function(n){Tn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Up(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Tn(e,t,r){return t=Ry(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ry(n.key),n)}}function ON(e,t,r){return t&&wN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ry(e){var t=AN(e,"string");return Lr(t)==="symbol"?t:String(t)}function AN(e,t){if(Lr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Lr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function SN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oc(e,t)}function oc(e,t){return oc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},oc(e,t)}function PN(e){var t=_N();return function(){var n=wa(e),i;if(t){var a=wa(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return uc(this,i)}}function uc(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cc(e)}function cc(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _N(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function wa(e){return wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},wa(e)}var dt=function(e){SN(r,e);var t=PN(r);function r(n,i){var a;xN(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,p=o.children,d=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(cc(a)),a.changeStyle=a.changeStyle.bind(cc(a)),!u||d<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),uc(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof p=="function")return a.state={style:s},uc(a);a.state={style:c?Tn({},c,s):s}}else a.state={style:{}};return a}return ON(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,p=this.state.style;if(u){if(!o){var d={style:c?Tn({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(d);return}if(!(CD(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var h=y||s?l:i.to;if(this.state&&p){var v={style:c?Tn({},c,h):h};(c&&p[c]!==h||!c&&p!==h)&&this.setState(v)}this.runAnimation(rt(rt({},this.props),{},{from:h,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,p=i.onAnimationStart,d=pN(o,u,tN(s),c,this.changeStyle),y=function(){a.stopJSAnimation=d()};this.manager.start([p,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,p=l===void 0?0:l,d=function(h,v,x){if(x===0)return h;var b=v.duration,w=v.easing,g=w===void 0?"ease":w,m=v.style,O=v.properties,A=v.onAnimationEnd,P=x>0?o[x-1]:v,j=O||Object.keys(m);if(typeof g=="function"||g==="spring")return[].concat(Qo(h),[a.runJSAnimation.bind(a,{from:P.style,to:m,duration:b,easing:g}),b]);var M=Lp(j,b,g),T=rt(rt(rt({},P.style),m),{},{transition:M});return[].concat(Qo(h),[T,b,A]).filter(UD)};return this.manager.start([c].concat(Qo(o.reduce(d,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=BD());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,p=i.steps,d=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof d=="function"||s==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var h=u?Tn({},u,c):c,v=Lp(Object.keys(h),o,s);y.start([f,a,rt(rt({},h),{},{transition:v}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=hN(i,dN),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(d){var y=d.props,h=y.style,v=h===void 0?{}:h,x=y.className,b=q.cloneElement(d,rt(rt({},c),{},{style:rt(rt({},v),f),className:x}));return b};return s===1?l(q.Children.only(a)):S.createElement("div",null,q.Children.map(a,function(p){return l(p)}))}}]),r}(q.PureComponent);dt.displayName="Animate";dt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};dt.propTypes={from:te.oneOfType([te.object,te.string]),to:te.oneOfType([te.object,te.string]),attributeName:te.string,duration:te.number,begin:te.number,easing:te.oneOfType([te.string,te.func]),steps:te.arrayOf(te.shape({duration:te.number.isRequired,style:te.object.isRequired,easing:te.oneOfType([te.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),te.func]),properties:te.arrayOf("string"),onAnimationEnd:te.func})),children:te.oneOfType([te.node,te.func]),isActive:te.bool,canBegin:te.bool,onAnimationEnd:te.func,shouldReAnimate:te.bool,onAnimationStart:te.func,onAnimationReStart:te.func};te.object,te.object,te.object,te.element;te.object,te.object,te.object,te.oneOfType([te.array,te.element]),te.any;function oi(e){"@babel/helpers - typeof";return oi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oi(e)}function Oa(){return Oa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oa.apply(this,arguments)}function $N(e,t){return MN(e)||jN(e,t)||EN(e,t)||TN()}function TN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function EN(e,t){if(e){if(typeof e=="string")return qp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qp(e,t)}}function qp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function MN(e){if(Array.isArray(e))return e}function Hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hp(Object(r),!0).forEach(function(n){CN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function CN(e,t,r){return t=IN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function IN(e){var t=kN(e,"string");return oi(t)=="symbol"?t:t+""}function kN(e,t){if(oi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(oi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Gp=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],p=0,d=4;p<d;p++)l[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},DN=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),p=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=p}return!1},NN={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},js=function(t){var r=Kp(Kp({},NN),t),n=q.useRef(),i=q.useState(-1),a=$N(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var g=n.current.getTotalLength();g&&u(g)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,p=r.radius,d=r.className,y=r.animationEasing,h=r.animationDuration,v=r.animationBegin,x=r.isAnimationActive,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=re("recharts-rectangle",d);return b?S.createElement(dt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:h,animationEasing:y,isActive:b},function(g){var m=g.width,O=g.height,A=g.x,P=g.y;return S.createElement(dt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:h,isActive:x,easing:y},S.createElement("path",Oa({},Y(r,!0),{className:w,d:Gp(A,P,m,O,p),ref:n})))}):S.createElement("path",Oa({},Y(r,!0),{className:w,d:Gp(c,s,f,l,p)}))},RN=["points","className","baseLinePoints","connectNulls"];function xr(){return xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xr.apply(this,arguments)}function LN(e,t){if(e==null)return{};var r=BN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Yp(e){return UN(e)||WN(e)||zN(e)||FN()}function FN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zN(e,t){if(e){if(typeof e=="string")return sc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sc(e,t)}}function WN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function UN(e){if(Array.isArray(e))return sc(e)}function sc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Xp=function(t){return t&&t.x===+t.x&&t.y===+t.y},qN=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){Xp(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),Xp(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},In=function(t,r){var n=qN(t);r&&(n=[n.reduce(function(a,o){return[].concat(Yp(a),Yp(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},HN=function(t,r,n){var i=In(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(In(r.reverse(),n).slice(1))},KN=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=LN(t,RN);if(!r||!r.length)return null;var u=re("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=HN(r,i,a);return S.createElement("g",{className:u},S.createElement("path",xr({},Y(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?S.createElement("path",xr({},Y(o,!0),{fill:"none",d:In(r,a)})):null,c?S.createElement("path",xr({},Y(o,!0),{fill:"none",d:In(i,a)})):null)}var f=In(r,a);return S.createElement("path",xr({},Y(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function lc(){return lc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lc.apply(this,arguments)}var po=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=re("recharts-dot",a);return r===+r&&n===+n&&i===+i?S.createElement("circle",lc({},Y(t,!1),Wi(t),{className:o,cx:r,cy:n,r:i})):null};function ui(e){"@babel/helpers - typeof";return ui=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ui(e)}var GN=["x","y","top","left","width","height","className"];function fc(){return fc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fc.apply(this,arguments)}function Vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function YN(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vp(Object(r),!0).forEach(function(n){XN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XN(e,t,r){return t=VN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function VN(e){var t=ZN(e,"string");return ui(t)=="symbol"?t:t+""}function ZN(e,t){if(ui(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ui(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function JN(e,t){if(e==null)return{};var r=QN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function QN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var eR=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},tR=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,p=t.height,d=p===void 0?0:p,y=t.className,h=JN(t,GN),v=YN({x:n,y:a,top:u,left:s,width:l,height:d},h);return!z(n)||!z(a)||!z(l)||!z(d)||!z(u)||!z(s)?null:S.createElement("path",fc({},Y(v,!0),{className:re("recharts-cross",y),d:eR(n,a,l,d,u,s)}))},rR=uo,nR=Hv,iR=vt;function aR(e,t){return e&&e.length?rR(e,iR(t),nR):void 0}var oR=aR;const uR=se(oR);var cR=uo,sR=vt,lR=Kv;function fR(e,t){return e&&e.length?cR(e,sR(t),lR):void 0}var pR=fR;const dR=se(pR);var hR=["cx","cy","angle","ticks","axisLine"],vR=["ticks","tick","angle","tickFormatter","stroke"];function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function kn(){return kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kn.apply(this,arguments)}function Zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zp(Object(r),!0).forEach(function(n){ho(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Jp(e,t){if(e==null)return{};var r=yR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function mR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,By(n.key),n)}}function gR(e,t,r){return t&&Qp(e.prototype,t),r&&Qp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function bR(e,t,r){return t=Aa(t),xR(e,Ly()?Reflect.construct(t,r||[],Aa(e).constructor):t.apply(e,r))}function xR(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wR(e)}function wR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ly(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ly=function(){return!!e})()}function Aa(e){return Aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Aa(e)}function OR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pc(e,t)}function pc(e,t){return pc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},pc(e,t)}function ho(e,t,r){return t=By(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function By(e){var t=AR(e,"string");return Br(t)=="symbol"?t:t+""}function AR(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var vo=function(e){function t(){return mR(this,t),bR(this,t,arguments)}return OR(t,e),gR(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return ve(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=uR(u,function(f){return f.coordinate||0}),s=dR(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=Jp(n,hR),f=u.reduce(function(y,h){return[Math.min(y[0],h.coordinate),Math.max(y[1],h.coordinate)]},[1/0,-1/0]),l=ve(i,a,f[0],o),p=ve(i,a,f[1],o),d=Kt(Kt(Kt({},Y(s,!1)),{},{fill:"none"},Y(c,!1)),{},{x1:l.x,y1:l.y,x2:p.x,y2:p.y});return S.createElement("line",kn({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=Jp(i,vR),l=this.getTickTextAnchor(),p=Y(f,!1),d=Y(o,!1),y=a.map(function(h,v){var x=n.getTickValueCoord(h),b=Kt(Kt(Kt(Kt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(x.x,", ").concat(x.y,")")},p),{},{stroke:"none",fill:s},d),{},{index:v},x),{},{payload:h});return S.createElement(ie,kn({className:re("recharts-polar-radius-axis-tick",xy(o)),key:"tick-".concat(h.coordinate)},ar(n.props,h,v)),t.renderTickItem(o,b,c?c(h.value,v):h.value))});return S.createElement(ie,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:S.createElement(ie,{className:re("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),je.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):Z(n)?o=n(i):o=S.createElement(or,kn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(q.PureComponent);ho(vo,"displayName","PolarRadiusAxis");ho(vo,"axisType","radiusAxis");ho(vo,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function Zt(){return Zt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zt.apply(this,arguments)}function ed(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ed(Object(r),!0).forEach(function(n){yo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ed(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function td(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zy(n.key),n)}}function PR(e,t,r){return t&&td(e.prototype,t),r&&td(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _R(e,t,r){return t=Sa(t),$R(e,Fy()?Reflect.construct(t,r||[],Sa(e).constructor):t.apply(e,r))}function $R(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TR(e)}function TR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Fy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Fy=function(){return!!e})()}function Sa(e){return Sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Sa(e)}function ER(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dc(e,t)}function dc(e,t){return dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},dc(e,t)}function yo(e,t,r){return t=zy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zy(e){var t=jR(e,"string");return Fr(t)=="symbol"?t:t+""}function jR(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var MR=Math.PI/180,rd=1e-5,mo=function(e){function t(){return SR(this,t),_R(this,t,arguments)}return ER(t,e),PR(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=ve(a,o,u,n.coordinate),p=ve(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*MR),o;return a>rd?o=i==="outer"?"start":"end":a<-rd?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Gt(Gt({},Y(this.props,!1)),{},{fill:"none"},Y(u,!1));if(c==="circle")return S.createElement(po,Zt({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(p){return ve(i,a,o,p.coordinate)});return S.createElement(KN,Zt({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=Y(this.props,!1),l=Y(o,!1),p=Gt(Gt({},f),{},{fill:"none"},Y(u,!1)),d=a.map(function(y,h){var v=n.getTickLineCoord(y),x=n.getTickTextAnchor(y),b=Gt(Gt(Gt({textAnchor:x},f),{},{stroke:"none",fill:s},l),{},{index:h,payload:y,x:v.x2,y:v.y2});return S.createElement(ie,Zt({className:re("recharts-polar-angle-axis-tick",xy(o)),key:"tick-".concat(y.coordinate)},ar(n.props,y,h)),u&&S.createElement("line",Zt({className:"recharts-polar-angle-axis-tick-line"},p,v)),o&&t.renderTickItem(o,b,c?c(y.value,h):y.value))});return S.createElement(ie,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:S.createElement(ie,{className:re("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):Z(n)?o=n(i):o=S.createElement(or,Zt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(q.PureComponent);yo(mo,"displayName","PolarAngleAxis");yo(mo,"axisType","angleAxis");yo(mo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var CR=zh,IR=CR(Object.getPrototypeOf,Object),kR=IR,DR=jt,NR=kR,RR=Mt,LR="[object Object]",BR=Function.prototype,FR=Object.prototype,Wy=BR.toString,zR=FR.hasOwnProperty,WR=Wy.call(Object);function UR(e){if(!RR(e)||DR(e)!=LR)return!1;var t=NR(e);if(t===null)return!0;var r=zR.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Wy.call(r)==WR}var qR=UR;const HR=se(qR);var KR=jt,GR=Mt,YR="[object Boolean]";function XR(e){return e===!0||e===!1||GR(e)&&KR(e)==YR}var VR=XR;const ZR=se(VR);function ci(e){"@babel/helpers - typeof";return ci=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(e)}function Pa(){return Pa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pa.apply(this,arguments)}function JR(e,t){return rL(e)||tL(e,t)||eL(e,t)||QR()}function QR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eL(e,t){if(e){if(typeof e=="string")return nd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nd(e,t)}}function nd(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tL(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function rL(e){if(Array.isArray(e))return e}function id(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ad(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?id(Object(r),!0).forEach(function(n){nL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):id(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nL(e,t,r){return t=iL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iL(e){var t=aL(e,"string");return ci(t)=="symbol"?t:t+""}function aL(e,t){if(ci(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ci(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var od=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},oL={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},uL=function(t){var r=ad(ad({},oL),t),n=q.useRef(),i=q.useState(-1),a=JR(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,p=r.height,d=r.className,y=r.animationEasing,h=r.animationDuration,v=r.animationBegin,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||p!==+p||f===0&&l===0||p===0)return null;var b=re("recharts-trapezoid",d);return x?S.createElement(dt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:p,x:c,y:s},duration:h,animationEasing:y,isActive:x},function(w){var g=w.upperWidth,m=w.lowerWidth,O=w.height,A=w.x,P=w.y;return S.createElement(dt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:h,easing:y},S.createElement("path",Pa({},Y(r,!0),{className:b,d:od(A,P,g,m,O),ref:n})))}):S.createElement("g",null,S.createElement("path",Pa({},Y(r,!0),{className:b,d:od(c,s,f,l,p)})))},cL=["option","shapeType","propTransformer","activeClassName","isActive"];function si(e){"@babel/helpers - typeof";return si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(e)}function sL(e,t){if(e==null)return{};var r=lL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _a(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(n){fL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fL(e,t,r){return t=pL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pL(e){var t=dL(e,"string");return si(t)=="symbol"?t:t+""}function dL(e,t){if(si(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(si(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function hL(e,t){return _a(_a({},t),e)}function vL(e,t){return e==="symbols"}function cd(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return S.createElement(js,r);case"trapezoid":return S.createElement(uL,r);case"sector":return S.createElement(Ay,r);case"symbols":if(vL(t))return S.createElement(Yc,r);break;default:return null}}function yL(e){return q.isValidElement(e)?e.props:e}function Uy(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?hL:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=sL(e,cL),s;if(q.isValidElement(t))s=q.cloneElement(t,_a(_a({},c),yL(t)));else if(Z(t))s=t(c);else if(HR(t)&&!ZR(t)){var f=i(t,c);s=S.createElement(cd,{shapeType:r,elementProps:f})}else{var l=c;s=S.createElement(cd,{shapeType:r,elementProps:l})}return u?S.createElement(ie,{className:o},s):s}function go(e,t){return t!=null&&"trapezoids"in e.props}function bo(e,t){return t!=null&&"sectors"in e.props}function li(e,t){return t!=null&&"points"in e.props}function mL(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function gL(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function bL(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function xL(e,t){var r;return go(e,t)?r=mL:bo(e,t)?r=gL:li(e,t)&&(r=bL),r}function wL(e,t){var r;return go(e,t)?r="trapezoids":bo(e,t)?r="sectors":li(e,t)&&(r="points"),r}function OL(e,t){if(go(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(bo(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return li(e,t)?t.payload:{}}function AL(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=wL(r,t),a=OL(r,t),o=n.filter(function(c,s){var f=Nr(a,c),l=r.props[i].filter(function(y){var h=xL(r,t);return h(y,t)}),p=r.props[i].indexOf(l[l.length-1]),d=s===p;return f&&d}),u=n.indexOf(o[o.length-1]);return u}var Fi;function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function wr(){return wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wr.apply(this,arguments)}function sd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sd(Object(r),!0).forEach(function(n){Ze(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ld(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hy(n.key),n)}}function PL(e,t,r){return t&&ld(e.prototype,t),r&&ld(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _L(e,t,r){return t=$a(t),$L(e,qy()?Reflect.construct(t,r||[],$a(e).constructor):t.apply(e,r))}function $L(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TL(e)}function TL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qy=function(){return!!e})()}function $a(e){return $a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$a(e)}function EL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hc(e,t)}function hc(e,t){return hc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hc(e,t)}function Ze(e,t,r){return t=Hy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hy(e){var t=jL(e,"string");return zr(t)=="symbol"?t:t+""}function jL(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ut=function(e){function t(r){var n;return SL(this,t),n=_L(this,t,[r]),Ze(n,"pieRef",null),Ze(n,"sectorRefs",[]),Ze(n,"id",cn("recharts-pie-")),Ze(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),Z(i)&&i()}),Ze(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),Z(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return EL(t,e),PL(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=Y(this.props,!1),l=Y(o,!1),p=Y(u,!1),d=o&&o.offsetRadius||20,y=n.map(function(h,v){var x=(h.startAngle+h.endAngle)/2,b=ve(h.cx,h.cy,h.outerRadius+d,x),w=de(de(de(de({},f),h),{},{stroke:"none"},l),{},{index:v,textAnchor:t.getTextAnchor(b.x,h.cx)},b),g=de(de(de(de({},f),h),{},{fill:"none",stroke:h.fill},p),{},{index:v,points:[ve(h.cx,h.cy,h.outerRadius,x),b]}),m=c;return ee(c)&&ee(s)?m="value":ee(c)&&(m=s),S.createElement(ie,{key:"label-".concat(h.startAngle,"-").concat(h.endAngle,"-").concat(h.midAngle,"-").concat(v)},u&&t.renderLabelLineItem(u,g,"line"),t.renderLabelItem(o,w,_e(h,m)))});return S.createElement(ie,{className:"recharts-pie-labels"},y)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),p=c&&i.hasActiveIndex()?c:null,d=l?o:p,y=de(de({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return S.createElement(ie,wr({ref:function(v){v&&!i.sectorRefs.includes(v)&&i.sectorRefs.push(v)},tabIndex:-1,className:"recharts-pie-sector"},ar(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),S.createElement(Uy,wr({option:d,isActive:l,shapeType:"sector"},y)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,p=l.prevSectors,d=l.prevIsAnimationActive;return S.createElement(dt,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(y){var h=y.t,v=[],x=a&&a[0],b=x.startAngle;return a.forEach(function(w,g){var m=p&&p[g],O=g>0?Ge(w,"paddingAngle",0):0;if(m){var A=Fe(m.endAngle-m.startAngle,w.endAngle-w.startAngle),P=de(de({},w),{},{startAngle:b+O,endAngle:b+A(h)+O});v.push(P),b=P.endAngle}else{var j=w.endAngle,M=w.startAngle,T=Fe(0,j-M),C=T(h),_=de(de({},w),{},{startAngle:b+O,endAngle:b+C+O});v.push(_),b=_.endAngle}}),S.createElement(ie,null,n.renderSectorsStatically(v))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!Nr(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,p=i.outerRadius,d=i.isAnimationActive,y=this.state.isAnimationFinished;if(a||!o||!o.length||!z(s)||!z(f)||!z(l)||!z(p))return null;var h=re("recharts-pie",u);return S.createElement(ie,{tabIndex:this.props.rootTabIndex,className:h,ref:function(x){n.pieRef=x}},this.renderSectors(),c&&this.renderLabels(o),je.renderCallByParent(this.props,null,!1),(!d||y)&&_t.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);if(Z(n))return n(i);var o=re("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return S.createElement($r,wr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);var o=a;if(Z(n)&&(o=n(i),S.isValidElement(o)))return o;var u=re("recharts-pie-label-text",typeof n!="boolean"&&!Z(n)?n.className:"");return S.createElement(or,wr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(q.PureComponent);Fi=Ut;Ze(Ut,"displayName","Pie");Ze(Ut,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!st.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Ze(Ut,"parseDeltaAngle",function(e,t){var r=Re(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Ze(Ut,"getRealPieData",function(e){var t=e.data,r=e.children,n=Y(e,!1),i=Qe(r,is);return t&&t.length?t.map(function(a,o){return de(de(de({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return de(de({},n),a.props)}):[]});Ze(Ut,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=by(i,a),u=n+Le(e.cx,i,i/2),c=r+Le(e.cy,a,a/2),s=Le(e.innerRadius,o,0),f=Le(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Ze(Ut,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?de(de({},t.type.defaultProps),t.props):t.props,i=Fi.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,p=n.tooltipType,d=Math.abs(n.minAngle),y=Fi.parseCoordinateOfPie(n,r),h=Fi.parseDeltaAngle(o,u),v=Math.abs(h),x=s;ee(s)&&ee(l)?(at(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x="value"):ee(s)&&(at(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x=l);var b=i.filter(function(P){return _e(P,x,0)!==0}).length,w=(v>=360?b:b-1)*c,g=v-b*d-w,m=i.reduce(function(P,j){var M=_e(j,x,0);return P+(z(M)?M:0)},0),O;if(m>0){var A;O=i.map(function(P,j){var M=_e(P,x,0),T=_e(P,f,j),C=(z(M)?M:0)/m,_;j?_=A.endAngle+Re(h)*c*(M!==0?1:0):_=o;var $=_+Re(h)*((M!==0?d:0)+C*g),k=(_+$)/2,I=(y.innerRadius+y.outerRadius)/2,D=[{name:T,value:M,payload:P,dataKey:x,type:p}],R=ve(y.cx,y.cy,I,k);return A=de(de(de({percent:C,cornerRadius:a,name:T,tooltipPayload:D,midAngle:k,middleRadius:I,tooltipPosition:R},P),y),{},{value:_e(P,x),startAngle:_,endAngle:$,payload:P,paddingAngle:Re(h)*c}),A})}return de(de({},y),{},{sectors:O,data:i})});var ML=Math.ceil,CL=Math.max;function IL(e,t,r,n){for(var i=-1,a=CL(ML((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var kL=IL,DL=ov,fd=1/0,NL=17976931348623157e292;function RL(e){if(!e)return e===0?e:0;if(e=DL(e),e===fd||e===-fd){var t=e<0?-1:1;return t*NL}return e===e?e:0}var Ky=RL,LL=kL,BL=eo,eu=Ky;function FL(e){return function(t,r,n){return n&&typeof n!="number"&&BL(t,r,n)&&(r=n=void 0),t=eu(t),r===void 0?(r=t,t=0):r=eu(r),n=n===void 0?t<r?1:-1:eu(n),LL(t,r,n,e)}}var zL=FL,WL=zL,UL=WL(),qL=UL;const Ta=se(qL);function fi(e){"@babel/helpers - typeof";return fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(e)}function pd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pd(Object(r),!0).forEach(function(n){Gy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gy(e,t,r){return t=HL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HL(e){var t=KL(e,"string");return fi(t)=="symbol"?t:t+""}function KL(e,t){if(fi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var GL=["Webkit","Moz","O","ms"],YL=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=GL.reduce(function(a,o){return dd(dd({},a),{},Gy({},o+n,r))},{});return i[t]=r,i};function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}function Ea(){return Ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ea.apply(this,arguments)}function hd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hd(Object(r),!0).forEach(function(n){qe(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xy(n.key),n)}}function VL(e,t,r){return t&&vd(e.prototype,t),r&&vd(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function ZL(e,t,r){return t=ja(t),JL(e,Yy()?Reflect.construct(t,r||[],ja(e).constructor):t.apply(e,r))}function JL(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QL(e)}function QL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Yy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Yy=function(){return!!e})()}function ja(e){return ja=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ja(e)}function eB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vc(e,t)}function vc(e,t){return vc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},vc(e,t)}function qe(e,t,r){return t=Xy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xy(e){var t=tB(e,"string");return Wr(t)=="symbol"?t:t+""}function tB(e,t){if(Wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rB=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=jn().domain(Ta(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},yd=function(t){return t.changedTouches&&!!t.changedTouches.length},Ur=function(e){function t(r){var n;return XL(this,t),n=ZL(this,t,[r]),qe(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),qe(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),qe(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),qe(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),qe(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),qe(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),qe(n,"handleSlideDragStart",function(i){var a=yd(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return eB(t,e),VL(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),p=Math.max(i,a),d=t.getIndexInRange(o,l),y=t.getIndexInRange(o,p);return{startIndex:d-d%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=_e(a[n],u,n);return Z(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,p=c.startIndex,d=c.endIndex,y=c.onChange,h=n.pageX-a;h>0?h=Math.min(h,s+f-l-u,s+f-l-o):h<0&&(h=Math.max(h,s-o,s-u));var v=this.getIndex({startX:o+h,endX:u+h});(v.startIndex!==p||v.endIndex!==d)&&y&&y(v),this.setState({startX:o+h,endX:u+h,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=yd(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,p=f.width,d=f.travellerWidth,y=f.onChange,h=f.gap,v=f.data,x={startX:this.state.startX,endX:this.state.endX},b=n.pageX-a;b>0?b=Math.min(b,l+p-d-s):b<0&&(b=Math.max(b,l-s)),x[o]=s+b;var w=this.getIndex(x),g=w.startIndex,m=w.endIndex,O=function(){var P=v.length-1;return o==="startX"&&(u>c?g%h===0:m%h===0)||u<c&&m===P||o==="endX"&&(u>c?m%h===0:g%h===0)||u>c&&m===P};this.setState(qe(qe({},o,s+b),"brushMoveStartX",n.pageX),function(){y&&O()&&y(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var p=l+n;if(!(p===-1||p>=u.length)){var d=u[p];i==="startX"&&d>=s||i==="endX"&&d<=c||this.setState(qe({},i,d),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return S.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?S.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,p=c.traveller,d=c.ariaLabel,y=c.data,h=c.startIndex,v=c.endIndex,x=Math.max(n,this.props.x),b=tu(tu({},Y(this.props,!1)),{},{x,y:s,width:f,height:l}),w=d||"Min value: ".concat((a=y[h])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[v])===null||o===void 0?void 0:o.name);return S.createElement(ie,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,b))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return S.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,p=f.endX,d=5,y={pointerEvents:"none",fill:s};return S.createElement(ie,{className:"recharts-brush-texts"},S.createElement(or,Ea({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-d,y:o+u/2},y),this.getTextOfTick(i)),S.createElement(or,Ea({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+c+d,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,p=this.state,d=p.startX,y=p.endX,h=p.isTextActive,v=p.isSlideMoving,x=p.isTravellerMoving,b=p.isTravellerFocused;if(!i||!i.length||!z(u)||!z(c)||!z(s)||!z(f)||s<=0||f<=0)return null;var w=re("recharts-brush",a),g=S.Children.count(o)===1,m=YL("userSelect","none");return S.createElement(ie,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(d,y),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(y,"endX"),(h||v||x||b||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return S.createElement(S.Fragment,null,S.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),S.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),S.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return S.isValidElement(n)?a=S.cloneElement(n,i):Z(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return tu({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?rB({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(d){return i.scale(d)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);qe(Ur,"displayName","Brush");qe(Ur,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var nB=ts;function iB(e,t){var r;return nB(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var aB=iB,oB=Ih,uB=vt,cB=aB,sB=We,lB=eo;function fB(e,t,r){var n=sB(e)?oB:cB;return r&&lB(e,t,r)&&(t=void 0),n(e,uB(t))}var pB=fB;const dB=se(pB);var ft=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},md=tv;function hB(e,t,r){t=="__proto__"&&md?md(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var vB=hB,yB=vB,mB=Qh,gB=vt;function bB(e,t){var r={};return t=gB(t),mB(e,function(n,i,a){yB(r,i,t(n,i,a))}),r}var xB=bB;const wB=se(xB);function OB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var AB=OB,SB=ts;function PB(e,t){var r=!0;return SB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var _B=PB,$B=AB,TB=_B,EB=vt,jB=We,MB=eo;function CB(e,t,r){var n=jB(e)?$B:TB;return r&&MB(e,t,r)&&(t=void 0),n(e,EB(t))}var IB=CB;const Vy=se(IB);var kB=["x","y"];function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function yc(){return yc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yc.apply(this,arguments)}function gd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $n(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gd(Object(r),!0).forEach(function(n){DB(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DB(e,t,r){return t=NB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function NB(e){var t=RB(e,"string");return qr(t)=="symbol"?t:t+""}function RB(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LB(e,t){if(e==null)return{};var r=BB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function FB(e,t){var r=e.x,n=e.y,i=LB(e,kB),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),p=parseInt(l,10);return $n($n($n($n($n({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function bd(e){return S.createElement(Uy,yc({shapeType:"rectangle",propTransformer:FB,activeClassName:"recharts-active-bar"},e))}var zB=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||cr(!1,"minPointSize callback function received a value with type of ".concat(qr(n),". Currently only numbers are supported.")),r)}},WB=["value","background"],Zy;function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function UB(e,t){if(e==null)return{};var r=qB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ma(){return Ma=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ma.apply(this,arguments)}function xd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xd(Object(r),!0).forEach(function(n){Rt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qy(n.key),n)}}function KB(e,t,r){return t&&wd(e.prototype,t),r&&wd(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function GB(e,t,r){return t=Ca(t),YB(e,Jy()?Reflect.construct(t,r||[],Ca(e).constructor):t.apply(e,r))}function YB(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return XB(e)}function XB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Jy=function(){return!!e})()}function Ca(e){return Ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ca(e)}function VB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mc(e,t)}function mc(e,t){return mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mc(e,t)}function Rt(e,t,r){return t=Qy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qy(e){var t=ZB(e,"string");return Hr(t)=="symbol"?t:t+""}function ZB(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var yn=function(e){function t(){var r;HB(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=GB(this,t,[].concat(i)),Rt(r,"state",{isAnimationFinished:!1}),Rt(r,"id",cn("recharts-bar-")),Rt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Rt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return VB(t,e),KB(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=Y(this.props,!1);return n&&n.map(function(l,p){var d=p===c,y=d?s:o,h=Ae(Ae(Ae({},f),l),{},{isActive:d,option:y,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return S.createElement(ie,Ma({className:"recharts-bar-rectangle"},ar(i.props,l,p),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value)}),S.createElement(bd,h))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,p=this.state.prevData;return S.createElement(dt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var y=d.t,h=a.map(function(v,x){var b=p&&p[x];if(b){var w=Fe(b.x,v.x),g=Fe(b.y,v.y),m=Fe(b.width,v.width),O=Fe(b.height,v.height);return Ae(Ae({},v),{},{x:w(y),y:g(y),width:m(y),height:O(y)})}if(o==="horizontal"){var A=Fe(0,v.height),P=A(y);return Ae(Ae({},v),{},{y:v.y+v.height-P,height:P})}var j=Fe(0,v.width),M=j(y);return Ae(Ae({},v),{},{width:M})});return S.createElement(ie,null,n.renderRectanglesStatically(h))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Nr(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=Y(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,p=UB(s,WB);if(!l)return null;var d=Ae(Ae(Ae(Ae(Ae({},p),{},{fill:"#eee"},l),c),ar(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return S.createElement(bd,Ma({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},d))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Qe(f,fo);if(!l)return null;var p=s==="vertical"?o[0].height/2:o[0].width/2,d=function(v,x){var b=Array.isArray(v.value)?v.value[1]:v.value;return{x:v.x,y:v.y,value:b,errorVal:_e(v,x)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(ie,y,l.map(function(h){return S.cloneElement(h,{key:"error-bar-".concat(i,"-").concat(h.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:p,dataPointFormatter:d})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,p=n.height,d=n.isAnimationActive,y=n.background,h=n.id;if(i||!a||!a.length)return null;var v=this.state.isAnimationFinished,x=re("recharts-bar",o),b=u&&u.allowDataOverflow,w=c&&c.allowDataOverflow,g=b||w,m=ee(h)?this.id:h;return S.createElement(ie,{className:x},b||w?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(m)},S.createElement("rect",{x:b?s:s-l/2,y:w?f:f-p/2,width:b?l:l*2,height:w?p:p*2}))):null,S.createElement(ie,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,m),(!d||v)&&_t.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);Zy=yn;Rt(yn,"displayName","Bar");Rt(yn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!st.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Rt(yn,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,d=Mk(n,r);if(!d)return null;var y=t.layout,h=r.type.defaultProps,v=h!==void 0?Ae(Ae({},h),r.props):r.props,x=v.dataKey,b=v.children,w=v.minPointSize,g=y==="horizontal"?o:a,m=s?g.scale.domain():null,O=Lk({numericAxis:g}),A=Qe(b,is),P=l.map(function(j,M){var T,C,_,$,k,I;s?T=Ck(s[f+M],m):(T=_e(j,x),Array.isArray(T)||(T=[O,T]));var D=zB(w,Zy.defaultProps.minPointSize)(T[1],M);if(y==="horizontal"){var R,L=[o.scale(T[0]),o.scale(T[1])],U=L[0],B=L[1];C=cp({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:j,index:M}),_=(R=B??U)!==null&&R!==void 0?R:void 0,$=d.size;var F=U-B;if(k=Number.isNaN(F)?0:F,I={x:C,y:o.y,width:$,height:o.height},Math.abs(D)>0&&Math.abs(k)<Math.abs(D)){var H=Re(k||D)*(Math.abs(D)-Math.abs(k));_-=H,k+=H}}else{var J=[a.scale(T[0]),a.scale(T[1])],ne=J[0],we=J[1];if(C=ne,_=cp({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:j,index:M}),$=we-ne,k=d.size,I={x:a.x,y:_,width:a.width,height:k},Math.abs(D)>0&&Math.abs($)<Math.abs(D)){var Oe=Re($||D)*(Math.abs(D)-Math.abs($));$+=Oe}}return Ae(Ae(Ae({},j),{},{x:C,y:_,width:$,height:k,value:s?T:T[1],payload:j,background:I},A&&A[M]&&A[M].props),{},{tooltipPayload:[my(r,j)],tooltipPosition:{x:C+$/2,y:_+k/2}})});return Ae({data:P,layout:y},p)});function pi(e){"@babel/helpers - typeof";return pi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pi(e)}function JB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Od(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,em(n.key),n)}}function QB(e,t,r){return t&&Od(e.prototype,t),r&&Od(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ad(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ad(Object(r),!0).forEach(function(n){xo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ad(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xo(e,t,r){return t=em(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function em(e){var t=eF(e,"string");return pi(t)=="symbol"?t:t+""}function eF(e,t){if(pi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(pi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var tm=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!He(s,yn);return f.reduce(function(d,y){var h=r[y],v=h.orientation,x=h.domain,b=h.padding,w=b===void 0?{}:b,g=h.mirror,m=h.reversed,O="".concat(v).concat(g?"Mirror":""),A,P,j,M,T;if(h.type==="number"&&(h.padding==="gap"||h.padding==="no-gap")){var C=x[1]-x[0],_=1/0,$=h.categoricalDomain.sort();if($.forEach(function(J,ne){ne>0&&(_=Math.min((J||0)-($[ne-1]||0),_))}),Number.isFinite(_)){var k=_/C,I=h.layout==="vertical"?n.height:n.width;if(h.padding==="gap"&&(A=k*I/2),h.padding==="no-gap"){var D=Le(t.barCategoryGap,k*I),R=k*I/2;A=R-D-(R-D)/I*D}}}i==="xAxis"?P=[n.left+(w.left||0)+(A||0),n.left+n.width-(w.right||0)-(A||0)]:i==="yAxis"?P=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(A||0),n.top+n.height-(w.bottom||0)-(A||0)]:P=h.range,m&&(P=[P[1],P[0]]);var L=dy(h,a,p),U=L.scale,B=L.realScaleType;U.domain(x).range(P),hy(U);var F=vy(U,nt(nt({},h),{},{realScaleType:B}));i==="xAxis"?(T=v==="top"&&!g||v==="bottom"&&g,j=n.left,M=l[O]-T*h.height):i==="yAxis"&&(T=v==="left"&&!g||v==="right"&&g,j=l[O]-T*h.width,M=n.top);var H=nt(nt(nt({},h),F),{},{realScaleType:B,x:j,y:M,scale:U,width:i==="xAxis"?n.width:h.width,height:i==="yAxis"?n.height:h.height});return H.bandSize=va(H,F),!h.hide&&i==="xAxis"?l[O]+=(T?-1:1)*H.height:h.hide||(l[O]+=(T?-1:1)*H.width),nt(nt({},d),{},xo({},y,H))},{})},rm=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},tF=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return rm({x:r,y:n},{x:i,y:a})},nm=function(){function e(t){JB(this,e),this.scale=t}return QB(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();xo(nm,"EPS",1e-4);var Ms=function(t){var r=Object.keys(t).reduce(function(n,i){return nt(nt({},n),{},xo({},i,nm.create(t[i])))},{});return nt(nt({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return wB(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Vy(i,function(a,o){return r[o].isInRange(a)})}})};function rF(e){return(e%180+180)%180}var nF=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=rF(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},iF=vt,aF=gi,oF=Ja;function uF(e){return function(t,r,n){var i=Object(t);if(!aF(t)){var a=iF(r);t=oF(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var cF=uF,sF=Ky;function lF(e){var t=sF(e),r=t%1;return t===t?r?t-r:t:0}var fF=lF,pF=Gh,dF=vt,hF=fF,vF=Math.max;function yF(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:hF(r);return i<0&&(i=vF(n+i,0)),pF(e,dF(t),i)}var mF=yF,gF=cF,bF=mF,xF=gF(bF),wF=xF;const OF=se(wF);var AF=O0(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")});function Ia(e){"@babel/helpers - typeof";return Ia=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ia(e)}var Cs=q.createContext(void 0),Is=q.createContext(void 0),im=q.createContext(void 0),am=q.createContext({}),om=q.createContext(void 0),um=q.createContext(0),cm=q.createContext(0),Sd=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=AF(a);return S.createElement(Cs.Provider,{value:n},S.createElement(Is.Provider,{value:i},S.createElement(am.Provider,{value:a},S.createElement(im.Provider,{value:f},S.createElement(om.Provider,{value:o},S.createElement(um.Provider,{value:s},S.createElement(cm.Provider,{value:c},u)))))))},SF=function(){return q.useContext(om)};function sm(e){var t=Object.keys(e);return t.length===0?"There are no available ids.":"Available ids are: ".concat(t,".")}var lm=function(t){var r=q.useContext(Cs);r==null&&cr(!1,"Could not find Recharts context; are you sure this is rendered inside a Recharts wrapper component?");var n=r[t];return n==null&&cr(!1,'Could not find xAxis by id "'.concat(t,'" [').concat(Ia(t),"]. ").concat(sm(r))),n},PF=function(){var t=q.useContext(Cs);return Dt(t)},_F=function(){var t=q.useContext(Is),r=OF(t,function(n){return Vy(n.domain,Number.isFinite)});return r||Dt(t)},fm=function(t){var r=q.useContext(Is);r==null&&cr(!1,"Could not find Recharts context; are you sure this is rendered inside a Recharts wrapper component?");var n=r[t];return n==null&&cr(!1,'Could not find yAxis by id "'.concat(t,'" [').concat(Ia(t),"]. ").concat(sm(r))),n},$F=function(){var t=q.useContext(im);return t},TF=function(){return q.useContext(am)},ks=function(){return q.useContext(cm)},Ds=function(){return q.useContext(um)};function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function EF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dm(n.key),n)}}function MF(e,t,r){return t&&jF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function CF(e,t,r){return t=ka(t),IF(e,pm()?Reflect.construct(t,r||[],ka(e).constructor):t.apply(e,r))}function IF(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kF(e)}function kF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pm=function(){return!!e})()}function ka(e){return ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ka(e)}function DF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gc(e,t)}function gc(e,t){return gc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gc(e,t)}function Pd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _d(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pd(Object(r),!0).forEach(function(n){Ns(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ns(e,t,r){return t=dm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dm(e){var t=NF(e,"string");return Kr(t)=="symbol"?t:t+""}function NF(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function RF(e,t){return zF(e)||FF(e,t)||BF(e,t)||LF()}function LF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function BF(e,t){if(e){if(typeof e=="string")return $d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $d(e,t)}}function $d(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function FF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function zF(e){if(Array.isArray(e))return e}function bc(){return bc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bc.apply(this,arguments)}var WF=function(t,r){var n;return S.isValidElement(t)?n=S.cloneElement(t,r):Z(t)?n=t(r):n=S.createElement("line",bc({},r,{className:"recharts-reference-line-line"})),n},UF=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,p=a.width,d=a.height;if(n){var y=s.y,h=t.y.apply(y,{position:o});if(ft(s,"discard")&&!t.y.isInRange(h))return null;var v=[{x:f+p,y:h},{x:f,y:h}];return c==="left"?v.reverse():v}if(r){var x=s.x,b=t.x.apply(x,{position:o});if(ft(s,"discard")&&!t.x.isInRange(b))return null;var w=[{x:b,y:l+d},{x:b,y:l}];return u==="top"?w.reverse():w}if(i){var g=s.segment,m=g.map(function(O){return t.apply(O,{position:o})});return ft(s,"discard")&&dB(m,function(O){return!t.isInRange(O)})?null:m}return null};function qF(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=SF(),f=lm(i),l=fm(a),p=$F();if(!s||!p)return null;at(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=Ms({x:f.scale,y:l.scale}),y=$e(t),h=$e(r),v=n&&n.length===2,x=UF(d,y,h,v,p,e.position,f.orientation,l.orientation,e);if(!x)return null;var b=RF(x,2),w=b[0],g=w.x,m=w.y,O=b[1],A=O.x,P=O.y,j=ft(e,"hidden")?"url(#".concat(s,")"):void 0,M=_d(_d({clipPath:j},Y(e,!0)),{},{x1:g,y1:m,x2:A,y2:P});return S.createElement(ie,{className:re("recharts-reference-line",u)},WF(o,M),je.renderCallByParent(e,tF({x1:g,y1:m,x2:A,y2:P})))}var Rs=function(e){function t(){return EF(this,t),CF(this,t,arguments)}return DF(t,e),MF(t,[{key:"render",value:function(){return S.createElement(qF,this.props)}}])}(S.Component);Ns(Rs,"displayName","ReferenceLine");Ns(Rs,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function xc(){return xc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xc.apply(this,arguments)}function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function Td(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ed(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Td(Object(r),!0).forEach(function(n){wo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Td(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function KF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vm(n.key),n)}}function GF(e,t,r){return t&&KF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function YF(e,t,r){return t=Da(t),XF(e,hm()?Reflect.construct(t,r||[],Da(e).constructor):t.apply(e,r))}function XF(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VF(e)}function VF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hm=function(){return!!e})()}function Da(e){return Da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Da(e)}function ZF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wc(e,t)}function wc(e,t){return wc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wc(e,t)}function wo(e,t,r){return t=vm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vm(e){var t=JF(e,"string");return Gr(t)=="symbol"?t:t+""}function JF(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var QF=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Ms({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ft(t,"discard")&&!o.isInRange(u)?null:u},Oo=function(e){function t(){return HF(this,t),YF(this,t,arguments)}return ZF(t,e),GF(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=$e(i),f=$e(a);if(at(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=QF(this.props);if(!l)return null;var p=l.x,d=l.y,y=this.props,h=y.shape,v=y.className,x=ft(this.props,"hidden")?"url(#".concat(c,")"):void 0,b=Ed(Ed({clipPath:x},Y(this.props,!0)),{},{cx:p,cy:d});return S.createElement(ie,{className:re("recharts-reference-dot",v)},t.renderDot(h,b),je.renderCallByParent(this.props,{x:p-o,y:d-o,width:2*o,height:2*o}))}}])}(S.Component);wo(Oo,"displayName","ReferenceDot");wo(Oo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});wo(Oo,"renderDot",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):Z(e)?r=e(t):r=S.createElement(po,xc({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Oc(){return Oc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oc.apply(this,arguments)}function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function jd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Md(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jd(Object(r),!0).forEach(function(n){Ao(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function e3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t3(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mm(n.key),n)}}function r3(e,t,r){return t&&t3(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n3(e,t,r){return t=Na(t),i3(e,ym()?Reflect.construct(t,r||[],Na(e).constructor):t.apply(e,r))}function i3(e,t){if(t&&(Yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return a3(e)}function a3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ym(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ym=function(){return!!e})()}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Na(e)}function o3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ac(e,t)}function Ac(e,t){return Ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ac(e,t)}function Ao(e,t,r){return t=mm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mm(e){var t=u3(e,"string");return Yr(t)=="symbol"?t:t+""}function u3(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var c3=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var p=Ms({x:f.scale,y:l.scale}),d={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},y={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(s,{position:"end"}):p.y.rangeMax};return ft(a,"discard")&&(!p.isInRange(d)||!p.isInRange(y))?null:rm(d,y)},So=function(e){function t(){return e3(this,t),n3(this,t,arguments)}return o3(t,e),r3(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;at(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=$e(i),p=$e(a),d=$e(o),y=$e(u),h=this.props.shape;if(!l&&!p&&!d&&!y&&!h)return null;var v=c3(l,p,d,y,this.props);if(!v&&!h)return null;var x=ft(this.props,"hidden")?"url(#".concat(f,")"):void 0;return S.createElement(ie,{className:re("recharts-reference-area",c)},t.renderRect(h,Md(Md({clipPath:x},Y(this.props,!0)),v)),je.renderCallByParent(this.props,v))}}])}(S.Component);Ao(So,"displayName","ReferenceArea");Ao(So,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Ao(So,"renderRect",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):Z(e)?r=e(t):r=S.createElement(js,Oc({},t,{className:"recharts-reference-area-rect"})),r});function gm(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function s3(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return nF(n,r)}function l3(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Ra(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function f3(e,t){return gm(e,t+1)}function p3(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:gm(n,s)};var h=c,v,x=function(){return v===void 0&&(v=r(y,h)),v},b=y.coordinate,w=c===0||Ra(e,b,x,f,u);w||(c=0,f=o,s+=1),w&&(f=b+e*(x()/2+i),c+=s)},p;s<=a.length;)if(p=l(),p)return p.v;return[]}function di(e){"@babel/helpers - typeof";return di=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},di(e)}function Cd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cd(Object(r),!0).forEach(function(n){d3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function d3(e,t,r){return t=h3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h3(e){var t=v3(e,"string");return di(t)=="symbol"?t:t+""}function v3(e,t){if(di(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(di(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function y3(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(p){var d=a[p],y,h=function(){return y===void 0&&(y=r(d,p)),y};if(p===o-1){var v=e*(d.coordinate+e*h()/2-c);a[p]=d=ke(ke({},d),{},{tickCoord:v>0?d.coordinate-v*e:d.coordinate})}else a[p]=d=ke(ke({},d),{},{tickCoord:d.coordinate});var x=Ra(e,d.tickCoord,h,u,c);x&&(c=d.tickCoord-e*(h()/2+i),a[p]=ke(ke({},d),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function m3(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),p=e*(f.coordinate+e*l/2-s);o[u-1]=f=ke(ke({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var d=Ra(e,f.tickCoord,function(){return l},c,s);d&&(s=f.tickCoord-e*(l/2+i),o[u-1]=ke(ke({},f),{},{isShow:!0}))}for(var y=a?u-1:u,h=function(b){var w=o[b],g,m=function(){return g===void 0&&(g=r(w,b)),g};if(b===0){var O=e*(w.coordinate-e*m()/2-c);o[b]=w=ke(ke({},w),{},{tickCoord:O<0?w.coordinate-O*e:w.coordinate})}else o[b]=w=ke(ke({},w),{},{tickCoord:w.coordinate});var A=Ra(e,w.tickCoord,m,c,s);A&&(c=w.tickCoord+e*(m()/2+i),o[b]=ke(ke({},w),{},{isShow:!0}))},v=0;v<y;v++)h(v);return o}function Ls(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(z(c)||st.isSsr)return f3(i,typeof c=="number"&&z(c)?c:0);var p=[],d=u==="top"||u==="bottom"?"width":"height",y=f&&d==="width"?En(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},h=function(w,g){var m=Z(s)?s(w.value,g):w.value;return d==="width"?s3(En(m,{fontSize:t,letterSpacing:r}),y,l):En(m,{fontSize:t,letterSpacing:r})[d]},v=i.length>=2?Re(i[1].coordinate-i[0].coordinate):1,x=l3(a,v,d);return c==="equidistantPreserveStart"?p3(v,x,h,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=m3(v,x,h,i,o,c==="preserveStartEnd"):p=y3(v,x,h,i,o),p.filter(function(b){return b.isShow}))}var g3=["viewBox"],b3=["viewBox"],x3=["ticks"];function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Or.apply(this,arguments)}function Id(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ne(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Id(Object(r),!0).forEach(function(n){Bs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Id(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ru(e,t){if(e==null)return{};var r=w3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function w3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function O3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xm(n.key),n)}}function A3(e,t,r){return t&&kd(e.prototype,t),r&&kd(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function S3(e,t,r){return t=La(t),P3(e,bm()?Reflect.construct(t,r||[],La(e).constructor):t.apply(e,r))}function P3(e,t){if(t&&(Xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _3(e)}function _3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function bm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(bm=function(){return!!e})()}function La(e){return La=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},La(e)}function $3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Sc(e,t)}function Sc(e,t){return Sc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Sc(e,t)}function Bs(e,t,r){return t=xm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xm(e){var t=T3(e,"string");return Xr(t)=="symbol"?t:t+""}function T3(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var mn=function(e){function t(r){var n;return O3(this,t),n=S3(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return $3(t,e),A3(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=ru(n,g3),u=this.props,c=u.viewBox,s=ru(u,b3);return!Ar(a,c)||!Ar(o,s)||!Ar(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,p=i.tickMargin,d,y,h,v,x,b,w=l?-1:1,g=n.tickSize||f,m=z(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":d=y=n.coordinate,v=o+ +!l*c,h=v-w*g,b=h-w*p,x=m;break;case"left":h=v=n.coordinate,y=a+ +!l*u,d=y-w*g,x=d-w*p,b=m;break;case"right":h=v=n.coordinate,y=a+ +l*u,d=y+w*g,x=d+w*p,b=m;break;default:d=y=n.coordinate,v=o+ +l*c,h=v+w*g,b=h+w*p,x=m;break}return{line:{x1:d,y1:h,x2:y,y2:v},tick:{x,y:b}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Ne(Ne(Ne({},Y(this.props,!1)),Y(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!s||c==="bottom"&&s);l=Ne(Ne({},l),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var d=+(c==="left"&&!s||c==="right"&&s);l=Ne(Ne({},l),{},{x1:i+d*o,y1:a,x2:i+d*o,y2:a+u})}return S.createElement("line",Or({},l,{className:re("recharts-cartesian-axis-line",Ge(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,p=u.unit,d=Ls(Ne(Ne({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),v=Y(this.props,!1),x=Y(f,!1),b=Ne(Ne({},v),{},{fill:"none"},Y(c,!1)),w=d.map(function(g,m){var O=o.getTickLineCoord(g),A=O.line,P=O.tick,j=Ne(Ne(Ne(Ne({textAnchor:y,verticalAnchor:h},v),{},{stroke:"none",fill:s},x),P),{},{index:m,payload:g,visibleTicksCount:d.length,tickFormatter:l});return S.createElement(ie,Or({className:"recharts-cartesian-axis-tick",key:"tick-".concat(g.value,"-").concat(g.coordinate,"-").concat(g.tickCoord)},ar(o.props,g,m)),c&&S.createElement("line",Or({},b,A,{className:re("recharts-cartesian-axis-tick-line",Ge(c,"className"))})),f&&t.renderTickItem(f,j,"".concat(Z(l)?l(g.value,m):g.value).concat(p||"")))});return S.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,p=l.ticks,d=ru(l,x3),y=p;return Z(c)&&(y=p&&p.length>0?c(this.props):c(d)),o<=0||u<=0||!y||!y.length?null:S.createElement(ie,{className:re("recharts-cartesian-axis",s),ref:function(v){n.layerReference=v}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),je.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):Z(n)?o=n(i):o=S.createElement(or,Or({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);Bs(mn,"displayName","CartesianAxis");Bs(mn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var E3=["x1","y1","x2","y2","key"],j3=["offset"];function sr(e){"@babel/helpers - typeof";return sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sr(e)}function Dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function De(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dd(Object(r),!0).forEach(function(n){M3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function M3(e,t,r){return t=C3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function C3(e){var t=I3(e,"string");return sr(t)=="symbol"?t:t+""}function I3(e,t){if(sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function tr(){return tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tr.apply(this,arguments)}function Nd(e,t){if(e==null)return{};var r=k3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function k3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var D3=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return S.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function wm(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(Z(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Nd(t,E3),s=Y(c,!1);s.offset;var f=Nd(s,j3);r=S.createElement("line",tr({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function N3(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=De(De({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return wm(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function R3(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=De(De({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return wm(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function L3(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,d){return p-d});i!==f[0]&&f.unshift(0);var l=f.map(function(p,d){var y=!f[d+1],h=y?i+o-p:f[d+1]-p;if(h<=0)return null;var v=d%t.length;return S.createElement("rect",{key:"react-".concat(d),y:p,x:n,height:h,width:a,stroke:"none",fill:t[v],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function B3(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(p){return Math.round(p+a-a)}).sort(function(p,d){return p-d});a!==f[0]&&f.unshift(0);var l=f.map(function(p,d){var y=!f[d+1],h=y?a+u-p:f[d+1]-p;if(h<=0)return null;var v=d%n.length;return S.createElement("rect",{key:"react-".concat(d),x:p,y:o,width:h,height:c,stroke:"none",fill:n[v],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var F3=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return py(Ls(De(De(De({},mn.defaultProps),n),{},{ticks:At(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},z3=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return py(Ls(De(De(De({},mn.defaultProps),n),{},{ticks:At(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},mr={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function W3(e){var t,r,n,i,a,o,u=ks(),c=Ds(),s=TF(),f=De(De({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:mr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:mr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:mr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:mr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:mr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:mr.verticalFill,x:z(e.x)?e.x:s.left,y:z(e.y)?e.y:s.top,width:z(e.width)?e.width:s.width,height:z(e.height)?e.height:s.height}),l=f.x,p=f.y,d=f.width,y=f.height,h=f.syncWithTicks,v=f.horizontalValues,x=f.verticalValues,b=PF(),w=_F();if(!z(d)||d<=0||!z(y)||y<=0||!z(l)||l!==+l||!z(p)||p!==+p)return null;var g=f.verticalCoordinatesGenerator||F3,m=f.horizontalCoordinatesGenerator||z3,O=f.horizontalPoints,A=f.verticalPoints;if((!O||!O.length)&&Z(m)){var P=v&&v.length,j=m({yAxis:w?De(De({},w),{},{ticks:P?v:w.ticks}):void 0,width:u,height:c,offset:s},P?!0:h);at(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(sr(j),"]")),Array.isArray(j)&&(O=j)}if((!A||!A.length)&&Z(g)){var M=x&&x.length,T=g({xAxis:b?De(De({},b),{},{ticks:M?x:b.ticks}):void 0,width:u,height:c,offset:s},M?!0:h);at(Array.isArray(T),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(sr(T),"]")),Array.isArray(T)&&(A=T)}return S.createElement("g",{className:"recharts-cartesian-grid"},S.createElement(D3,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),S.createElement(N3,tr({},f,{offset:s,horizontalPoints:O,xAxis:b,yAxis:w})),S.createElement(R3,tr({},f,{offset:s,verticalPoints:A,xAxis:b,yAxis:w})),S.createElement(L3,tr({},f,{horizontalPoints:O})),S.createElement(B3,tr({},f,{verticalPoints:A})))}W3.displayName="CartesianGrid";var U3=["layout","type","stroke","connectNulls","isRange","ref"],q3=["key"],Om;function Vr(e){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vr(e)}function Am(e,t){if(e==null)return{};var r=H3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function H3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function rr(){return rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rr.apply(this,arguments)}function Rd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function It(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rd(Object(r),!0).forEach(function(n){ct(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ld(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Pm(n.key),n)}}function G3(e,t,r){return t&&Ld(e.prototype,t),r&&Ld(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Y3(e,t,r){return t=Ba(t),X3(e,Sm()?Reflect.construct(t,r||[],Ba(e).constructor):t.apply(e,r))}function X3(e,t){if(t&&(Vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V3(e)}function V3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Sm=function(){return!!e})()}function Ba(e){return Ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ba(e)}function Z3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pc(e,t)}function Pc(e,t){return Pc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pc(e,t)}function ct(e,t,r){return t=Pm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pm(e){var t=J3(e,"string");return Vr(t)=="symbol"?t:t+""}function J3(e,t){if(Vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var dr=function(e){function t(){var r;K3(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Y3(this,t,[].concat(i)),ct(r,"state",{isAnimationFinished:!0}),ct(r,"id",cn("recharts-area-")),ct(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),Z(o)&&o()}),ct(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),Z(o)&&o()}),r}return Z3(t,e),G3(t,[{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive,u=this.state.isAnimationFinished;if(o&&!u)return null;var c=this.props,s=c.dot,f=c.points,l=c.dataKey,p=Y(this.props,!1),d=Y(s,!0),y=f.map(function(v,x){var b=It(It(It({key:"dot-".concat(x),r:3},p),d),{},{index:x,cx:v.x,cy:v.y,dataKey:l,value:v.value,payload:v.payload,points:f});return t.renderDotItem(s,b)}),h={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return S.createElement(ie,rr({className:"recharts-area-dots"},h),y)}},{key:"renderHorizontalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].x,s=o[o.length-1].x,f=n*Math.abs(c-s),l=Nt(o.map(function(p){return p.y||0}));return z(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(Nt(a.map(function(p){return p.y||0})),l)),z(l)?S.createElement("rect",{x:c<s?c:c-f,y:0,width:f,height:Math.floor(l+(u?parseInt("".concat(u),10):1))}):null}},{key:"renderVerticalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].y,s=o[o.length-1].y,f=n*Math.abs(c-s),l=Nt(o.map(function(p){return p.x||0}));return z(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(Nt(a.map(function(p){return p.x||0})),l)),z(l)?S.createElement("rect",{x:0,y:c<s?c:c-f,width:l+(u?parseInt("".concat(u),10):1),height:Math.floor(f)}):null}},{key:"renderClipRect",value:function(n){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(n):this.renderHorizontalRect(n)}},{key:"renderAreaStatically",value:function(n,i,a,o){var u=this.props,c=u.layout,s=u.type,f=u.stroke,l=u.connectNulls,p=u.isRange;u.ref;var d=Am(u,U3);return S.createElement(ie,{clipPath:a?"url(#clipPath-".concat(o,")"):null},S.createElement($r,rr({},Y(d,!0),{points:n,connectNulls:l,type:s,baseLine:i,layout:c,stroke:"none",className:"recharts-area-area"})),f!=="none"&&S.createElement($r,rr({},Y(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:n})),f!=="none"&&p&&S.createElement($r,rr({},Y(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.baseLine,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,d=o.animationId,y=this.state,h=y.prevPoints,v=y.prevBaseLine;return S.createElement(dt,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"area-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(x){var b=x.t;if(h){var w=h.length/u.length,g=u.map(function(P,j){var M=Math.floor(j*w);if(h[M]){var T=h[M],C=Fe(T.x,P.x),_=Fe(T.y,P.y);return It(It({},P),{},{x:C(b),y:_(b)})}return P}),m;if(z(c)&&typeof c=="number"){var O=Fe(v,c);m=O(b)}else if(ee(c)||un(c)){var A=Fe(v,0);m=A(b)}else m=c.map(function(P,j){var M=Math.floor(j*w);if(v[M]){var T=v[M],C=Fe(T.x,P.x),_=Fe(T.y,P.y);return It(It({},P),{},{x:C(b),y:_(b)})}return P});return a.renderAreaStatically(g,m,n,i)}return S.createElement(ie,null,S.createElement("defs",null,S.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(b))),S.createElement(ie,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(u,c,n,i)))})}},{key:"renderArea",value:function(n,i){var a=this.props,o=a.points,u=a.baseLine,c=a.isAnimationActive,s=this.state,f=s.prevPoints,l=s.prevBaseLine,p=s.totalLength;return c&&o&&o.length&&(!f&&p>0||!Nr(f,o)||!Nr(l,u))?this.renderAreaWithAnimation(n,i):this.renderAreaStatically(o,u,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.top,f=i.left,l=i.xAxis,p=i.yAxis,d=i.width,y=i.height,h=i.isAnimationActive,v=i.id;if(a||!u||!u.length)return null;var x=this.state.isAnimationFinished,b=u.length===1,w=re("recharts-area",c),g=l&&l.allowDataOverflow,m=p&&p.allowDataOverflow,O=g||m,A=ee(v)?this.id:v,P=(n=Y(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},j=P.r,M=j===void 0?3:j,T=P.strokeWidth,C=T===void 0?2:T,_=Px(o)?o:{},$=_.clipDot,k=$===void 0?!0:$,I=M*2+C;return S.createElement(ie,{className:w},g||m?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(A)},S.createElement("rect",{x:g?f:f-d/2,y:m?s:s-y/2,width:g?d:d*2,height:m?y:y*2})),!k&&S.createElement("clipPath",{id:"clipPath-dots-".concat(A)},S.createElement("rect",{x:f-I/2,y:s-I/2,width:d+I,height:y+I}))):null,b?null:this.renderArea(O,A),(o||b)&&this.renderDots(O,k,A),(!h||x)&&_t.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,curBaseLine:n.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:n.points!==i.curPoints||n.baseLine!==i.curBaseLine?{curPoints:n.points,curBaseLine:n.baseLine}:null}}])}(q.PureComponent);Om=dr;ct(dr,"displayName","Area");ct(dr,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!st.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});ct(dr,"getBaseValue",function(e,t,r,n){var i=e.layout,a=e.baseValue,o=t.props.baseValue,u=o??a;if(z(u)&&typeof u=="number")return u;var c=i==="horizontal"?n:r,s=c.scale.domain();if(c.type==="number"){var f=Math.max(s[0],s[1]),l=Math.min(s[0],s[1]);return u==="dataMin"?l:u==="dataMax"||f<0?f:Math.max(Math.min(s[0],s[1]),0)}return u==="dataMin"?s[0]:u==="dataMax"?s[1]:s[0]});ct(dr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.xAxis,i=e.yAxis,a=e.xAxisTicks,o=e.yAxisTicks,u=e.bandSize,c=e.dataKey,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,d=t.layout,y=s&&s.length,h=Om.getBaseValue(t,r,n,i),v=d==="horizontal",x=!1,b=l.map(function(g,m){var O;y?O=s[f+m]:(O=_e(g,c),Array.isArray(O)?x=!0:O=[h,O]);var A=O[1]==null||y&&_e(g,c)==null;return v?{x:up({axis:n,ticks:a,bandSize:u,entry:g,index:m}),y:A?null:i.scale(O[1]),value:O,payload:g}:{x:A?null:n.scale(O[1]),y:up({axis:i,ticks:o,bandSize:u,entry:g,index:m}),value:O,payload:g}}),w;return y||x?w=b.map(function(g){var m=Array.isArray(g.value)?g.value[0]:null;return v?{x:g.x,y:m!=null&&g.y!=null?i.scale(m):null}:{x:m!=null?n.scale(m):null,y:g.y}}):w=v?i.scale(h):n.scale(h),It({points:b,baseLine:w,layout:d,isRange:x},p)});ct(dr,"renderDotItem",function(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(Z(e))r=e(t);else{var n=re("recharts-area-dot",typeof e!="boolean"?e.className:""),i=t.key,a=Am(t,q3);r=S.createElement(po,rr({},a,{key:i,className:n}))}return r});function Zr(e){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(e)}function Q3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ez(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tm(n.key),n)}}function tz(e,t,r){return t&&ez(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function rz(e,t,r){return t=Fa(t),nz(e,_m()?Reflect.construct(t,r||[],Fa(e).constructor):t.apply(e,r))}function nz(e,t){if(t&&(Zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iz(e)}function iz(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _m(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_m=function(){return!!e})()}function Fa(e){return Fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fa(e)}function az(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_c(e,t)}function _c(e,t){return _c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_c(e,t)}function $m(e,t,r){return t=Tm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tm(e){var t=oz(e,"string");return Zr(t)=="symbol"?t:t+""}function oz(e,t){if(Zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $c(){return $c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$c.apply(this,arguments)}function uz(e){var t=e.xAxisId,r=ks(),n=Ds(),i=lm(t);return i==null?null:S.createElement(mn,$c({},i,{className:re("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return At(o,!0)}}))}var Po=function(e){function t(){return Q3(this,t),rz(this,t,arguments)}return az(t,e),tz(t,[{key:"render",value:function(){return S.createElement(uz,this.props)}}])}(S.Component);$m(Po,"displayName","XAxis");$m(Po,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Jr(e){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jr(e)}function cz(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sz(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Mm(n.key),n)}}function lz(e,t,r){return t&&sz(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function fz(e,t,r){return t=za(t),pz(e,Em()?Reflect.construct(t,r||[],za(e).constructor):t.apply(e,r))}function pz(e,t){if(t&&(Jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dz(e)}function dz(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Em(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Em=function(){return!!e})()}function za(e){return za=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},za(e)}function hz(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tc(e,t)}function Tc(e,t){return Tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tc(e,t)}function jm(e,t,r){return t=Mm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Mm(e){var t=vz(e,"string");return Jr(t)=="symbol"?t:t+""}function vz(e,t){if(Jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ec(){return Ec=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ec.apply(this,arguments)}var yz=function(t){var r=t.yAxisId,n=ks(),i=Ds(),a=fm(r);return a==null?null:S.createElement(mn,Ec({},a,{className:re("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return At(u,!0)}}))},_o=function(e){function t(){return cz(this,t),fz(this,t,arguments)}return hz(t,e),lz(t,[{key:"render",value:function(){return S.createElement(yz,this.props)}}])}(S.Component);jm(_o,"displayName","YAxis");jm(_o,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Bd(e){return xz(e)||bz(e)||gz(e)||mz()}function mz(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gz(e,t){if(e){if(typeof e=="string")return jc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jc(e,t)}}function bz(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function xz(e){if(Array.isArray(e))return jc(e)}function jc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Mc=function(t,r,n,i,a){var o=Qe(t,Rs),u=Qe(t,Oo),c=[].concat(Bd(o),Bd(u)),s=Qe(t,So),f="".concat(i,"Id"),l=i[0],p=r;if(c.length&&(p=c.reduce(function(h,v){if(v.props[f]===n&&ft(v.props,"extendDomain")&&z(v.props[l])){var x=v.props[l];return[Math.min(h[0],x),Math.max(h[1],x)]}return h},p)),s.length){var d="".concat(l,"1"),y="".concat(l,"2");p=s.reduce(function(h,v){if(v.props[f]===n&&ft(v.props,"extendDomain")&&z(v.props[d])&&z(v.props[y])){var x=v.props[d],b=v.props[y];return[Math.min(h[0],x,b),Math.max(h[1],x,b)]}return h},p)}return a&&a.length&&(p=a.reduce(function(h,v){return z(v)?[Math.min(h[0],v),Math.max(h[1],v)]:h},p)),p},Cm={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var d=new i(f,l||c,p),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],d]:c._events[y].push(d):(c._events[y]=d,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,d=l.length,y=new Array(d);p<d;p++)y[p]=l[p].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,p,d,y){var h=r?r+s:s;if(!this._events[h])return!1;var v=this._events[h],x=arguments.length,b,w;if(v.fn){switch(v.once&&this.removeListener(s,v.fn,void 0,!0),x){case 1:return v.fn.call(v.context),!0;case 2:return v.fn.call(v.context,f),!0;case 3:return v.fn.call(v.context,f,l),!0;case 4:return v.fn.call(v.context,f,l,p),!0;case 5:return v.fn.call(v.context,f,l,p,d),!0;case 6:return v.fn.call(v.context,f,l,p,d,y),!0}for(w=1,b=new Array(x-1);w<x;w++)b[w-1]=arguments[w];v.fn.apply(v.context,b)}else{var g=v.length,m;for(w=0;w<g;w++)switch(v[w].once&&this.removeListener(s,v[w].fn,void 0,!0),x){case 1:v[w].fn.call(v[w].context);break;case 2:v[w].fn.call(v[w].context,f);break;case 3:v[w].fn.call(v[w].context,f,l);break;case 4:v[w].fn.call(v[w].context,f,l,p);break;default:if(!b)for(m=1,b=new Array(x-1);m<x;m++)b[m-1]=arguments[m];v[w].fn.apply(v[w].context,b)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,p){var d=r?r+s:s;if(!this._events[d])return this;if(!f)return o(this,d),this;var y=this._events[d];if(y.fn)y.fn===f&&(!p||y.once)&&(!l||y.context===l)&&o(this,d);else{for(var h=0,v=[],x=y.length;h<x;h++)(y[h].fn!==f||p&&!y[h].once||l&&y[h].context!==l)&&v.push(y[h]);v.length?this._events[d]=v.length===1?v[0]:v:o(this,d)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(Cm);var wz=Cm.exports;const Oz=se(wz);var nu=new Oz,iu="recharts.syncMouseEvents";function hi(e){"@babel/helpers - typeof";return hi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hi(e)}function Az(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sz(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Im(n.key),n)}}function Pz(e,t,r){return t&&Sz(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function au(e,t,r){return t=Im(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Im(e){var t=_z(e,"string");return hi(t)=="symbol"?t:t+""}function _z(e,t){if(hi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(hi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var $z=function(){function e(){Az(this,e),au(this,"activeIndex",0),au(this,"coordinateList",[]),au(this,"layout","horizontal")}return Pz(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,p=r.mouseHandlerCallback,d=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=d??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:p})}}}])}();function Tz(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&z(n)&&z(i))return!0}return!1}function Ez(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function km(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=ve(t,r,n,i),u=ve(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function jz(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,p=ve(u,c,s,l),d=ve(u,c,f,l);n=p.x,i=p.y,a=d.x,o=d.y}else return km(t);return[{x:n,y:i},{x:a,y:o}]}function vi(e){"@babel/helpers - typeof";return vi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vi(e)}function Fd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Li(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fd(Object(r),!0).forEach(function(n){Mz(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mz(e,t,r){return t=Cz(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cz(e){var t=Iz(e,"string");return vi(t)=="symbol"?t:t+""}function Iz(e,t){if(vi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(vi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function kz(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,p=e.chartName,d=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!d||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var y,h=$r;if(p==="ScatterChart")y=o,h=tR;else if(p==="BarChart")y=Ez(l,o,c,f),h=js;else if(l==="radial"){var v=km(o),x=v.cx,b=v.cy,w=v.radius,g=v.startAngle,m=v.endAngle;y={cx:x,cy:b,startAngle:g,endAngle:m,innerRadius:w,outerRadius:w},h=Ay}else y={points:jz(l,o,c)},h=$r;var O=Li(Li(Li(Li({stroke:"#ccc",pointerEvents:"none"},c),y),Y(d,!1)),{},{payload:u,payloadIndex:s,className:re("recharts-tooltip-cursor",d.className)});return q.isValidElement(d)?q.cloneElement(d,O):q.createElement(h,O)}var Dz=["item"],Nz=["children","className","width","height","style","compact","title","desc"];function Qr(e){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(e)}function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dn.apply(this,arguments)}function zd(e,t){return Bz(e)||Lz(e,t)||Nm(e,t)||Rz()}function Rz(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Lz(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Bz(e){if(Array.isArray(e))return e}function Wd(e,t){if(e==null)return{};var r=Fz(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Fz(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function zz(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Wz(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rm(n.key),n)}}function Uz(e,t,r){return t&&Wz(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function qz(e,t,r){return t=Wa(t),Hz(e,Dm()?Reflect.construct(t,r||[],Wa(e).constructor):t.apply(e,r))}function Hz(e,t){if(t&&(Qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Kz(e)}function Kz(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dm=function(){return!!e})()}function Wa(e){return Wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wa(e)}function Gz(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cc(e,t)}function Cc(e,t){return Cc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cc(e,t)}function en(e){return Vz(e)||Xz(e)||Nm(e)||Yz()}function Yz(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Nm(e,t){if(e){if(typeof e=="string")return Ic(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ic(e,t)}}function Xz(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Vz(e){if(Array.isArray(e))return Ic(e)}function Ic(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ud(Object(r),!0).forEach(function(n){X(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ud(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function X(e,t,r){return t=Rm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rm(e){var t=Zz(e,"string");return Qr(t)=="symbol"?t:t+""}function Zz(e,t){if(Qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jz={xAxis:["bottom","top"],yAxis:["left","right"]},Qz={width:"100%",height:"100%"},Lm={x:0,y:0};function Bi(e){return e}var eW=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},tW=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return N(N(N({},i),ve(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return N(N(N({},i),ve(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return Lm},$o=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(en(u),en(s)):u},[]);return o.length>0?o:t&&t.length&&z(i)&&z(a)?t.slice(i,a+1):[]};function Bm(e){return e==="number"?[0,"auto"]:void 0}var kc=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=$o(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var d=l===void 0?u:l;p=zi(d,o.dataKey,i)}else p=l&&l[n]||u[n];return p?[].concat(en(c),[my(s,p)]):c},[])},qd=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=eW(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=Pk(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,p=kc(t,r,f,l),d=tW(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:p,activeCoordinate:d}}return null},rW=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=t.stackOffset,d=fy(f,a);return n.reduce(function(y,h){var v,x=h.type.defaultProps!==void 0?N(N({},h.type.defaultProps),h.props):h.props,b=x.type,w=x.dataKey,g=x.allowDataOverflow,m=x.allowDuplicatedCategory,O=x.scale,A=x.ticks,P=x.includeHidden,j=x[o];if(y[j])return y;var M=$o(t.data,{graphicalItems:i.filter(function(F){var H,J=o in F.props?F.props[o]:(H=F.type.defaultProps)===null||H===void 0?void 0:H[o];return J===j}),dataStartIndex:c,dataEndIndex:s}),T=M.length,C,_,$;Tz(x.domain,g,b)&&(C=Gu(x.domain,null,g),d&&(b==="number"||O!=="auto")&&($=Mn(M,w,"category")));var k=Bm(b);if(!C||C.length===0){var I,D=(I=x.domain)!==null&&I!==void 0?I:k;if(w){if(C=Mn(M,w,b),b==="category"&&d){var R=yx(C);m&&R?(_=C,C=Ta(0,T)):m||(C=fp(D,C,h).reduce(function(F,H){return F.indexOf(H)>=0?F:[].concat(en(F),[H])},[]))}else if(b==="category")m?C=C.filter(function(F){return F!==""&&!ee(F)}):C=fp(D,C,h).reduce(function(F,H){return F.indexOf(H)>=0||H===""||ee(H)?F:[].concat(en(F),[H])},[]);else if(b==="number"){var L=jk(M,i.filter(function(F){var H,J,ne=o in F.props?F.props[o]:(H=F.type.defaultProps)===null||H===void 0?void 0:H[o],we="hide"in F.props?F.props.hide:(J=F.type.defaultProps)===null||J===void 0?void 0:J.hide;return ne===j&&(P||!we)}),w,a,f);L&&(C=L)}d&&(b==="number"||O!=="auto")&&($=Mn(M,w,"category"))}else d?C=Ta(0,T):u&&u[j]&&u[j].hasStack&&b==="number"?C=p==="expand"?[0,1]:yy(u[j].stackGroups,c,s):C=ly(M,i.filter(function(F){var H=o in F.props?F.props[o]:F.type.defaultProps[o],J="hide"in F.props?F.props.hide:F.type.defaultProps.hide;return H===j&&(P||!J)}),b,f,!0);if(b==="number")C=Mc(l,C,j,a,A),D&&(C=Gu(D,C,g));else if(b==="category"&&D){var U=D,B=C.every(function(F){return U.indexOf(F)>=0});B&&(C=U)}}return N(N({},y),{},X({},j,N(N({},x),{},{axisType:a,domain:C,categoricalDomain:$,duplicateDomain:_,originalDomain:(v=x.domain)!==null&&v!==void 0?v:k,isCategorical:d,layout:f})))},{})},nW=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=$o(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),d=p.length,y=fy(f,a),h=-1;return n.reduce(function(v,x){var b=x.type.defaultProps!==void 0?N(N({},x.type.defaultProps),x.props):x.props,w=b[o],g=Bm("number");if(!v[w]){h++;var m;return y?m=Ta(0,d):u&&u[w]&&u[w].hasStack?(m=yy(u[w].stackGroups,c,s),m=Mc(l,m,w,a)):(m=Gu(g,ly(p,n.filter(function(O){var A,P,j=o in O.props?O.props[o]:(A=O.type.defaultProps)===null||A===void 0?void 0:A[o],M="hide"in O.props?O.props.hide:(P=O.type.defaultProps)===null||P===void 0?void 0:P.hide;return j===w&&!M}),"number",f),i.defaultProps.allowDataOverflow),m=Mc(l,m,w,a)),N(N({},v),{},X({},w,N(N({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ge(Jz,"".concat(a,".").concat(h%2),null),domain:m,originalDomain:g,isCategorical:y,layout:f})))}return v},{})},iW=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),p=Qe(f,a),d={};return p&&p.length?d=rW(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(d=nW(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),d},aW=function(t){var r=Dt(t),n=At(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:rs(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:va(r,n)}},Hd=function(t){var r=t.children,n=t.defaultShowTooltip,i=He(r,Ur),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},oW=function(t){return!t||!t.length?!1:t.some(function(r){var n=St(r&&r.type);return n&&n.indexOf("Bar")>=0})},Kd=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},uW=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,p=n.margin||{},d=He(l,Ur),y=He(l,Sr),h=Object.keys(c).reduce(function(m,O){var A=c[O],P=A.orientation;return!A.mirror&&!A.hide?N(N({},m),{},X({},P,m[P]+A.width)):m},{left:p.left||0,right:p.right||0}),v=Object.keys(o).reduce(function(m,O){var A=o[O],P=A.orientation;return!A.mirror&&!A.hide?N(N({},m),{},X({},P,Ge(m,"".concat(P))+A.height)):m},{top:p.top||0,bottom:p.bottom||0}),x=N(N({},v),h),b=x.bottom;d&&(x.bottom+=d.props.height||Ur.defaultProps.height),y&&r&&(x=Tk(x,i,n,r));var w=s-x.left-x.right,g=f-x.top-x.bottom;return N(N({brushBottom:b},x),{},{width:Math.max(w,0),height:Math.max(g,0)})},cW=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Fs=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,p=function(v,x){var b=x.graphicalItems,w=x.stackGroups,g=x.offset,m=x.updateId,O=x.dataStartIndex,A=x.dataEndIndex,P=v.barSize,j=v.layout,M=v.barGap,T=v.barCategoryGap,C=v.maxBarSize,_=Kd(j),$=_.numericAxisName,k=_.cateAxisName,I=oW(b),D=[];return b.forEach(function(R,L){var U=$o(v.data,{graphicalItems:[R],dataStartIndex:O,dataEndIndex:A}),B=R.type.defaultProps!==void 0?N(N({},R.type.defaultProps),R.props):R.props,F=B.dataKey,H=B.maxBarSize,J=B["".concat($,"Id")],ne=B["".concat(k,"Id")],we={},Oe=c.reduce(function(qt,mt){var To,Eo,jo=x["".concat(mt.axisType,"Map")],zs=B["".concat(mt.axisType,"Id")];jo&&jo[zs]||mt.axisType==="zAxis"||cr(!1,"Specifying a(n) ".concat(mt.axisType,"Id requires a corresponding ").concat(mt.axisType,"Id on the targeted graphical component ").concat((To=R==null||(Eo=R.type)===null||Eo===void 0?void 0:Eo.displayName)!==null&&To!==void 0?To:""));var Ws=jo[zs];return N(N({},qt),{},X(X({},mt.axisType,Ws),"".concat(mt.axisType,"Ticks"),At(Ws)))},we),Ue=Oe[k],K=Oe["".concat(k,"Ticks")],V=w&&w[J]&&w[J].hasStack&&Bk(R,w[J].stackGroups),Q=St(R.type).indexOf("Bar")>=0,E=va(Ue,K),le=[],W=I&&_k({barSize:P,stackGroups:w,totalSize:cW(Oe,k)});if(Q){var pe,ye,Ie=ee(H)?C:H,yt=(pe=(ye=va(Ue,K,!0))!==null&&ye!==void 0?ye:Ie)!==null&&pe!==void 0?pe:0;le=$k({barGap:M,barCategoryGap:T,bandSize:yt!==E?yt:E,sizeList:W[ne],maxBarSize:Ie}),yt!==E&&(le=le.map(function(qt){return N(N({},qt),{},{position:N(N({},qt.position),{},{offset:qt.position.offset-yt/2})})}))}var Ai=R&&R.type&&R.type.getComposedData;Ai&&D.push({props:N(N({},Ai(N(N({},Oe),{},{displayedData:U,props:v,dataKey:F,item:R,bandSize:E,barPosition:le,offset:g,stackedData:V,layout:j,dataStartIndex:O,dataEndIndex:A}))),{},X(X(X({key:R.key||"item-".concat(L)},$,Oe[$]),k,Oe[k]),"animationId",m)),childIndex:Tx(R,v.children),item:R})}),D},d=function(v,x){var b=v.props,w=v.dataStartIndex,g=v.dataEndIndex,m=v.updateId;if(!rl({props:b}))return null;var O=b.children,A=b.layout,P=b.stackOffset,j=b.data,M=b.reverseStackOrder,T=Kd(A),C=T.numericAxisName,_=T.cateAxisName,$=Qe(O,n),k=Rk(j,$,"".concat(C,"Id"),"".concat(_,"Id"),P,M),I=c.reduce(function(B,F){var H="".concat(F.axisType,"Map");return N(N({},B),{},X({},H,iW(b,N(N({},F),{},{graphicalItems:$,stackGroups:F.axisType===C&&k,dataStartIndex:w,dataEndIndex:g}))))},{}),D=uW(N(N({},I),{},{props:b,graphicalItems:$}),x==null?void 0:x.legendBBox);Object.keys(I).forEach(function(B){I[B]=f(b,I[B],D,B.replace("Map",""),r)});var R=I["".concat(_,"Map")],L=aW(R),U=p(b,N(N({},I),{},{dataStartIndex:w,dataEndIndex:g,updateId:m,graphicalItems:$,stackGroups:k,offset:D}));return N(N({formattedGraphicalItems:U,graphicalItems:$,offset:D,stackGroups:k},L),I)},y=function(h){function v(x){var b,w,g;return zz(this,v),g=qz(this,v,[x]),X(g,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),X(g,"accessibilityManager",new $z),X(g,"handleLegendBBoxUpdate",function(m){if(m){var O=g.state,A=O.dataStartIndex,P=O.dataEndIndex,j=O.updateId;g.setState(N({legendBBox:m},d({props:g.props,dataStartIndex:A,dataEndIndex:P,updateId:j},N(N({},g.state),{},{legendBBox:m}))))}}),X(g,"handleReceiveSyncEvent",function(m,O,A){if(g.props.syncId===m){if(A===g.eventEmitterSymbol&&typeof g.props.syncMethod!="function")return;g.applySyncEvent(O)}}),X(g,"handleBrushChange",function(m){var O=m.startIndex,A=m.endIndex;if(O!==g.state.dataStartIndex||A!==g.state.dataEndIndex){var P=g.state.updateId;g.setState(function(){return N({dataStartIndex:O,dataEndIndex:A},d({props:g.props,dataStartIndex:O,dataEndIndex:A,updateId:P},g.state))}),g.triggerSyncEvent({dataStartIndex:O,dataEndIndex:A})}}),X(g,"handleMouseEnter",function(m){var O=g.getMouseInfo(m);if(O){var A=N(N({},O),{},{isTooltipActive:!0});g.setState(A),g.triggerSyncEvent(A);var P=g.props.onMouseEnter;Z(P)&&P(A,m)}}),X(g,"triggeredAfterMouseMove",function(m){var O=g.getMouseInfo(m),A=O?N(N({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};g.setState(A),g.triggerSyncEvent(A);var P=g.props.onMouseMove;Z(P)&&P(A,m)}),X(g,"handleItemMouseEnter",function(m){g.setState(function(){return{isTooltipActive:!0,activeItem:m,activePayload:m.tooltipPayload,activeCoordinate:m.tooltipPosition||{x:m.cx,y:m.cy}}})}),X(g,"handleItemMouseLeave",function(){g.setState(function(){return{isTooltipActive:!1}})}),X(g,"handleMouseMove",function(m){m.persist(),g.throttleTriggeredAfterMouseMove(m)}),X(g,"handleMouseLeave",function(m){g.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};g.setState(O),g.triggerSyncEvent(O);var A=g.props.onMouseLeave;Z(A)&&A(O,m)}),X(g,"handleOuterEvent",function(m){var O=$x(m),A=Ge(g.props,"".concat(O));if(O&&Z(A)){var P,j;/.*touch.*/i.test(O)?j=g.getMouseInfo(m.changedTouches[0]):j=g.getMouseInfo(m),A((P=j)!==null&&P!==void 0?P:{},m)}}),X(g,"handleClick",function(m){var O=g.getMouseInfo(m);if(O){var A=N(N({},O),{},{isTooltipActive:!0});g.setState(A),g.triggerSyncEvent(A);var P=g.props.onClick;Z(P)&&P(A,m)}}),X(g,"handleMouseDown",function(m){var O=g.props.onMouseDown;if(Z(O)){var A=g.getMouseInfo(m);O(A,m)}}),X(g,"handleMouseUp",function(m){var O=g.props.onMouseUp;if(Z(O)){var A=g.getMouseInfo(m);O(A,m)}}),X(g,"handleTouchMove",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.throttleTriggeredAfterMouseMove(m.changedTouches[0])}),X(g,"handleTouchStart",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseDown(m.changedTouches[0])}),X(g,"handleTouchEnd",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseUp(m.changedTouches[0])}),X(g,"triggerSyncEvent",function(m){g.props.syncId!==void 0&&nu.emit(iu,g.props.syncId,m,g.eventEmitterSymbol)}),X(g,"applySyncEvent",function(m){var O=g.props,A=O.layout,P=O.syncMethod,j=g.state.updateId,M=m.dataStartIndex,T=m.dataEndIndex;if(m.dataStartIndex!==void 0||m.dataEndIndex!==void 0)g.setState(N({dataStartIndex:M,dataEndIndex:T},d({props:g.props,dataStartIndex:M,dataEndIndex:T,updateId:j},g.state)));else if(m.activeTooltipIndex!==void 0){var C=m.chartX,_=m.chartY,$=m.activeTooltipIndex,k=g.state,I=k.offset,D=k.tooltipTicks;if(!I)return;if(typeof P=="function")$=P(D,m);else if(P==="value"){$=-1;for(var R=0;R<D.length;R++)if(D[R].value===m.activeLabel){$=R;break}}var L=N(N({},I),{},{x:I.left,y:I.top}),U=Math.min(C,L.x+L.width),B=Math.min(_,L.y+L.height),F=D[$]&&D[$].value,H=kc(g.state,g.props.data,$),J=D[$]?{x:A==="horizontal"?D[$].coordinate:U,y:A==="horizontal"?B:D[$].coordinate}:Lm;g.setState(N(N({},m),{},{activeLabel:F,activeCoordinate:J,activePayload:H,activeTooltipIndex:$}))}else g.setState(m)}),X(g,"renderCursor",function(m){var O,A=g.state,P=A.isTooltipActive,j=A.activeCoordinate,M=A.activePayload,T=A.offset,C=A.activeTooltipIndex,_=A.tooltipAxisBandSize,$=g.getTooltipEventType(),k=(O=m.props.active)!==null&&O!==void 0?O:P,I=g.props.layout,D=m.key||"_recharts-cursor";return S.createElement(kz,{key:D,activeCoordinate:j,activePayload:M,activeTooltipIndex:C,chartName:r,element:m,isActive:k,layout:I,offset:T,tooltipAxisBandSize:_,tooltipEventType:$})}),X(g,"renderPolarAxis",function(m,O,A){var P=Ge(m,"type.axisType"),j=Ge(g.state,"".concat(P,"Map")),M=m.type.defaultProps,T=M!==void 0?N(N({},M),m.props):m.props,C=j&&j[T["".concat(P,"Id")]];return q.cloneElement(m,N(N({},C),{},{className:re(P,C.className),key:m.key||"".concat(O,"-").concat(A),ticks:At(C,!0)}))}),X(g,"renderPolarGrid",function(m){var O=m.props,A=O.radialLines,P=O.polarAngles,j=O.polarRadius,M=g.state,T=M.radiusAxisMap,C=M.angleAxisMap,_=Dt(T),$=Dt(C),k=$.cx,I=$.cy,D=$.innerRadius,R=$.outerRadius;return q.cloneElement(m,{polarAngles:Array.isArray(P)?P:At($,!0).map(function(L){return L.coordinate}),polarRadius:Array.isArray(j)?j:At(_,!0).map(function(L){return L.coordinate}),cx:k,cy:I,innerRadius:D,outerRadius:R,key:m.key||"polar-grid",radialLines:A})}),X(g,"renderLegend",function(){var m=g.state.formattedGraphicalItems,O=g.props,A=O.children,P=O.width,j=O.height,M=g.props.margin||{},T=P-(M.left||0)-(M.right||0),C=cy({children:A,formattedGraphicalItems:m,legendWidth:T,legendContent:s});if(!C)return null;var _=C.item,$=Wd(C,Dz);return q.cloneElement(_,N(N({},$),{},{chartWidth:P,chartHeight:j,margin:M,onBBoxUpdate:g.handleLegendBBoxUpdate}))}),X(g,"renderTooltip",function(){var m,O=g.props,A=O.children,P=O.accessibilityLayer,j=He(A,bt);if(!j)return null;var M=g.state,T=M.isTooltipActive,C=M.activeCoordinate,_=M.activePayload,$=M.activeLabel,k=M.offset,I=(m=j.props.active)!==null&&m!==void 0?m:T;return q.cloneElement(j,{viewBox:N(N({},k),{},{x:k.left,y:k.top}),active:I,label:$,payload:I?_:[],coordinate:C,accessibilityLayer:P})}),X(g,"renderBrush",function(m){var O=g.props,A=O.margin,P=O.data,j=g.state,M=j.offset,T=j.dataStartIndex,C=j.dataEndIndex,_=j.updateId;return q.cloneElement(m,{key:m.key||"_recharts-brush",onChange:Ii(g.handleBrushChange,m.props.onChange),data:P,x:z(m.props.x)?m.props.x:M.left,y:z(m.props.y)?m.props.y:M.top+M.height+M.brushBottom-(A.bottom||0),width:z(m.props.width)?m.props.width:M.width,startIndex:T,endIndex:C,updateId:"brush-".concat(_)})}),X(g,"renderReferenceElement",function(m,O,A){if(!m)return null;var P=g,j=P.clipPathId,M=g.state,T=M.xAxisMap,C=M.yAxisMap,_=M.offset,$=m.type.defaultProps||{},k=m.props,I=k.xAxisId,D=I===void 0?$.xAxisId:I,R=k.yAxisId,L=R===void 0?$.yAxisId:R;return q.cloneElement(m,{key:m.key||"".concat(O,"-").concat(A),xAxis:T[D],yAxis:C[L],viewBox:{x:_.left,y:_.top,width:_.width,height:_.height},clipPathId:j})}),X(g,"renderActivePoints",function(m){var O=m.item,A=m.activePoint,P=m.basePoint,j=m.childIndex,M=m.isRange,T=[],C=O.props.key,_=O.item.type.defaultProps!==void 0?N(N({},O.item.type.defaultProps),O.item.props):O.item.props,$=_.activeDot,k=_.dataKey,I=N(N({index:j,dataKey:k,cx:A.x,cy:A.y,r:4,fill:Es(O.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},Y($,!1)),Wi($));return T.push(v.renderActiveDot($,I,"".concat(C,"-activePoint-").concat(j))),P?T.push(v.renderActiveDot($,N(N({},I),{},{cx:P.x,cy:P.y}),"".concat(C,"-basePoint-").concat(j))):M&&T.push(null),T}),X(g,"renderGraphicChild",function(m,O,A){var P=g.filterFormatItem(m,O,A);if(!P)return null;var j=g.getTooltipEventType(),M=g.state,T=M.isTooltipActive,C=M.tooltipAxis,_=M.activeTooltipIndex,$=M.activeLabel,k=g.props.children,I=He(k,bt),D=P.props,R=D.points,L=D.isRange,U=D.baseLine,B=P.item.type.defaultProps!==void 0?N(N({},P.item.type.defaultProps),P.item.props):P.item.props,F=B.activeDot,H=B.hide,J=B.activeBar,ne=B.activeShape,we=!!(!H&&T&&I&&(F||J||ne)),Oe={};j!=="axis"&&I&&I.props.trigger==="click"?Oe={onClick:Ii(g.handleItemMouseEnter,m.props.onClick)}:j!=="axis"&&(Oe={onMouseLeave:Ii(g.handleItemMouseLeave,m.props.onMouseLeave),onMouseEnter:Ii(g.handleItemMouseEnter,m.props.onMouseEnter)});var Ue=q.cloneElement(m,N(N({},P.props),Oe));function K(mt){return typeof C.dataKey=="function"?C.dataKey(mt.payload):null}if(we)if(_>=0){var V,Q;if(C.dataKey&&!C.allowDuplicatedCategory){var E=typeof C.dataKey=="function"?K:"payload.".concat(C.dataKey.toString());V=zi(R,E,$),Q=L&&U&&zi(U,E,$)}else V=R==null?void 0:R[_],Q=L&&U&&U[_];if(ne||J){var le=m.props.activeIndex!==void 0?m.props.activeIndex:_;return[q.cloneElement(m,N(N(N({},P.props),Oe),{},{activeIndex:le})),null,null]}if(!ee(V))return[Ue].concat(en(g.renderActivePoints({item:P,activePoint:V,basePoint:Q,childIndex:_,isRange:L})))}else{var W,pe=(W=g.getItemByXY(g.state.activeCoordinate))!==null&&W!==void 0?W:{graphicalItem:Ue},ye=pe.graphicalItem,Ie=ye.item,yt=Ie===void 0?m:Ie,Ai=ye.childIndex,qt=N(N(N({},P.props),Oe),{},{activeIndex:Ai});return[q.cloneElement(yt,qt),null,null]}return L?[Ue,null,null]:[Ue,null]}),X(g,"renderCustomized",function(m,O,A){return q.cloneElement(m,N(N({key:"recharts-customized-".concat(A)},g.props),g.state))}),X(g,"renderMap",{CartesianGrid:{handler:Bi,once:!0},ReferenceArea:{handler:g.renderReferenceElement},ReferenceLine:{handler:Bi},ReferenceDot:{handler:g.renderReferenceElement},XAxis:{handler:Bi},YAxis:{handler:Bi},Brush:{handler:g.renderBrush,once:!0},Bar:{handler:g.renderGraphicChild},Line:{handler:g.renderGraphicChild},Area:{handler:g.renderGraphicChild},Radar:{handler:g.renderGraphicChild},RadialBar:{handler:g.renderGraphicChild},Scatter:{handler:g.renderGraphicChild},Pie:{handler:g.renderGraphicChild},Funnel:{handler:g.renderGraphicChild},Tooltip:{handler:g.renderCursor,once:!0},PolarGrid:{handler:g.renderPolarGrid,once:!0},PolarAngleAxis:{handler:g.renderPolarAxis},PolarRadiusAxis:{handler:g.renderPolarAxis},Customized:{handler:g.renderCustomized}}),g.clipPathId="".concat((b=x.id)!==null&&b!==void 0?b:cn("recharts"),"-clip"),g.throttleTriggeredAfterMouseMove=uv(g.triggeredAfterMouseMove,(w=x.throttleDelay)!==null&&w!==void 0?w:1e3/60),g.state={},g}return Gz(v,h),Uz(v,[{key:"componentDidMount",value:function(){var b,w;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(b=this.props.margin.left)!==null&&b!==void 0?b:0,top:(w=this.props.margin.top)!==null&&w!==void 0?w:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var b=this.props,w=b.children,g=b.data,m=b.height,O=b.layout,A=He(w,bt);if(A){var P=A.props.defaultIndex;if(!(typeof P!="number"||P<0||P>this.state.tooltipTicks.length-1)){var j=this.state.tooltipTicks[P]&&this.state.tooltipTicks[P].value,M=kc(this.state,g,P,j),T=this.state.tooltipTicks[P].coordinate,C=(this.state.offset.top+m)/2,_=O==="horizontal",$=_?{x:T,y:C}:{y:T,x:C},k=this.state.formattedGraphicalItems.find(function(D){var R=D.item;return R.type.name==="Scatter"});k&&($=N(N({},$),k.props.points[P].tooltipPosition),M=k.props.points[P].tooltipPayload);var I={activeTooltipIndex:P,isTooltipActive:!0,activeLabel:j,activePayload:M,activeCoordinate:$};this.setState(I),this.renderCursor(A),this.accessibilityManager.setIndex(P)}}}},{key:"getSnapshotBeforeUpdate",value:function(b,w){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==w.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==b.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==b.margin){var g,m;this.accessibilityManager.setDetails({offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(m=this.props.margin.top)!==null&&m!==void 0?m:0}})}return null}},{key:"componentDidUpdate",value:function(b){cu([He(b.children,bt)],[He(this.props.children,bt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var b=He(this.props.children,bt);if(b&&typeof b.props.shared=="boolean"){var w=b.props.shared?"axis":"item";return u.indexOf(w)>=0?w:a}return a}},{key:"getMouseInfo",value:function(b){if(!this.container)return null;var w=this.container,g=w.getBoundingClientRect(),m=ej(g),O={chartX:Math.round(b.pageX-m.left),chartY:Math.round(b.pageY-m.top)},A=g.width/w.offsetWidth||1,P=this.inRange(O.chartX,O.chartY,A);if(!P)return null;var j=this.state,M=j.xAxisMap,T=j.yAxisMap,C=this.getTooltipEventType();if(C!=="axis"&&M&&T){var _=Dt(M).scale,$=Dt(T).scale,k=_&&_.invert?_.invert(O.chartX):null,I=$&&$.invert?$.invert(O.chartY):null;return N(N({},O),{},{xValue:k,yValue:I})}var D=qd(this.state,this.props.data,this.props.layout,P);return D?N(N({},O),D):null}},{key:"inRange",value:function(b,w){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,m=this.props.layout,O=b/g,A=w/g;if(m==="horizontal"||m==="vertical"){var P=this.state.offset,j=O>=P.left&&O<=P.left+P.width&&A>=P.top&&A<=P.top+P.height;return j?{x:O,y:A}:null}var M=this.state,T=M.angleAxisMap,C=M.radiusAxisMap;if(T&&C){var _=Dt(T);return hp({x:O,y:A},_)}return null}},{key:"parseEventsOfWrapper",value:function(){var b=this.props.children,w=this.getTooltipEventType(),g=He(b,bt),m={};g&&w==="axis"&&(g.props.trigger==="click"?m={onClick:this.handleClick}:m={onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd});var O=Wi(this.props,this.handleOuterEvent);return N(N({},O),m)}},{key:"addListener",value:function(){nu.on(iu,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){nu.removeListener(iu,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(b,w,g){for(var m=this.state.formattedGraphicalItems,O=0,A=m.length;O<A;O++){var P=m[O];if(P.item===b||P.props.key===b.key||w===St(P.item.type)&&g===P.childIndex)return P}return null}},{key:"renderClipPath",value:function(){var b=this.clipPathId,w=this.state.offset,g=w.left,m=w.top,O=w.height,A=w.width;return S.createElement("defs",null,S.createElement("clipPath",{id:b},S.createElement("rect",{x:g,y:m,height:O,width:A})))}},{key:"getXScales",value:function(){var b=this.state.xAxisMap;return b?Object.entries(b).reduce(function(w,g){var m=zd(g,2),O=m[0],A=m[1];return N(N({},w),{},X({},O,A.scale))},{}):null}},{key:"getYScales",value:function(){var b=this.state.yAxisMap;return b?Object.entries(b).reduce(function(w,g){var m=zd(g,2),O=m[0],A=m[1];return N(N({},w),{},X({},O,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(b){var w;return(w=this.state.xAxisMap)===null||w===void 0||(w=w[b])===null||w===void 0?void 0:w.scale}},{key:"getYScaleByAxisId",value:function(b){var w;return(w=this.state.yAxisMap)===null||w===void 0||(w=w[b])===null||w===void 0?void 0:w.scale}},{key:"getItemByXY",value:function(b){var w=this.state,g=w.formattedGraphicalItems,m=w.activeItem;if(g&&g.length)for(var O=0,A=g.length;O<A;O++){var P=g[O],j=P.props,M=P.item,T=M.type.defaultProps!==void 0?N(N({},M.type.defaultProps),M.props):M.props,C=St(M.type);if(C==="Bar"){var _=(j.data||[]).find(function(D){return DN(b,D)});if(_)return{graphicalItem:P,payload:_}}else if(C==="RadialBar"){var $=(j.data||[]).find(function(D){return hp(b,D)});if($)return{graphicalItem:P,payload:$}}else if(go(P,m)||bo(P,m)||li(P,m)){var k=AL({graphicalItem:P,activeTooltipItem:m,itemData:T.data}),I=T.activeIndex===void 0?k:T.activeIndex;return{graphicalItem:N(N({},P),{},{childIndex:I}),payload:li(P,m)?T.data[k]:P.props.data[k]}}}return null}},{key:"render",value:function(){var b=this;if(!rl(this))return null;var w=this.props,g=w.children,m=w.className,O=w.width,A=w.height,P=w.style,j=w.compact,M=w.title,T=w.desc,C=Wd(w,Nz),_=Y(C,!1);if(j)return S.createElement(Sd,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement(lu,Dn({},_,{width:O,height:A,title:M,desc:T}),this.renderClipPath(),il(g,this.renderMap)));if(this.props.accessibilityLayer){var $,k;_.tabIndex=($=this.props.tabIndex)!==null&&$!==void 0?$:0,_.role=(k=this.props.role)!==null&&k!==void 0?k:"application",_.onKeyDown=function(D){b.accessibilityManager.keyboardEvent(D)},_.onFocus=function(){b.accessibilityManager.focus()}}var I=this.parseEventsOfWrapper();return S.createElement(Sd,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement("div",Dn({className:re("recharts-wrapper",m),style:N({position:"relative",cursor:"default",width:O,height:A},P)},I,{ref:function(R){b.container=R}}),S.createElement(lu,Dn({},_,{width:O,height:A,title:M,desc:T,style:Qz}),this.renderClipPath(),il(g,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);return X(y,"displayName",r),X(y,"defaultProps",N({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),X(y,"getDerivedStateFromProps",function(h,v){var x=h.dataKey,b=h.data,w=h.children,g=h.width,m=h.height,O=h.layout,A=h.stackOffset,P=h.margin,j=v.dataStartIndex,M=v.dataEndIndex;if(v.updateId===void 0){var T=Hd(h);return N(N(N({},T),{},{updateId:0},d(N(N({props:h},T),{},{updateId:0}),v)),{},{prevDataKey:x,prevData:b,prevWidth:g,prevHeight:m,prevLayout:O,prevStackOffset:A,prevMargin:P,prevChildren:w})}if(x!==v.prevDataKey||b!==v.prevData||g!==v.prevWidth||m!==v.prevHeight||O!==v.prevLayout||A!==v.prevStackOffset||!Ar(P,v.prevMargin)){var C=Hd(h),_={chartX:v.chartX,chartY:v.chartY,isTooltipActive:v.isTooltipActive},$=N(N({},qd(v,b,O)),{},{updateId:v.updateId+1}),k=N(N(N({},C),_),$);return N(N(N({},k),d(N({props:h},k),v)),{},{prevDataKey:x,prevData:b,prevWidth:g,prevHeight:m,prevLayout:O,prevStackOffset:A,prevMargin:P,prevChildren:w})}if(!cu(w,v.prevChildren)){var I,D,R,L,U=He(w,Ur),B=U&&(I=(D=U.props)===null||D===void 0?void 0:D.startIndex)!==null&&I!==void 0?I:j,F=U&&(R=(L=U.props)===null||L===void 0?void 0:L.endIndex)!==null&&R!==void 0?R:M,H=B!==j||F!==M,J=!ee(b),ne=J&&!H?v.updateId:v.updateId+1;return N(N({updateId:ne},d(N(N({props:h},v),{},{updateId:ne,dataStartIndex:B,dataEndIndex:F}),v)),{},{prevChildren:w,dataStartIndex:B,dataEndIndex:F})}return null}),X(y,"renderActiveDot",function(h,v,x){var b;return q.isValidElement(h)?b=q.cloneElement(h,v):Z(h)?b=h(v):b=S.createElement(po,v),S.createElement(ie,{className:"recharts-active-dot",key:x},b)}),function(v){return S.createElement(y,v)}},pW=Fs({chartName:"BarChart",GraphicalChild:yn,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Po},{axisType:"yAxis",AxisComp:_o}],formatAxisMap:tm}),dW=Fs({chartName:"PieChart",GraphicalChild:Ut,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:mo},{axisType:"radiusAxis",AxisComp:vo}],formatAxisMap:Xk,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),hW=Fs({chartName:"AreaChart",GraphicalChild:dr,axisComponents:[{axisType:"xAxis",AxisComp:Po},{axisType:"yAxis",AxisComp:_o}],formatAxisMap:tm});export{hW as A,pW as B,is as C,dW as P,fW as R,bt as T,Po as X,_o as Y,Ut as a,W3 as b,dr as c};

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useSearchParams, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FileText, Plus, Minus, Eye, EyeOff } from 'lucide-react';
import { commitApi } from '@/lib/api';
import type { CommitDetail, CommitFile } from '@/types/api';
import { toast } from '@/hooks/use-toast';

// 类型定义
interface DiffLine {
  type: 'add' | 'remove' | 'normal';
  oldNumber: number | null;
  newNumber: number | null;
  content: string;
}

interface FileDiffData {
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  beforeContent: string;
  afterContent: string;
  diffLines: DiffLine[];
}

const FileDiff = () => {
  const { hash, filename } = useParams<{ hash: string; filename: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [activeTab, setActiveTab] = useState('diff');
  const [fileDiff, setFileDiff] = useState<FileDiffData | null>(null);
  const [loading, setLoading] = useState(true);

  const decodedFilename = decodeURIComponent(filename || '');

  // 加载提交和文件数据
  const loadFileDiff = async () => {
    if (!hash || !filename) return;
    
    try {
      setLoading(true);
      const commitDetail = await commitApi.getCommit(hash);
      const file = commitDetail.files.find(f => f.filename === decodedFilename);
      
      if (!file) {
        toast({
          title: "文件不存在",
          description: "在此提交中未找到指定文件",
          variant: "destructive",
        });
        return;
      }

      // 解析diff数据
      const diffData = parsePatchToDiffLines(file.patch || '');
      
      setFileDiff({
        filename: file.filename,
        status: file.status,
        additions: file.additions,
        deletions: file.deletions,
        beforeContent: extractBeforeContent(file.patch || ''),
        afterContent: extractAfterContent(file.patch || ''),
        diffLines: diffData,
      });
    } catch (error) {
      console.error('Failed to load file diff:', error);
      toast({
        title: "加载失败",
        description: "无法加载文件差异",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFileDiff();
  }, [hash, filename]);

  // 处理返回
  const handleGoBack = () => {
    const from = searchParams.get('from');
    if (from) {
      navigate(decodeURIComponent(from));
    } else {
      navigate(`/commit/${hash}`);
    }
  };

  // 解析patch数据为diff行格式
  const parsePatchToDiffLines = (patch: string): DiffLine[] => {
    if (!patch) return [];
    
    const lines = patch.split('\n');
    const diffLines: DiffLine[] = [];
    let oldLineNumber = 0;
    let newLineNumber = 0;
    
    // 解析diff头部信息
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 跳过文件头信息
      if (line.startsWith('@@')) {
        // 解析行号信息，格式: @@ -oldStart,oldCount +newStart,newCount @@
        const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);
        if (match) {
          oldLineNumber = parseInt(match[1]) - 1;
          newLineNumber = parseInt(match[2]) - 1;
        }
        continue;
      }
      
      if (line.startsWith('+++') || line.startsWith('---') || line.startsWith('diff --git')) {
        continue;
      }
      
      // 处理diff内容行
      if (line.startsWith('+')) {
        newLineNumber++;
        diffLines.push({
          type: 'add',
          oldNumber: null,
          newNumber: newLineNumber,
          content: line.substring(1)
        });
      } else if (line.startsWith('-')) {
        oldLineNumber++;
        diffLines.push({
          type: 'remove',
          oldNumber: oldLineNumber,
          newNumber: null,
          content: line.substring(1)
        });
      } else if (line.startsWith(' ') || line === '') {
        oldLineNumber++;
        newLineNumber++;
        diffLines.push({
          type: 'normal',
          oldNumber: oldLineNumber,
          newNumber: newLineNumber,
          content: line.startsWith(' ') ? line.substring(1) : line
        });
      }
    }
    
    return diffLines;
  };

  // 提取修改前的内容
  const extractBeforeContent = (patch: string): string => {
    if (!patch) return '';
    
    const lines = patch.split('\n');
    const beforeLines: string[] = [];
    
    for (const line of lines) {
      if (line.startsWith('@@') || line.startsWith('+++') || line.startsWith('---') || line.startsWith('diff --git')) {
        continue;
      }
      
      if (line.startsWith('-')) {
        beforeLines.push(line.substring(1));
      } else if (line.startsWith(' ') || line === '') {
        beforeLines.push(line.startsWith(' ') ? line.substring(1) : line);
      }
    }
    
    return beforeLines.join('\n');
  };

  // 提取修改后的内容
  const extractAfterContent = (patch: string): string => {
    if (!patch) return '';
    
    const lines = patch.split('\n');
    const afterLines: string[] = [];
    
    for (const line of lines) {
      if (line.startsWith('@@') || line.startsWith('+++') || line.startsWith('---') || line.startsWith('diff --git')) {
        continue;
      }
      
      if (line.startsWith('+')) {
        afterLines.push(line.substring(1));
      } else if (line.startsWith(' ') || line === '') {
        afterLines.push(line.startsWith(' ') ? line.substring(1) : line);
      }
    }
    
    return afterLines.join('\n');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'added':
        return <Badge variant="added">新增文件</Badge>;
      case 'removed':
        return <Badge variant="removed">删除文件</Badge>;
      case 'modified':
        return <Badge variant="modified">修改文件</Badge>;
      default:
        return <Badge variant="default">{status}</Badge>;
    }
  };

  const getLineClass = (type: string) => {
    switch (type) {
      case 'add':
        return 'bg-green-50 border-l-4 border-green-500 text-green-800 dark:bg-green-900/20 dark:text-green-200';
      case 'remove':
        return 'bg-red-50 border-l-4 border-red-500 text-red-800 dark:bg-red-900/20 dark:text-red-200';
      default:
        return 'bg-background';
    }
  };

  const getLinePrefix = (type: string) => {
    switch (type) {
      case 'add':
        return '+';
      case 'remove':
        return '-';
      default:
        return ' ';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      </Layout>
    );
  }

  if (!fileDiff) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-foreground mb-2">文件不存在</h2>
          <p className="text-muted-foreground mb-4">无法找到文件 {decodedFilename}</p>
          <Button onClick={handleGoBack}>
            返回
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleGoBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回
          </Button>
          
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-primary" />
            <div>
              <h1 className="text-xl font-bold text-foreground">
                {decodedFilename}
              </h1>
              <div className="flex items-center gap-3 mt-1">
                {getStatusBadge(fileDiff.status)}
                <span className="text-sm text-muted-foreground">
                  +{fileDiff.additions} -{fileDiff.deletions}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="diff">差异视图</TabsTrigger>
              <TabsTrigger value="before">修改前</TabsTrigger>
              <TabsTrigger value="after">修改后</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowLineNumbers(!showLineNumbers)}
          >
            {showLineNumbers ? (
              <>
                <EyeOff className="w-4 h-4 mr-2" />
                隐藏行号
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                显示行号
              </>
            )}
          </Button>
        </div>

        {/* File Content */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">文件内容</CardTitle>
            <CardDescription>
              查看 {decodedFilename} 的详细变更
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="diff" className="mt-0">
                <div className="font-mono text-sm">
                  {fileDiff.diffLines.map((line, index) => (
                    <div
                      key={index}
                      className={`flex ${getLineClass(line.type)} min-h-[1.5rem] leading-6`}
                    >
                      {showLineNumbers && (
                        <div className="flex bg-muted/50 border-r border-border">
                          <div className="w-12 px-2 text-right text-muted-foreground bg-muted/30">
                            {line.oldNumber || ''}
                          </div>
                          <div className="w-12 px-2 text-right text-muted-foreground">
                            {line.newNumber || ''}
                          </div>
                        </div>
                      )}
                      <div className="flex-1 px-4 py-0.5">
                        <span className="inline-block w-4 text-center opacity-60">
                          {getLinePrefix(line.type)}
                        </span>
                        <span className="ml-2">{line.content}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="before" className="mt-0">
                <div className="p-6">
                  {fileDiff.beforeContent ? (
                    <pre className="font-mono text-sm whitespace-pre-wrap">
                      {fileDiff.beforeContent}
                    </pre>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>此文件为新增文件，无原始内容</p>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="after" className="mt-0">
                <div className="p-6">
                  <pre className="font-mono text-sm whitespace-pre-wrap">
                    {fileDiff.afterContent}
                  </pre>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default FileDiff;
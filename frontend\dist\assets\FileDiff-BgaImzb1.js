import{j as e,L as b,B as x,C as z,d as U,e as $,f as G,a as H,h as O,t as E}from"./index-D3FXQGgv.js";import{j as Q,i as q,e as J,r as o}from"./react-vendor-DucYQ77b.js";import{B as a}from"./badge-BHFrTODj.js";import{T as P,a as K,b as D,c as j}from"./tabs-CYmfXndi.js";import{ad as M,a3 as V,Q as X,R as Y}from"./utils-CUkY6B8V.js";import"./radix-ui-DdAvwPj2.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const me=()=>{const{hash:l,filename:d}=Q(),[w]=q(),g=J(),[u,W]=o.useState(!0),[h,v]=o.useState("diff"),[m,y]=o.useState(null),[L,C]=o.useState(!0),f=decodeURIComponent(d||""),T=async()=>{if(!(!l||!d))try{C(!0);const s=(await O.getCommit(l)).files.find(i=>i.filename===f);if(!s){E({title:"文件不存在",description:"在此提交中未找到指定文件",variant:"destructive"});return}const n=k(s.patch||"");y({filename:s.filename,status:s.status,additions:s.additions,deletions:s.deletions,beforeContent:B(s.patch||""),afterContent:S(s.patch||""),diffLines:n})}catch(t){console.error("Failed to load file diff:",t),E({title:"加载失败",description:"无法加载文件差异",variant:"destructive"})}finally{C(!1)}};o.useEffect(()=>{T()},[l,d]);const F=()=>{const t=w.get("from");g(t?decodeURIComponent(t):`/commit/${l}`)},k=t=>{if(!t)return[];const s=t.split(`
`),n=[];let i=0,c=0;for(let N=0;N<s.length;N++){const r=s[N];if(r.startsWith("@@")){const p=r.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);p&&(i=parseInt(p[1])-1,c=parseInt(p[2])-1);continue}r.startsWith("+++")||r.startsWith("---")||r.startsWith("diff --git")||(r.startsWith("+")?(c++,n.push({type:"add",oldNumber:null,newNumber:c,content:r.substring(1)})):r.startsWith("-")?(i++,n.push({type:"remove",oldNumber:i,newNumber:null,content:r.substring(1)})):(r.startsWith(" ")||r==="")&&(i++,c++,n.push({type:"normal",oldNumber:i,newNumber:c,content:r.startsWith(" ")?r.substring(1):r})))}return n},B=t=>{if(!t)return"";const s=t.split(`
`),n=[];for(const i of s)i.startsWith("@@")||i.startsWith("+++")||i.startsWith("---")||i.startsWith("diff --git")||(i.startsWith("-")?n.push(i.substring(1)):(i.startsWith(" ")||i==="")&&n.push(i.startsWith(" ")?i.substring(1):i));return n.join(`
`)},S=t=>{if(!t)return"";const s=t.split(`
`),n=[];for(const i of s)i.startsWith("@@")||i.startsWith("+++")||i.startsWith("---")||i.startsWith("diff --git")||(i.startsWith("+")?n.push(i.substring(1)):(i.startsWith(" ")||i==="")&&n.push(i.startsWith(" ")?i.substring(1):i));return n.join(`
`)},A=t=>{switch(t){case"added":return e.jsxDEV(a,{variant:"added",children:"新增文件"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:205,columnNumber:16},void 0);case"removed":return e.jsxDEV(a,{variant:"removed",children:"删除文件"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:207,columnNumber:16},void 0);case"modified":return e.jsxDEV(a,{variant:"modified",children:"修改文件"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:209,columnNumber:16},void 0);default:return e.jsxDEV(a,{variant:"default",children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:211,columnNumber:16},void 0)}},I=t=>{switch(t){case"add":return"bg-green-50 border-l-4 border-green-500 text-green-800 dark:bg-green-900/20 dark:text-green-200";case"remove":return"bg-red-50 border-l-4 border-red-500 text-red-800 dark:bg-red-900/20 dark:text-red-200";default:return"bg-background"}},R=t=>{switch(t){case"add":return"+";case"remove":return"-";default:return" "}};return L?e.jsxDEV(b,{children:e.jsxDEV("div",{className:"flex items-center justify-center h-64",children:e.jsxDEV("div",{className:"w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:241,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:240,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:239,columnNumber:7},void 0):m?e.jsxDEV(b,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV(x,{variant:"outline",size:"sm",onClick:F,className:"flex items-center gap-2",children:[e.jsxDEV(M,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:272,columnNumber:13},void 0),"返回"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:266,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(V,{className:"w-6 h-6 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:277,columnNumber:13},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-xl font-bold text-foreground",children:f},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:279,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex items-center gap-3 mt-1",children:[A(m.status),e.jsxDEV("span",{className:"text-sm text-muted-foreground",children:["+",m.additions," -",m.deletions]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:284,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:282,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:278,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:276,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:265,columnNumber:9},void 0),e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV(P,{value:h,onValueChange:v,children:e.jsxDEV(K,{children:[e.jsxDEV(D,{value:"diff",children:"差异视图"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:296,columnNumber:15},void 0),e.jsxDEV(D,{value:"before",children:"修改前"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:297,columnNumber:15},void 0),e.jsxDEV(D,{value:"after",children:"修改后"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:298,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:295,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:294,columnNumber:11},void 0),e.jsxDEV(x,{variant:"outline",size:"sm",onClick:()=>W(!u),children:u?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(X,{className:"w-4 h-4 mr-2"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:309,columnNumber:17},void 0),"隐藏行号"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:308,columnNumber:15},void 0):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(Y,{className:"w-4 h-4 mr-2"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:314,columnNumber:17},void 0),"显示行号"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:313,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:302,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:293,columnNumber:9},void 0),e.jsxDEV(z,{children:[e.jsxDEV(U,{children:[e.jsxDEV($,{className:"text-lg",children:"文件内容"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:324,columnNumber:13},void 0),e.jsxDEV(G,{children:["查看 ",f," 的详细变更"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:325,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:323,columnNumber:11},void 0),e.jsxDEV(H,{className:"p-0",children:e.jsxDEV(P,{value:h,onValueChange:v,children:[e.jsxDEV(j,{value:"diff",className:"mt-0",children:e.jsxDEV("div",{className:"font-mono text-sm",children:m.diffLines.map((t,s)=>e.jsxDEV("div",{className:`flex ${I(t.type)} min-h-[1.5rem] leading-6`,children:[u&&e.jsxDEV("div",{className:"flex bg-muted/50 border-r border-border",children:[e.jsxDEV("div",{className:"w-12 px-2 text-right text-muted-foreground bg-muted/30",children:t.oldNumber||""},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:341,columnNumber:27},void 0),e.jsxDEV("div",{className:"w-12 px-2 text-right text-muted-foreground",children:t.newNumber||""},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:344,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:340,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex-1 px-4 py-0.5",children:[e.jsxDEV("span",{className:"inline-block w-4 text-center opacity-60",children:R(t.type)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:350,columnNumber:25},void 0),e.jsxDEV("span",{className:"ml-2",children:t.content},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:353,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:349,columnNumber:23},void 0)]},s,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:335,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:333,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:332,columnNumber:15},void 0),e.jsxDEV(j,{value:"before",className:"mt-0",children:e.jsxDEV("div",{className:"p-6",children:m.beforeContent?e.jsxDEV("pre",{className:"font-mono text-sm whitespace-pre-wrap",children:m.beforeContent},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:363,columnNumber:21},void 0):e.jsxDEV("div",{className:"text-center py-12 text-muted-foreground",children:[e.jsxDEV(V,{className:"w-12 h-12 mx-auto mb-4 opacity-50"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:368,columnNumber:23},void 0),e.jsxDEV("p",{children:"此文件为新增文件，无原始内容"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:369,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:367,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:361,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:360,columnNumber:15},void 0),e.jsxDEV(j,{value:"after",className:"mt-0",children:e.jsxDEV("div",{className:"p-6",children:e.jsxDEV("pre",{className:"font-mono text-sm whitespace-pre-wrap",children:m.afterContent},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:377,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:376,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:375,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:331,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:330,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:322,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:263,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:262,columnNumber:5},void 0):e.jsxDEV(b,{children:e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("h2",{className:"text-2xl font-bold text-foreground mb-2",children:"文件不存在"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:251,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-muted-foreground mb-4",children:["无法找到文件 ",f]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:252,columnNumber:11},void 0),e.jsxDEV(x,{onClick:F,children:"返回"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:253,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:250,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/FileDiff.tsx",lineNumber:249,columnNumber:7},void 0)};export{me as default};

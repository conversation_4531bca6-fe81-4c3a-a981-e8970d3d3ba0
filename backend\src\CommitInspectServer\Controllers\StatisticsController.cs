using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CommitInspectServer.EfCore;
using CommitInspectServer.Entities;

namespace CommitInspectServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class StatisticsController : ControllerBase
{
    private readonly CommitInspectDbContext _context;
    private readonly ILogger<StatisticsController> _logger;

    public StatisticsController(CommitInspectDbContext context, ILogger<StatisticsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 获取总体统计数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>总体统计信息</returns>
    [HttpGet("overview")]
    public async Task<IActionResult> GetOverviewStatistics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            // 设置默认时间范围（最近3个月）
            var defaultEndDate = endDate ?? DateTime.UtcNow;
            var defaultStartDate = startDate ?? defaultEndDate.AddMonths(-3);

            var commitsQuery = _context.Commits.AsQueryable();
            
            if (startDate.HasValue && endDate.HasValue)
            {
                commitsQuery = commitsQuery.Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate);
            }

            var totalCommits = await commitsQuery.CountAsync();
            var totalRepositories = await _context.Repositories.CountAsync();
            var totalContributors = await commitsQuery.Select(c => c.Author).Distinct().CountAsync();
            var totalAdditions = await commitsQuery.SumAsync(c => c.Additions);
            var totalDeletions = await commitsQuery.SumAsync(c => c.Deletions);
            var totalFiles = await _context.CommitFiles.Select(cf => cf.Filename).Distinct().CountAsync();

            return Ok(new
            {
                success = true,
                data = new
                {
                    totalCommits,
                    totalRepositories,
                    totalContributors,
                    totalCodeLines = totalAdditions,
                    totalFiles,
                    timeRange = new
                    {
                        startDate = defaultStartDate,
                        endDate = defaultEndDate
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取总体统计数据失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取统计数据时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取提交趋势数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>提交趋势</returns>
    [HttpGet("commit-trends")]
    public async Task<IActionResult> GetCommitTrends([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var defaultEndDate = endDate ?? DateTime.UtcNow;
            var defaultStartDate = startDate ?? defaultEndDate.AddMonths(-3);

            var commitsQuery = _context.Commits
                .Where(c => c.CreatedAt >= defaultStartDate && c.CreatedAt <= defaultEndDate);

            // 按日分组统计提交数量
            var commitTrends = await commitsQuery
                .GroupBy(c => c.CreatedAt.Date)
                .Select(g => new
                {
                    date = g.Key.ToString("yyyy-MM-dd"),
                    commits = g.Count(),
                    timestamp = g.Key
                })
                .OrderBy(x => x.timestamp)
                .ToListAsync();

            return Ok(new
            {
                success = true,
                data = commitTrends
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取提交趋势数据失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取提交趋势数据时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取贡献者统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="limit">返回数量限制</param>
    /// <returns>贡献者统计</returns>
    [HttpGet("contributors")]
    public async Task<IActionResult> GetContributorStatistics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null, [FromQuery] int limit = 10)
    {
        try
        {
            var defaultEndDate = endDate ?? DateTime.UtcNow;
            var defaultStartDate = startDate ?? defaultEndDate.AddMonths(-3);

            var contributorStats = await _context.Commits
                .Where(c => c.CreatedAt >= defaultStartDate && c.CreatedAt <= defaultEndDate)
                .GroupBy(c => c.Author)
                .Select(g => new
                {
                    name = g.Key,
                    commits = g.Count(),
                    additions = g.Sum(c => c.Additions),
                    deletions = g.Sum(c => c.Deletions),
                    timestamp = g.Max(c => c.CreatedAt)
                })
                .OrderByDescending(x => x.commits)
                .Take(limit)
                .ToListAsync();

            var totalCommits = contributorStats.Sum(c => c.commits);
            
            var contributorsWithPercentage = contributorStats.Select((c, index) => new
            {
                c.name,
                c.commits,
                c.additions,
                c.deletions,
                c.timestamp,
                percentage = totalCommits > 0 ? Math.Round((double)c.commits / totalCommits * 100, 1) : 0,
                color = GetColor(index)
            }).ToList();

            return Ok(new
            {
                success = true,
                data = contributorsWithPercentage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取贡献者统计数据失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取贡献者统计数据时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取文件类型统计
    /// </summary>
    /// <returns>文件类型统计</returns>
    [HttpGet("file-types")]
    public async Task<IActionResult> GetFileTypeStatistics()
    {
        try
        {
            var allFiles = await _context.CommitFiles.ToListAsync();
            var fileStats = allFiles
                .GroupBy(cf => GetFileExtension(cf.Filename))
                .Select(g => new
                {
                    type = g.Key,
                    count = g.Count()
                })
                .OrderByDescending(x => x.count)
                .ToList();

            var totalFiles = fileStats.Sum(f => f.count);
            
            var fileTypesWithPercentage = fileStats.Select(f => new
            {
                f.type,
                f.count,
                percentage = totalFiles > 0 ? Math.Round((double)f.count / totalFiles * 100, 1) : 0
            }).ToList();

            return Ok(new
            {
                success = true,
                data = fileTypesWithPercentage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件类型统计数据失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取文件类型统计数据时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取语言分布统计
    /// </summary>
    /// <returns>语言分布统计</returns>
    [HttpGet("languages")]
    public async Task<IActionResult> GetLanguageStatistics()
    {
        try
        {
            var allFiles = await _context.CommitFiles.ToListAsync();
            var languageStats = allFiles
                .GroupBy(cf => GetLanguageFromExtension(cf.Filename))
                .Select(g => new
                {
                    name = g.Key,
                    count = g.Count()
                })
                .OrderByDescending(x => x.count)
                .Take(10)
                .ToList();

            var totalFiles = languageStats.Sum(l => l.count);
            
            var languagesWithPercentage = languageStats.Select((l, index) => new
            {
                l.name,
                value = totalFiles > 0 ? Math.Round((double)l.count / totalFiles * 100, 1) : 0,
                color = GetLanguageColor(l.name)
            }).ToList();

            return Ok(new
            {
                success = true,
                data = languagesWithPercentage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取语言分布统计数据失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取语言分布统计数据时发生错误",
                error = ex.Message
            });
        }
    }

    private static string GetFileExtension(string filename)
    {
        if (string.IsNullOrEmpty(filename)) return "Unknown";
        
        var extension = Path.GetExtension(filename).ToLowerInvariant();
        return string.IsNullOrEmpty(extension) ? "No Extension" : extension;
    }

    private static string GetLanguageFromExtension(string filename)
    {
        var extension = GetFileExtension(filename);
        
        return extension switch
        {
            ".js" or ".jsx" => "JavaScript",
            ".ts" or ".tsx" => "TypeScript",
            ".cs" => "C#",
            ".py" => "Python",
            ".java" => "Java",
            ".cpp" or ".cc" or ".cxx" => "C++",
            ".c" or ".h" => "C",
            ".go" => "Go",
            ".rs" => "Rust",
            ".php" => "PHP",
            ".rb" => "Ruby",
            ".swift" => "Swift",
            ".kt" => "Kotlin",
            ".dart" => "Dart",
            ".html" or ".htm" => "HTML",
            ".css" or ".scss" or ".sass" or ".less" => "CSS",
            ".json" => "JSON",
            ".xml" => "XML",
            ".yaml" or ".yml" => "YAML",
            ".md" or ".markdown" => "Markdown",
            ".txt" => "Text",
            _ => "Other"
        };
    }

    private static string GetLanguageColor(string language)
    {
        return language switch
        {
            "JavaScript" => "#f7df1e",
            "TypeScript" => "#3178c6",
            "C#" => "#239120",
            "Python" => "#3776ab",
            "Java" => "#ed8b00",
            "C++" => "#00599c",
            "C" => "#a8b9cc",
            "Go" => "#00add8",
            "Rust" => "#000000",
            "PHP" => "#777bb4",
            "Ruby" => "#cc342d",
            "Swift" => "#fa7343",
            "Kotlin" => "#7f52ff",
            "Dart" => "#0175c2",
            "HTML" => "#e34f26",
            "CSS" => "#1572b6",
            "JSON" => "#2fa04e",
            "XML" => "#ff6600",
            "YAML" => "#cb171e",
            "Markdown" => "#083fa1",
            _ => "#6b7280"
        };
    }

    private static string GetColor(int index)
    {
        var colors = new[]
        {
            "#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6",
            "#06b6d4", "#84cc16", "#f97316", "#ec4899", "#6b7280"
        };
        
        return colors[index % colors.Length];
    }
}
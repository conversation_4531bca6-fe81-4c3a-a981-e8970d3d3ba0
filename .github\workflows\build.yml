name: 🚨正式环境部署
on:
  push:
    branches: [ main ]

jobs:

  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      working-directory: backend
      run: dotnet restore

    - name: Build project
      working-directory: backend
      run: dotnet build --configuration Release --no-restore

    - name: Publish project
      working-directory: backend
      run: dotnet publish src/CommitInspectServer/CommitInspectServer.csproj -c Release -o publish

    - name: Create wwwroot if not exists
      working-directory: backend
      run: mkdir -p publish/wwwroot

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'

    - name: Install dependencies
      working-directory: frontend
      run: npm install

    - name: Build
      working-directory: frontend
      run: npm run build

    - name: Copy frontend build
      working-directory: frontend
      run: cp -r dist/* ../backend/publish/wwwroot/


    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        source: "backend/publish/"
        target: "/opt/commit-inspect/upload"

    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        script: |
          cd /opt/commit-inspect
          rm -rf app
          cp -r /opt/commit-inspect/upload/backend/publish /opt/commit-inspect/app
          rm -rf /opt/commit-inspect/upload
          docker compose down && docker compose up -d


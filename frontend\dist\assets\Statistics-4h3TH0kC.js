import{j as e,c as g,b as T,L as A,C as d,B as y,a as N,d as v,e as D,f as C,s as S,t as ae}from"./index-D3FXQGgv.js";import{r as o}from"./react-vendor-DucYQ77b.js";import{B as E}from"./badge-BHFrTODj.js";import{k as H,I as me,l as oe,m as U,n as le,o as de}from"./radix-ui-DdAvwPj2.js";import{D as ue,z as u}from"./ui-extras-Cs9DDC1j.js";import{$ as q,a0 as Q,I as f,Y as fe,a1 as z,a2 as I,a3 as B,a4 as Ne,V as pe,a5 as xe}from"./utils-CUkY6B8V.js";import{S as Y,a as F,b as $,c as k,d as L}from"./select-CsIt0xHD.js";import{B as be,R as M,P as K,a as O,C as X,T as _,A as ge,b as je,X as he,Y as ve,c as De}from"./charts-CFrHoElS.js";import"./data-Csye86cT.js";const J=o.forwardRef(({className:s,value:n,...i},r)=>e.jsxDEV(H,{ref:r,className:g("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...i,children:e.jsxDEV(me,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(n||0)}%)`}},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/progress.tsx",lineNumber:18,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/progress.tsx",lineNumber:10,columnNumber:3},void 0));J.displayName=H.displayName;function W({className:s,classNames:n,showOutsideDays:i=!0,...r}){return e.jsxDEV(ue,{showOutsideDays:i,className:g("p-3",s),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:g(T({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:g(T({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},components:{IconLeft:({...l})=>e.jsxDEV(q,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/calendar.tsx",lineNumber:55,columnNumber:38},this),IconRight:({...l})=>e.jsxDEV(Q,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/calendar.tsx",lineNumber:56,columnNumber:39},this)},...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/calendar.tsx",lineNumber:17,columnNumber:5},this)}W.displayName="Calendar";const Ce=le,Se=de,Z=o.forwardRef(({className:s,align:n="center",sideOffset:i=4,...r},l)=>e.jsxDEV(oe,{children:e.jsxDEV(U,{ref:l,align:n,sideOffset:i,className:g("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/popover.tsx",lineNumber:15,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/popover.tsx",lineNumber:14,columnNumber:3},void 0));Z.displayName=U.displayName;const R=[{label:"过去7天",days:7},{label:"过去30天",days:30},{label:"过去3个月",days:90},{label:"过去6个月",days:180},{label:"过去1年",days:365},{label:"今年",value:"thisYear"},{label:"去年",value:"lastYear"},{label:"本月",value:"thisMonth"},{label:"上个月",value:"lastMonth"}],Pe=s=>{const n=new Date,i=new Date(n.getFullYear(),n.getMonth(),n.getDate());if(typeof s=="number"){const r=new Date(i);return r.setDate(r.getDate()-s),{startDate:r,endDate:i}}switch(s){case"thisYear":return{startDate:new Date(n.getFullYear(),0,1),endDate:i};case"lastYear":return{startDate:new Date(n.getFullYear()-1,0,1),endDate:new Date(n.getFullYear()-1,11,31)};case"thisMonth":return{startDate:new Date(n.getFullYear(),n.getMonth(),1),endDate:i};case"lastMonth":return{startDate:new Date(n.getFullYear(),n.getMonth()-1,1),endDate:new Date(n.getFullYear(),n.getMonth(),0)};default:return null}},G=({date:s,setDate:n,placeholder:i})=>{const[r,l]=o.useState(s||new Date);return e.jsxDEV(Ce,{children:[e.jsxDEV(Se,{asChild:!0,children:e.jsxDEV(y,{variant:"outline",className:g("w-[160px] justify-start text-left font-normal",!s&&"text-muted-foreground"),children:[e.jsxDEV(xe,{className:"mr-2 h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:156,columnNumber:11},void 0),s?f(s,"yyyy-MM-dd",{locale:u}):i]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:149,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:148,columnNumber:7},void 0),e.jsxDEV(Z,{className:"w-auto p-0",align:"start",children:[e.jsxDEV("div",{className:"p-3 border-b",children:[e.jsxDEV("div",{className:"flex items-center justify-between gap-2 mb-2",children:[e.jsxDEV(y,{variant:"outline",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{const m=new Date(r);m.setMonth(m.getMonth()-1),l(m)},children:e.jsxDEV(q,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:173,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:163,columnNumber:13},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(Y,{value:r.getFullYear().toString(),onValueChange:m=>{const c=new Date(r);c.setFullYear(parseInt(m)),l(c)},children:[e.jsxDEV(F,{className:"w-[80px] h-8",children:e.jsxDEV($,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:186,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:185,columnNumber:17},void 0),e.jsxDEV(k,{children:Array.from({length:10},(m,c)=>{const j=new Date().getFullYear()-5+c;return e.jsxDEV(L,{value:j.toString(),children:j},j,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:192,columnNumber:23},void 0)})},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:188,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:177,columnNumber:15},void 0),e.jsxDEV(Y,{value:r.getMonth().toString(),onValueChange:m=>{const c=new Date(r);c.setMonth(parseInt(m)),l(c)},children:[e.jsxDEV(F,{className:"w-[60px] h-8",children:e.jsxDEV($,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:209,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:208,columnNumber:17},void 0),e.jsxDEV(k,{children:Array.from({length:12},(m,c)=>e.jsxDEV(L,{value:c.toString(),children:[c+1,"月"]},c,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:213,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:211,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:200,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:176,columnNumber:13},void 0),e.jsxDEV(y,{variant:"outline",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{const m=new Date(r);m.setMonth(m.getMonth()+1),l(m)},children:e.jsxDEV(Q,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:231,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:221,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:162,columnNumber:11},void 0),e.jsxDEV("div",{className:"text-center text-sm text-muted-foreground",children:f(r,"yyyy年M月",{locale:u})},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:235,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:161,columnNumber:9},void 0),e.jsxDEV(W,{mode:"single",selected:s,onSelect:n,month:r,onMonthChange:l,initialFocus:!0,className:"p-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:240,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:160,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:147,columnNumber:5},void 0)},$e=()=>{const[s,n]=o.useState((()=>{const t=new Date;return t.setMonth(t.getMonth()-3),t})()),[i,r]=o.useState(new Date),[l,m]=o.useState(!0),[c,j]=o.useState(null),[ee,te]=o.useState([]),[P,se]=o.useState([]),[w,ie]=o.useState([]),[ne,ce]=o.useState([]),re=async()=>{try{m(!0);const[t,a,p,x,b]=await Promise.all([S.getOverviewStatistics(s,i),S.getCommitTrends(s,i),S.getContributorStatistics(s,i,10),S.getLanguageStatistics(),S.getFileTypeStatistics()]);t.success&&j(t.data),a.success&&te(a.data.map(h=>({...h,timestamp:new Date(h.timestamp)}))),p.success&&se(p.data.map(h=>({...h,timestamp:new Date(h.timestamp)}))),x.success&&ie(x.data),b.success&&ce(b.data)}catch(t){console.error("Failed to load statistics data:",t),ae({title:"加载失败",description:"无法加载统计数据",variant:"destructive"})}finally{m(!1)}};o.useEffect(()=>{re()},[s,i]);const V=o.useMemo(()=>P.length?P.map(t=>({name:t.name,commits:t.commits,percentage:t.percentage,color:t.color})):[],[P]);return l?e.jsxDEV(A,{children:e.jsxDEV("div",{className:"flex items-center justify-center h-64",children:e.jsxDEV("div",{className:"w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:350,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:349,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:348,columnNumber:7},void 0):e.jsxDEV(A,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(be,{className:"w-8 h-8 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:363,columnNumber:13},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-3xl font-bold text-foreground",children:"仓库统计"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:365,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-muted-foreground mt-1",children:"深入分析代码仓库的各项数据和趋势"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:368,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:364,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:362,columnNumber:11},void 0),e.jsxDEV(d,{className:"p-4",children:e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[e.jsxDEV("span",{className:"text-sm font-medium text-foreground",children:"快速选择:"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:379,columnNumber:17},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV(Y,{onValueChange:t=>{var p,x;const a=Pe(((p=R.find(b=>b.label===t))==null?void 0:p.days)||((x=R.find(b=>b.label===t))==null?void 0:x.value)||"");a&&(n(a.startDate),r(a.endDate))},children:[e.jsxDEV(F,{className:"w-[140px]",children:e.jsxDEV($,{placeholder:"选择时间范围"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:394,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:393,columnNumber:21},void 0),e.jsxDEV(k,{children:R.map(t=>e.jsxDEV(L,{value:t.label,children:t.label},t.label,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:398,columnNumber:25},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:396,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:381,columnNumber:19},void 0),e.jsxDEV(y,{variant:"outline",size:"sm",onClick:()=>{const t=new Date,a=new Date;a.setMonth(a.getMonth()-3),n(a),r(t)},children:"重置"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:405,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:380,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:378,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[e.jsxDEV("span",{className:"text-sm font-medium text-foreground",children:"自定义:"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:423,columnNumber:17},void 0),e.jsxDEV("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2",children:[e.jsxDEV(G,{date:s,setDate:n,placeholder:"开始日期"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:425,columnNumber:19},void 0),e.jsxDEV("span",{className:"text-sm text-muted-foreground hidden sm:block",children:"至"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:430,columnNumber:19},void 0),e.jsxDEV(G,{date:i,setDate:r,placeholder:"结束日期"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:431,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:424,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:422,columnNumber:15},void 0),s&&i&&e.jsxDEV("div",{className:"text-xs text-muted-foreground bg-muted/50 rounded px-2 py-1",children:["已选择: ",f(s,"yyyy年M月d日",{locale:u})," 至 ",f(i,"yyyy年M月d日",{locale:u}),"（共 ",Math.ceil((i.getTime()-s.getTime())/(1e3*60*60*24))," 天）"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:441,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:376,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:375,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:361,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsxDEV(d,{children:e.jsxDEV(N,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-primary/10 rounded-full",children:e.jsxDEV(fe,{className:"w-6 h-6 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:456,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:455,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:(c==null?void 0:c.totalCommits.toLocaleString())||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:459,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"总提交数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:460,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:458,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:454,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:453,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:452,columnNumber:11},void 0),e.jsxDEV(d,{children:e.jsxDEV(N,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-github-success/10 rounded-full",children:e.jsxDEV(z,{className:"w-6 h-6 text-github-success"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:470,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:469,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:(c==null?void 0:c.totalContributors)||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:473,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"贡献者"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:474,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:472,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:468,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:467,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:466,columnNumber:11},void 0),e.jsxDEV(d,{children:e.jsxDEV(N,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-github-warning/10 rounded-full",children:e.jsxDEV(I,{className:"w-6 h-6 text-github-warning"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:484,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:483,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:(c==null?void 0:c.totalCodeLines.toLocaleString())||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:487,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"代码行数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:488,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:486,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:482,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:481,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:480,columnNumber:11},void 0),e.jsxDEV(d,{children:e.jsxDEV(N,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-github-danger/10 rounded-full",children:e.jsxDEV(B,{className:"w-6 h-6 text-github-danger"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:498,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:497,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:(c==null?void 0:c.totalFiles.toLocaleString())||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:501,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"文件总数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:502,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:500,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:496,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:495,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:494,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:451,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxDEV(d,{children:[e.jsxDEV(v,{children:[e.jsxDEV(D,{className:"flex items-center gap-2",children:[e.jsxDEV(I,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:515,columnNumber:17},void 0),"语言分布"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:514,columnNumber:15},void 0),e.jsxDEV(C,{children:"代码库中各种编程语言的占比"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:518,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:513,columnNumber:13},void 0),e.jsxDEV(N,{children:[e.jsxDEV("div",{className:"h-64",children:e.jsxDEV(M,{width:"100%",height:"100%",children:e.jsxDEV(K,{children:[e.jsxDEV(O,{data:w,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:w.map((t,a)=>e.jsxDEV(X,{fill:t.color},`cell-${a}`,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:536,columnNumber:25},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:526,columnNumber:21},void 0),e.jsxDEV(_,{formatter:t=>[`${t}%`,"占比"]},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:539,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:525,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:524,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:523,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-wrap gap-2 mt-4",children:w.map(t=>e.jsxDEV(E,{variant:"outline",className:"flex items-center gap-2",children:[e.jsxDEV("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:t.color}},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:546,columnNumber:21},void 0),t.name," ",t.value,"%"]},t.name,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:545,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:543,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:522,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:512,columnNumber:11},void 0),e.jsxDEV(d,{children:[e.jsxDEV(v,{children:[e.jsxDEV(D,{className:"flex items-center gap-2",children:[e.jsxDEV(Ne,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:561,columnNumber:17},void 0),"提交趋势"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:560,columnNumber:15},void 0),e.jsxDEV(C,{children:s&&i?`${f(s,"yyyy年M月d日",{locale:u})} 至 ${f(i,"yyyy年M月d日",{locale:u})} 的提交活动趋势`:"过去3个月的提交活动趋势"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:564,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:559,columnNumber:13},void 0),e.jsxDEV(N,{children:e.jsxDEV("div",{className:"h-64",children:e.jsxDEV(M,{width:"100%",height:"100%",children:e.jsxDEV(ge,{data:ee,children:[e.jsxDEV(je,{strokeDasharray:"3 3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:575,columnNumber:21},void 0),e.jsxDEV(he,{dataKey:"date"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:576,columnNumber:21},void 0),e.jsxDEV(ve,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:577,columnNumber:21},void 0),e.jsxDEV(_,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:578,columnNumber:21},void 0),e.jsxDEV(De,{type:"monotone",dataKey:"commits",stroke:"hsl(var(--primary))",fill:"hsl(var(--primary))",fillOpacity:.2},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:579,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:574,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:573,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:572,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:571,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:558,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:510,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxDEV(d,{children:[e.jsxDEV(v,{children:[e.jsxDEV(D,{className:"flex items-center gap-2",children:[e.jsxDEV(z,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:599,columnNumber:17},void 0),"主要贡献者"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:598,columnNumber:15},void 0),e.jsxDEV(C,{children:s&&i?`${f(s,"yyyy年M月",{locale:u})} 至 ${f(i,"yyyy年M月",{locale:u})} 的主要贡献者`:"按提交数量排序的前5位贡献者"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:602,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:597,columnNumber:13},void 0),e.jsxDEV(N,{children:e.jsxDEV("div",{className:"space-y-4",children:P.map((t,a)=>e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm",children:a+1},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:613,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("div",{className:"flex items-center justify-between mb-1",children:[e.jsxDEV("p",{className:"font-medium text-foreground",children:t.name},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:618,columnNumber:25},void 0),e.jsxDEV(E,{variant:"secondary",children:[t.commits," commits"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:619,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:617,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxDEV("span",{className:"text-github-success",children:["+",t.additions.toLocaleString()]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:622,columnNumber:25},void 0),e.jsxDEV("span",{className:"text-github-danger",children:["-",t.deletions.toLocaleString()]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:623,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:621,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:616,columnNumber:21},void 0)]},t.name,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:612,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:610,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:609,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:596,columnNumber:11},void 0),e.jsxDEV(d,{children:[e.jsxDEV(v,{children:[e.jsxDEV(D,{className:"flex items-center gap-2",children:[e.jsxDEV(pe,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:636,columnNumber:17},void 0),"贡献者提交占比"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:635,columnNumber:15},void 0),e.jsxDEV(C,{children:s&&i?`${f(s,"yyyy年M月",{locale:u})} 至 ${f(i,"yyyy年M月",{locale:u})} 各贡献者的提交分布情况`:"过去6个月各贡献者的提交分布情况"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:639,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:634,columnNumber:13},void 0),e.jsxDEV(N,{children:[e.jsxDEV("div",{className:"h-64",children:e.jsxDEV(M,{width:"100%",height:"100%",children:e.jsxDEV(K,{children:[e.jsxDEV(O,{data:V,cx:"50%",cy:"50%",outerRadius:100,paddingAngle:2,dataKey:"commits",children:V.map((t,a)=>e.jsxDEV(X,{fill:t.color},`cell-${a}`,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:659,columnNumber:25},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:650,columnNumber:21},void 0),e.jsxDEV(_,{formatter:(t,a)=>{var p;return[`${t} commits (${(p=V.find(x=>x.commits===t))==null?void 0:p.percentage}%)`,"提交数"]}},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:662,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:649,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:648,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:647,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-wrap gap-2 mt-4",children:V.map(t=>e.jsxDEV(E,{variant:"outline",className:"flex items-center gap-2",children:[e.jsxDEV("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:t.color}},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:674,columnNumber:21},void 0),t.name," ",t.percentage,"%"]},t.name,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:673,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:671,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:646,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:633,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:594,columnNumber:9},void 0),e.jsxDEV(d,{children:[e.jsxDEV(v,{children:[e.jsxDEV(D,{className:"flex items-center gap-2",children:[e.jsxDEV(B,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:690,columnNumber:15},void 0),"文件类型分布"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:689,columnNumber:13},void 0),e.jsxDEV(C,{children:"不同文件类型在代码库中的分布情况"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:693,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:688,columnNumber:11},void 0),e.jsxDEV(N,{children:e.jsxDEV("div",{className:"space-y-4",children:ne.map(t=>e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("span",{className:"font-medium text-foreground",children:t.type},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:702,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV("span",{className:"text-sm text-muted-foreground",children:[t.count," 文件"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:704,columnNumber:23},void 0),e.jsxDEV(E,{variant:"outline",children:[t.percentage,"%"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:705,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:703,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:701,columnNumber:19},void 0),e.jsxDEV(J,{value:t.percentage,className:"h-2"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:708,columnNumber:19},void 0)]},t.type,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:700,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:698,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:697,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:687,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:359,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Statistics.tsx",lineNumber:358,columnNumber:5},void 0)};export{$e as default};

function __(c,p){for(var m=0;m<p.length;m++){const y=p[m];if(typeof y!="string"&&!Array.isArray(y)){for(const S in y)if(S!=="default"&&!(S in c)){const T=Object.getOwnPropertyDescriptor(y,S);T&&Object.defineProperty(c,S,T.get?T:{enumerable:!0,get:()=>y[S]})}}}return Object.freeze(Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}))}var tM=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function JS(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var ZS={exports:{}},of={exports:{}};of.exports;(function(c,p){/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var m="18.3.1",y=Symbol.for("react.element"),S=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),U=Symbol.for("react.provider"),w=Symbol.for("react.context"),V=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),B=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),ye=Symbol.for("react.offscreen"),ne=Symbol.iterator,we="@@iterator";function I(s){if(s===null||typeof s!="object")return null;var h=ne&&s[ne]||s[we];return typeof h=="function"?h:null}var Z={current:null},ve={transition:null},ie={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1},Be={current:null},Ce={},Ft=null;function vn(s){Ft=s}Ce.setExtraStackFrame=function(s){Ft=s},Ce.getCurrentStack=null,Ce.getStackAddendum=function(){var s="";Ft&&(s+=Ft);var h=Ce.getCurrentStack;return h&&(s+=h()||""),s};var et=!1,qe=!1,ia=!1,Fe=!1,Qe=!1,Qt={ReactCurrentDispatcher:Z,ReactCurrentBatchConfig:ve,ReactCurrentOwner:Be};Qt.ReactDebugCurrentFrame=Ce,Qt.ReactCurrentActQueue=ie;function bt(s){{for(var h=arguments.length,x=new Array(h>1?h-1:0),O=1;O<h;O++)x[O-1]=arguments[O];an("warn",s,x)}}function me(s){{for(var h=arguments.length,x=new Array(h>1?h-1:0),O=1;O<h;O++)x[O-1]=arguments[O];an("error",s,x)}}function an(s,h,x){{var O=Qt.ReactDebugCurrentFrame,j=O.getStackAddendum();j!==""&&(h+="%s",x=x.concat([j]));var ue=x.map(function(K){return String(K)});ue.unshift("Warning: "+h),Function.prototype.apply.call(console[s],console,ue)}}var $r={};function Da(s,h){{var x=s.constructor,O=x&&(x.displayName||x.name)||"ReactClass",j=O+"."+h;if($r[j])return;me("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",h,O),$r[j]=!0}}var nr={isMounted:function(s){return!1},enqueueForceUpdate:function(s,h,x){Da(s,"forceUpdate")},enqueueReplaceState:function(s,h,x,O){Da(s,"replaceState")},enqueueSetState:function(s,h,x,O){Da(s,"setState")}},Wt=Object.assign,Bn={};Object.freeze(Bn);function $n(s,h,x){this.props=s,this.context=h,this.refs=Bn,this.updater=x||nr}$n.prototype.isReactComponent={},$n.prototype.setState=function(s,h){if(typeof s!="object"&&typeof s!="function"&&s!=null)throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,s,h,"setState")},$n.prototype.forceUpdate=function(s){this.updater.enqueueForceUpdate(this,s,"forceUpdate")};{var ar={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},Yr=function(s,h){Object.defineProperty($n.prototype,s,{get:function(){bt("%s(...) is deprecated in plain JavaScript React classes. %s",h[0],h[1])}})};for(var Pr in ar)ar.hasOwnProperty(Pr)&&Yr(Pr,ar[Pr])}function qr(){}qr.prototype=$n.prototype;function pn(s,h,x){this.props=s,this.context=h,this.refs=Bn,this.updater=x||nr}var wa=pn.prototype=new qr;wa.constructor=pn,Wt(wa,$n.prototype),wa.isPureReactComponent=!0;function Yn(){var s={current:null};return Object.seal(s),s}var la=Array.isArray;function St(s){return la(s)}function rn(s){{var h=typeof Symbol=="function"&&Symbol.toStringTag,x=h&&s[Symbol.toStringTag]||s.constructor.name||"Object";return x}}function hn(s){try{return mn(s),!1}catch{return!0}}function mn(s){return""+s}function jt(s){if(hn(s))return me("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",rn(s)),mn(s)}function ua(s,h,x){var O=s.displayName;if(O)return O;var j=h.displayName||h.name||"";return j!==""?x+"("+j+")":x}function Gr(s){return s.displayName||"Context"}function _n(s){if(s==null)return null;if(typeof s.tag=="number"&&me("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof s=="function")return s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case T:return"Fragment";case S:return"Portal";case k:return"Profiler";case f:return"StrictMode";case M:return"Suspense";case F:return"SuspenseList"}if(typeof s=="object")switch(s.$$typeof){case w:var h=s;return Gr(h)+".Consumer";case U:var x=s;return Gr(x._context)+".Provider";case V:return ua(s,s.render,"ForwardRef");case B:var O=s.displayName||null;return O!==null?O:_n(s.type)||"Memo";case J:{var j=s,ue=j._payload,K=j._init;try{return _n(K(ue))}catch{return null}}}return null}var Oa=Object.prototype.hasOwnProperty,rr={key:!0,ref:!0,__self:!0,__source:!0},yn,oa,gn;gn={};function sa(s){if(Oa.call(s,"ref")){var h=Object.getOwnPropertyDescriptor(s,"ref").get;if(h&&h.isReactWarning)return!1}return s.ref!==void 0}function It(s){if(Oa.call(s,"key")){var h=Object.getOwnPropertyDescriptor(s,"key").get;if(h&&h.isReactWarning)return!1}return s.key!==void 0}function ca(s,h){var x=function(){yn||(yn=!0,me("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",h))};x.isReactWarning=!0,Object.defineProperty(s,"key",{get:x,configurable:!0})}function Oi(s,h){var x=function(){oa||(oa=!0,me("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",h))};x.isReactWarning=!0,Object.defineProperty(s,"ref",{get:x,configurable:!0})}function _i(s){if(typeof s.ref=="string"&&Be.current&&s.__self&&Be.current.stateNode!==s.__self){var h=_n(Be.current.type);gn[h]||(me('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h,s.ref),gn[h]=!0)}}var q=function(s,h,x,O,j,ue,K){var se={$$typeof:y,type:s,key:h,ref:x,props:K,_owner:ue};return se._store={},Object.defineProperty(se._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(se,"_self",{configurable:!1,enumerable:!1,writable:!1,value:O}),Object.defineProperty(se,"_source",{configurable:!1,enumerable:!1,writable:!1,value:j}),Object.freeze&&(Object.freeze(se.props),Object.freeze(se)),se};function ae(s,h,x){var O,j={},ue=null,K=null,se=null,Ee=null;if(h!=null){sa(h)&&(K=h.ref,_i(h)),It(h)&&(jt(h.key),ue=""+h.key),se=h.__self===void 0?null:h.__self,Ee=h.__source===void 0?null:h.__source;for(O in h)Oa.call(h,O)&&!rr.hasOwnProperty(O)&&(j[O]=h[O])}var Ne=arguments.length-2;if(Ne===1)j.children=x;else if(Ne>1){for(var $e=Array(Ne),Ye=0;Ye<Ne;Ye++)$e[Ye]=arguments[Ye+2];Object.freeze&&Object.freeze($e),j.children=$e}if(s&&s.defaultProps){var We=s.defaultProps;for(O in We)j[O]===void 0&&(j[O]=We[O])}if(ue||K){var nt=typeof s=="function"?s.displayName||s.name||"Unknown":s;ue&&ca(j,nt),K&&Oi(j,nt)}return q(s,ue,K,se,Ee,Be.current,j)}function be(s,h){var x=q(s.type,h,s.ref,s._self,s._source,s._owner,s.props);return x}function je(s,h,x){if(s==null)throw new Error("React.cloneElement(...): The argument must be a React element, but you passed "+s+".");var O,j=Wt({},s.props),ue=s.key,K=s.ref,se=s._self,Ee=s._source,Ne=s._owner;if(h!=null){sa(h)&&(K=h.ref,Ne=Be.current),It(h)&&(jt(h.key),ue=""+h.key);var $e;s.type&&s.type.defaultProps&&($e=s.type.defaultProps);for(O in h)Oa.call(h,O)&&!rr.hasOwnProperty(O)&&(h[O]===void 0&&$e!==void 0?j[O]=$e[O]:j[O]=h[O])}var Ye=arguments.length-2;if(Ye===1)j.children=x;else if(Ye>1){for(var We=Array(Ye),nt=0;nt<Ye;nt++)We[nt]=arguments[nt+2];j.children=We}return q(s.type,ue,K,se,Ee,Ne,j)}function Ve(s){return typeof s=="object"&&s!==null&&s.$$typeof===y}var wt=".",ct=":";function Ln(s){var h=/[=:]/g,x={"=":"=0",":":"=2"},O=s.replace(h,function(j){return x[j]});return"$"+O}var Ke=!1,fa=/\/+/g;function Ct(s){return s.replace(fa,"$&/")}function it(s,h){return typeof s=="object"&&s!==null&&s.key!=null?(jt(s.key),Ln(""+s.key)):h.toString(36)}function Li(s,h,x,O,j){var ue=typeof s;(ue==="undefined"||ue==="boolean")&&(s=null);var K=!1;if(s===null)K=!0;else switch(ue){case"string":case"number":K=!0;break;case"object":switch(s.$$typeof){case y:case S:K=!0}}if(K){var se=s,Ee=j(se),Ne=O===""?wt+it(se,0):O;if(St(Ee)){var $e="";Ne!=null&&($e=Ct(Ne)+"/"),Li(Ee,h,$e,"",function(Tf){return Tf})}else Ee!=null&&(Ve(Ee)&&(Ee.key&&(!se||se.key!==Ee.key)&&jt(Ee.key),Ee=be(Ee,x+(Ee.key&&(!se||se.key!==Ee.key)?Ct(""+Ee.key)+"/":"")+Ne)),h.push(Ee));return 1}var Ye,We,nt=0,Me=O===""?wt:O+ct;if(St(s))for(var hr=0;hr<s.length;hr++)Ye=s[hr],We=Me+it(Ye,hr),nt+=Li(Ye,h,x,We,j);else{var $i=I(s);if(typeof $i=="function"){var tu=s;$i===tu.entries&&(Ke||bt("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Ke=!0);for(var Rf=$i.call(tu),Ua,nu=0;!(Ua=Rf.next()).done;)Ye=Ua.value,We=Me+it(Ye,nu++),nt+=Li(Ye,h,x,We,j)}else if(ue==="object"){var au=String(s);throw new Error("Objects are not valid as a React child (found: "+(au==="[object Object]"?"object with keys {"+Object.keys(s).join(", ")+"}":au)+"). If you meant to render a collection of children, use an array instead.")}}return nt}function ir(s,h,x){if(s==null)return s;var O=[],j=0;return Li(s,O,"","",function(ue){return h.call(x,ue,j++)}),O}function jl(s){var h=0;return ir(s,function(){h++}),h}function df(s,h,x){ir(s,function(){h.apply(this,arguments)},x)}function vf(s){return ir(s,function(h){return h})||[]}function Bo(s){if(!Ve(s))throw new Error("React.Children.only expected to receive a single React element child.");return s}function $o(s){var h={$$typeof:w,_currentValue:s,_currentValue2:s,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};h.Provider={$$typeof:U,_context:h};var x=!1,O=!1,j=!1;{var ue={$$typeof:w,_context:h};Object.defineProperties(ue,{Provider:{get:function(){return O||(O=!0,me("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),h.Provider},set:function(K){h.Provider=K}},_currentValue:{get:function(){return h._currentValue},set:function(K){h._currentValue=K}},_currentValue2:{get:function(){return h._currentValue2},set:function(K){h._currentValue2=K}},_threadCount:{get:function(){return h._threadCount},set:function(K){h._threadCount=K}},Consumer:{get:function(){return x||(x=!0,me("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),h.Consumer}},displayName:{get:function(){return h.displayName},set:function(K){j||(bt("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.",K),j=!0)}}}),h.Consumer=ue}return h._currentRenderer=null,h._currentRenderer2=null,h}var Qr=-1,Mi=0,Ui=1,lr=2;function _a(s){if(s._status===Qr){var h=s._result,x=h();if(x.then(function(ue){if(s._status===Mi||s._status===Qr){var K=s;K._status=Ui,K._result=ue}},function(ue){if(s._status===Mi||s._status===Qr){var K=s;K._status=lr,K._result=ue}}),s._status===Qr){var O=s;O._status=Mi,O._result=x}}if(s._status===Ui){var j=s._result;return j===void 0&&me(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,j),"default"in j||me(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,j),j.default}else throw s._result}function ur(s){var h={_status:Qr,_result:s},x={$$typeof:J,_payload:h,_init:_a};{var O,j;Object.defineProperties(x,{defaultProps:{configurable:!0,get:function(){return O},set:function(ue){me("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),O=ue,Object.defineProperty(x,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return j},set:function(ue){me("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),j=ue,Object.defineProperty(x,"propTypes",{enumerable:!0})}}})}return x}function Vl(s){s!=null&&s.$$typeof===B?me("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof s!="function"?me("forwardRef requires a render function but was given %s.",s===null?"null":typeof s):s.length!==0&&s.length!==2&&me("forwardRef render functions accept exactly two parameters: props and ref. %s",s.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),s!=null&&(s.defaultProps!=null||s.propTypes!=null)&&me("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");var h={$$typeof:V,render:s};{var x;Object.defineProperty(h,"displayName",{enumerable:!1,configurable:!0,get:function(){return x},set:function(O){x=O,!s.name&&!s.displayName&&(s.displayName=O)}})}return h}var Ai;Ai=Symbol.for("react.module.reference");function Ni(s){return!!(typeof s=="string"||typeof s=="function"||s===T||s===k||Qe||s===f||s===M||s===F||Fe||s===ye||et||qe||ia||typeof s=="object"&&s!==null&&(s.$$typeof===J||s.$$typeof===B||s.$$typeof===U||s.$$typeof===w||s.$$typeof===V||s.$$typeof===Ai||s.getModuleId!==void 0))}function Bl(s,h){Ni(s)||me("memo: The first argument must be a component. Instead received: %s",s===null?"null":typeof s);var x={$$typeof:B,type:s,compare:h===void 0?null:h};{var O;Object.defineProperty(x,"displayName",{enumerable:!1,configurable:!0,get:function(){return O},set:function(j){O=j,!s.name&&!s.displayName&&(s.displayName=j)}})}return x}function tt(){var s=Z.current;return s===null&&me(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`),s}function ki(s){var h=tt();if(s._context!==void 0){var x=s._context;x.Consumer===s?me("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):x.Provider===s&&me("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return h.useContext(s)}function zi(s){var h=tt();return h.useState(s)}function Wr(s,h,x){var O=tt();return O.useReducer(s,h,x)}function Ot(s){var h=tt();return h.useRef(s)}function pf(s,h){var x=tt();return x.useEffect(s,h)}function hf(s,h){var x=tt();return x.useInsertionEffect(s,h)}function Yo(s,h){var x=tt();return x.useLayoutEffect(s,h)}function mf(s,h){var x=tt();return x.useCallback(s,h)}function yf(s,h){var x=tt();return x.useMemo(s,h)}function gf(s,h,x){var O=tt();return O.useImperativeHandle(s,h,x)}function Po(s,h){{var x=tt();return x.useDebugValue(s,h)}}function bf(){var s=tt();return s.useTransition()}function La(s){var h=tt();return h.useDeferredValue(s)}function Se(){var s=tt();return s.useId()}function Ir(s,h,x){var O=tt();return O.useSyncExternalStore(s,h,x)}var or=0,$l,Yl,Pl,ql,Gl,Ql,Wl;function qo(){}qo.__reactDisabledLog=!0;function Sf(){{if(or===0){$l=console.log,Yl=console.info,Pl=console.warn,ql=console.error,Gl=console.group,Ql=console.groupCollapsed,Wl=console.groupEnd;var s={configurable:!0,enumerable:!0,value:qo,writable:!0};Object.defineProperties(console,{info:s,log:s,warn:s,error:s,group:s,groupCollapsed:s,groupEnd:s})}or++}}function Il(){{if(or--,or===0){var s={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Wt({},s,{value:$l}),info:Wt({},s,{value:Yl}),warn:Wt({},s,{value:Pl}),error:Wt({},s,{value:ql}),group:Wt({},s,{value:Gl}),groupCollapsed:Wt({},s,{value:Ql}),groupEnd:Wt({},s,{value:Wl})})}or<0&&me("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Xr=Qt.ReactCurrentDispatcher,Mn;function sr(s,h,x){{if(Mn===void 0)try{throw Error()}catch(j){var O=j.stack.trim().match(/\n( *(at )?)/);Mn=O&&O[1]||""}return`
`+Mn+s}}var cr=!1,Hi;{var Xl=typeof WeakMap=="function"?WeakMap:Map;Hi=new Xl}function Go(s,h){if(!s||cr)return"";{var x=Hi.get(s);if(x!==void 0)return x}var O;cr=!0;var j=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var ue;ue=Xr.current,Xr.current=null,Sf();try{if(h){var K=function(){throw Error()};if(Object.defineProperty(K.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(K,[])}catch(Me){O=Me}Reflect.construct(s,[],K)}else{try{K.call()}catch(Me){O=Me}s.call(K.prototype)}}else{try{throw Error()}catch(Me){O=Me}s()}}catch(Me){if(Me&&O&&typeof Me.stack=="string"){for(var se=Me.stack.split(`
`),Ee=O.stack.split(`
`),Ne=se.length-1,$e=Ee.length-1;Ne>=1&&$e>=0&&se[Ne]!==Ee[$e];)$e--;for(;Ne>=1&&$e>=0;Ne--,$e--)if(se[Ne]!==Ee[$e]){if(Ne!==1||$e!==1)do if(Ne--,$e--,$e<0||se[Ne]!==Ee[$e]){var Ye=`
`+se[Ne].replace(" at new "," at ");return s.displayName&&Ye.includes("<anonymous>")&&(Ye=Ye.replace("<anonymous>",s.displayName)),typeof s=="function"&&Hi.set(s,Ye),Ye}while(Ne>=1&&$e>=0);break}}}finally{cr=!1,Xr.current=ue,Il(),Error.prepareStackTrace=j}var We=s?s.displayName||s.name:"",nt=We?sr(We):"";return typeof s=="function"&&Hi.set(s,nt),nt}function Kl(s,h,x){return Go(s,!1)}function Cf(s){var h=s.prototype;return!!(h&&h.isReactComponent)}function fr(s,h,x){if(s==null)return"";if(typeof s=="function")return Go(s,Cf(s));if(typeof s=="string")return sr(s);switch(s){case M:return sr("Suspense");case F:return sr("SuspenseList")}if(typeof s=="object")switch(s.$$typeof){case V:return Kl(s.render);case B:return fr(s.type,h,x);case J:{var O=s,j=O._payload,ue=O._init;try{return fr(ue(j),h,x)}catch{}}}return""}var Qo={},Jl=Qt.ReactDebugCurrentFrame;function Fi(s){if(s){var h=s._owner,x=fr(s.type,s._source,h?h.type:null);Jl.setExtraStackFrame(x)}else Jl.setExtraStackFrame(null)}function Wo(s,h,x,O,j){{var ue=Function.call.bind(Oa);for(var K in s)if(ue(s,K)){var se=void 0;try{if(typeof s[K]!="function"){var Ee=Error((O||"React class")+": "+x+" type `"+K+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof s[K]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Ee.name="Invariant Violation",Ee}se=s[K](h,K,O,x,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Ne){se=Ne}se&&!(se instanceof Error)&&(Fi(j),me("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",O||"React class",x,K,typeof se),Fi(null)),se instanceof Error&&!(se.message in Qo)&&(Qo[se.message]=!0,Fi(j),me("Failed %s type: %s",x,se.message),Fi(null))}}}function Oe(s){if(s){var h=s._owner,x=fr(s.type,s._source,h?h.type:null);vn(x)}else vn(null)}var Zl;Zl=!1;function eu(){if(Be.current){var s=_n(Be.current.type);if(s)return`

Check the render method of \``+s+"`."}return""}function pe(s){if(s!==void 0){var h=s.fileName.replace(/^.*[\\\/]/,""),x=s.lineNumber;return`

Check your code at `+h+":"+x+"."}return""}function Io(s){return s!=null?pe(s.__source):""}var Vt={};function Kr(s){var h=eu();if(!h){var x=typeof s=="string"?s:s.displayName||s.name;x&&(h=`

Check the top-level render call using <`+x+">.")}return h}function dr(s,h){if(!(!s._store||s._store.validated||s.key!=null)){s._store.validated=!0;var x=Kr(h);if(!Vt[x]){Vt[x]=!0;var O="";s&&s._owner&&s._owner!==Be.current&&(O=" It was passed a child from "+_n(s._owner.type)+"."),Oe(s),me('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',x,O),Oe(null)}}}function Xo(s,h){if(typeof s=="object"){if(St(s))for(var x=0;x<s.length;x++){var O=s[x];Ve(O)&&dr(O,h)}else if(Ve(s))s._store&&(s._store.validated=!0);else if(s){var j=I(s);if(typeof j=="function"&&j!==s.entries)for(var ue=j.call(s),K;!(K=ue.next()).done;)Ve(K.value)&&dr(K.value,h)}}}function Et(s){{var h=s.type;if(h==null||typeof h=="string")return;var x;if(typeof h=="function")x=h.propTypes;else if(typeof h=="object"&&(h.$$typeof===V||h.$$typeof===B))x=h.propTypes;else return;if(x){var O=_n(h);Wo(x,s.props,"prop",O,s)}else if(h.PropTypes!==void 0&&!Zl){Zl=!0;var j=_n(h);me("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",j||"Unknown")}typeof h.getDefaultProps=="function"&&!h.getDefaultProps.isReactClassApproved&&me("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function Je(s){{for(var h=Object.keys(s.props),x=0;x<h.length;x++){var O=h[x];if(O!=="children"&&O!=="key"){Oe(s),me("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",O),Oe(null);break}}s.ref!==null&&(Oe(s),me("Invalid attribute `ref` supplied to `React.Fragment`."),Oe(null))}}function Ko(s,h,x){var O=Ni(s);if(!O){var j="";(s===void 0||typeof s=="object"&&s!==null&&Object.keys(s).length===0)&&(j+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var ue=Io(h);ue?j+=ue:j+=eu();var K;s===null?K="null":St(s)?K="array":s!==void 0&&s.$$typeof===y?(K="<"+(_n(s.type)||"Unknown")+" />",j=" Did you accidentally export a JSX literal instead of a component?"):K=typeof s,me("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",K,j)}var se=ae.apply(this,arguments);if(se==null)return se;if(O)for(var Ee=2;Ee<arguments.length;Ee++)Xo(arguments[Ee],s);return s===T?Je(se):Et(se),se}var bn=!1;function ln(s){var h=Ko.bind(null,s);return h.type=s,bn||(bn=!0,bt("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(h,"type",{enumerable:!1,get:function(){return bt("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:s}),s}}),h}function da(s,h,x){for(var O=je.apply(this,arguments),j=2;j<arguments.length;j++)Xo(arguments[j],O.type);return Et(O),O}function Ef(s,h){var x=ve.transition;ve.transition={};var O=ve.transition;ve.transition._updatedFibers=new Set;try{s()}finally{if(ve.transition=x,x===null&&O._updatedFibers){var j=O._updatedFibers.size;j>10&&bt("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),O._updatedFibers.clear()}}}var ji=!1,Jr=null;function Jo(s){if(Jr===null)try{var h=("require"+Math.random()).slice(0,7),x=c&&c[h];Jr=x.call(c,"timers").setImmediate}catch{Jr=function(j){ji===!1&&(ji=!0,typeof MessageChannel>"u"&&me("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var ue=new MessageChannel;ue.port1.onmessage=j,ue.port2.postMessage(void 0)}}return Jr(s)}var vr=0,Zo=!1;function es(s){{var h=vr;vr++,ie.current===null&&(ie.current=[]);var x=ie.isBatchingLegacy,O;try{if(ie.isBatchingLegacy=!0,O=s(),!x&&ie.didScheduleLegacyUpdate){var j=ie.current;j!==null&&(ie.didScheduleLegacyUpdate=!1,Bi(j))}}catch(We){throw Ma(h),We}finally{ie.isBatchingLegacy=x}if(O!==null&&typeof O=="object"&&typeof O.then=="function"){var ue=O,K=!1,se={then:function(We,nt){K=!0,ue.then(function(Me){Ma(h),vr===0?Vi(Me,We,nt):We(Me)},function(Me){Ma(h),nt(Me)})}};return!Zo&&typeof Promise<"u"&&Promise.resolve().then(function(){}).then(function(){K||(Zo=!0,me("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),se}else{var Ee=O;if(Ma(h),vr===0){var Ne=ie.current;Ne!==null&&(Bi(Ne),ie.current=null);var $e={then:function(We,nt){ie.current===null?(ie.current=[],Vi(Ee,We,nt)):We(Ee)}};return $e}else{var Ye={then:function(We,nt){We(Ee)}};return Ye}}}}function Ma(s){s!==vr-1&&me("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),vr=s}function Vi(s,h,x){{var O=ie.current;if(O!==null)try{Bi(O),Jo(function(){O.length===0?(ie.current=null,h(s)):Vi(s,h,x)})}catch(j){x(j)}else h(s)}}var pr=!1;function Bi(s){if(!pr){pr=!0;var h=0;try{for(;h<s.length;h++){var x=s[h];do x=x(!0);while(x!==null)}s.length=0}catch(O){throw s=s.slice(h+1),O}finally{pr=!1}}}var ts=Ko,ns=da,as=ln,rs={map:ir,forEach:df,count:jl,toArray:vf,only:Bo};p.Children=rs,p.Component=$n,p.Fragment=T,p.Profiler=k,p.PureComponent=pn,p.StrictMode=f,p.Suspense=M,p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qt,p.act=es,p.cloneElement=ns,p.createContext=$o,p.createElement=ts,p.createFactory=as,p.createRef=Yn,p.forwardRef=Vl,p.isValidElement=Ve,p.lazy=ur,p.memo=Bl,p.startTransition=Ef,p.unstable_act=es,p.useCallback=mf,p.useContext=ki,p.useDebugValue=Po,p.useDeferredValue=La,p.useEffect=pf,p.useId=Se,p.useImperativeHandle=gf,p.useInsertionEffect=hf,p.useLayoutEffect=Yo,p.useMemo=yf,p.useReducer=Wr,p.useRef=Ot,p.useState=zi,p.useSyncExternalStore=Ir,p.useTransition=bf,p.version=m,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()})(of,of.exports);var L_=of.exports;ZS.exports=L_;var H=ZS.exports;const M_=JS(H),U_=__({__proto__:null,default:M_},[H]);var eC={exports:{}},On={},tC={exports:{}},nC={};(function(c){/**
 * @license React
 * scheduler.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var p=!1,m=!1,y=5;function S(q,ae){var be=q.length;q.push(ae),k(q,ae,be)}function T(q){return q.length===0?null:q[0]}function f(q){if(q.length===0)return null;var ae=q[0],be=q.pop();return be!==ae&&(q[0]=be,U(q,be,0)),ae}function k(q,ae,be){for(var je=be;je>0;){var Ve=je-1>>>1,wt=q[Ve];if(w(wt,ae)>0)q[Ve]=ae,q[je]=wt,je=Ve;else return}}function U(q,ae,be){for(var je=be,Ve=q.length,wt=Ve>>>1;je<wt;){var ct=(je+1)*2-1,Ln=q[ct],Ke=ct+1,fa=q[Ke];if(w(Ln,ae)<0)Ke<Ve&&w(fa,Ln)<0?(q[je]=fa,q[Ke]=ae,je=Ke):(q[je]=Ln,q[ct]=ae,je=ct);else if(Ke<Ve&&w(fa,ae)<0)q[je]=fa,q[Ke]=ae,je=Ke;else return}}function w(q,ae){var be=q.sortIndex-ae.sortIndex;return be!==0?be:q.id-ae.id}var V=1,M=2,F=3,B=4,J=5;function ye(q,ae){}var ne=typeof performance=="object"&&typeof performance.now=="function";if(ne){var we=performance;c.unstable_now=function(){return we.now()}}else{var I=Date,Z=I.now();c.unstable_now=function(){return I.now()-Z}}var ve=1073741823,ie=-1,Be=250,Ce=5e3,Ft=1e4,vn=ve,et=[],qe=[],ia=1,Fe=null,Qe=F,Qt=!1,bt=!1,me=!1,an=typeof setTimeout=="function"?setTimeout:null,$r=typeof clearTimeout=="function"?clearTimeout:null,Da=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function nr(q){for(var ae=T(qe);ae!==null;){if(ae.callback===null)f(qe);else if(ae.startTime<=q)f(qe),ae.sortIndex=ae.expirationTime,S(et,ae);else return;ae=T(qe)}}function Wt(q){if(me=!1,nr(q),!bt)if(T(et)!==null)bt=!0,sa(Bn);else{var ae=T(qe);ae!==null&&It(Wt,ae.startTime-q)}}function Bn(q,ae){bt=!1,me&&(me=!1,ca()),Qt=!0;var be=Qe;try{var je;if(!m)return $n(q,ae)}finally{Fe=null,Qe=be,Qt=!1}}function $n(q,ae){var be=ae;for(nr(be),Fe=T(et);Fe!==null&&!p&&!(Fe.expirationTime>be&&(!q||Gr()));){var je=Fe.callback;if(typeof je=="function"){Fe.callback=null,Qe=Fe.priorityLevel;var Ve=Fe.expirationTime<=be,wt=je(Ve);be=c.unstable_now(),typeof wt=="function"?Fe.callback=wt:Fe===T(et)&&f(et),nr(be)}else f(et);Fe=T(et)}if(Fe!==null)return!0;var ct=T(qe);return ct!==null&&It(Wt,ct.startTime-be),!1}function ar(q,ae){switch(q){case V:case M:case F:case B:case J:break;default:q=F}var be=Qe;Qe=q;try{return ae()}finally{Qe=be}}function Yr(q){var ae;switch(Qe){case V:case M:case F:ae=F;break;default:ae=Qe;break}var be=Qe;Qe=ae;try{return q()}finally{Qe=be}}function Pr(q){var ae=Qe;return function(){var be=Qe;Qe=ae;try{return q.apply(this,arguments)}finally{Qe=be}}}function qr(q,ae,be){var je=c.unstable_now(),Ve;if(typeof be=="object"&&be!==null){var wt=be.delay;typeof wt=="number"&&wt>0?Ve=je+wt:Ve=je}else Ve=je;var ct;switch(q){case V:ct=ie;break;case M:ct=Be;break;case J:ct=vn;break;case B:ct=Ft;break;case F:default:ct=Ce;break}var Ln=Ve+ct,Ke={id:ia++,callback:ae,priorityLevel:q,startTime:Ve,expirationTime:Ln,sortIndex:-1};return Ve>je?(Ke.sortIndex=Ve,S(qe,Ke),T(et)===null&&Ke===T(qe)&&(me?ca():me=!0,It(Wt,Ve-je))):(Ke.sortIndex=Ln,S(et,Ke),!bt&&!Qt&&(bt=!0,sa(Bn))),Ke}function pn(){}function wa(){!bt&&!Qt&&(bt=!0,sa(Bn))}function Yn(){return T(et)}function la(q){q.callback=null}function St(){return Qe}var rn=!1,hn=null,mn=-1,jt=y,ua=-1;function Gr(){var q=c.unstable_now()-ua;return!(q<jt)}function _n(){}function Oa(q){if(q<0||q>125){console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported");return}q>0?jt=Math.floor(1e3/q):jt=y}var rr=function(){if(hn!==null){var q=c.unstable_now();ua=q;var ae=!0,be=!0;try{be=hn(ae,q)}finally{be?yn():(rn=!1,hn=null)}}else rn=!1},yn;if(typeof Da=="function")yn=function(){Da(rr)};else if(typeof MessageChannel<"u"){var oa=new MessageChannel,gn=oa.port2;oa.port1.onmessage=rr,yn=function(){gn.postMessage(null)}}else yn=function(){an(rr,0)};function sa(q){hn=q,rn||(rn=!0,yn())}function It(q,ae){mn=an(function(){q(c.unstable_now())},ae)}function ca(){$r(mn),mn=-1}var Oi=_n,_i=null;c.unstable_IdlePriority=J,c.unstable_ImmediatePriority=V,c.unstable_LowPriority=B,c.unstable_NormalPriority=F,c.unstable_Profiling=_i,c.unstable_UserBlockingPriority=M,c.unstable_cancelCallback=la,c.unstable_continueExecution=wa,c.unstable_forceFrameRate=Oa,c.unstable_getCurrentPriorityLevel=St,c.unstable_getFirstCallbackNode=Yn,c.unstable_next=Yr,c.unstable_pauseExecution=pn,c.unstable_requestPaint=Oi,c.unstable_runWithPriority=ar,c.unstable_scheduleCallback=qr,c.unstable_shouldYield=Gr,c.unstable_wrapCallback=Pr,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()})(nC);tC.exports=nC;var A_=tC.exports;/**
 * @license React
 * react-dom.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var c=H,p=A_,m=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,y=!1;function S(e){y=e}function T(e){if(!y){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];k("warn",e,n)}}function f(e){if(!y){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];k("error",e,n)}}function k(e,t,n){{var a=m.ReactDebugCurrentFrame,r=a.getStackAddendum();r!==""&&(t+="%s",n=n.concat([r]));var i=n.map(function(l){return String(l)});i.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,i)}}var U=0,w=1,V=2,M=3,F=4,B=5,J=6,ye=7,ne=8,we=9,I=10,Z=11,ve=12,ie=13,Be=14,Ce=15,Ft=16,vn=17,et=18,qe=19,ia=21,Fe=22,Qe=23,Qt=24,bt=25,me=!0,an=!1,$r=!1,Da=!1,nr=!1,Wt=!0,Bn=!1,$n=!0,ar=!0,Yr=!0,Pr=!0,qr=new Set,pn={},wa={};function Yn(e,t){la(e,t),la(e+"Capture",t)}function la(e,t){pn[e]&&f("EventRegistry: More than one plugin attempted to publish the same registration name, `%s`.",e),pn[e]=t;{var n=e.toLowerCase();wa[n]=e,e==="onDoubleClick"&&(wa.ondblclick=e)}for(var a=0;a<t.length;a++)qr.add(t[a])}var St=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",rn=Object.prototype.hasOwnProperty;function hn(e){{var t=typeof Symbol=="function"&&Symbol.toStringTag,n=t&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n}}function mn(e){try{return jt(e),!1}catch{return!0}}function jt(e){return""+e}function ua(e,t){if(mn(e))return f("The provided `%s` attribute is an unsupported type %s. This value must be coerced to a string before before using it here.",t,hn(e)),jt(e)}function Gr(e){if(mn(e))return f("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",hn(e)),jt(e)}function _n(e,t){if(mn(e))return f("The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.",t,hn(e)),jt(e)}function Oa(e,t){if(mn(e))return f("The provided `%s` CSS property is an unsupported type %s. This value must be coerced to a string before before using it here.",t,hn(e)),jt(e)}function rr(e){if(mn(e))return f("The provided HTML markup uses a value of unsupported type %s. This value must be coerced to a string before before using it here.",hn(e)),jt(e)}function yn(e){if(mn(e))return f("Form field values (value, checked, defaultValue, or defaultChecked props) must be strings, not %s. This value must be coerced to a string before before using it here.",hn(e)),jt(e)}var oa=0,gn=1,sa=2,It=3,ca=4,Oi=5,_i=6,q=":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",ae=q+"\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040",be=new RegExp("^["+q+"]["+ae+"]*$"),je={},Ve={};function wt(e){return rn.call(Ve,e)?!0:rn.call(je,e)?!1:be.test(e)?(Ve[e]=!0,!0):(je[e]=!0,f("Invalid attribute name: `%s`",e),!1)}function ct(e,t,n){return t!==null?t.type===oa:n?!1:e.length>2&&(e[0]==="o"||e[0]==="O")&&(e[1]==="n"||e[1]==="N")}function Ln(e,t,n,a){if(n!==null&&n.type===oa)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":{if(a)return!1;if(n!==null)return!n.acceptsBooleans;var r=e.toLowerCase().slice(0,5);return r!=="data-"&&r!=="aria-"}default:return!1}}function Ke(e,t,n,a){if(t===null||typeof t>"u"||Ln(e,t,n,a))return!0;if(a)return!1;if(n!==null)switch(n.type){case It:return!t;case ca:return t===!1;case Oi:return isNaN(t);case _i:return isNaN(t)||t<1}return!1}function fa(e){return it.hasOwnProperty(e)?it[e]:null}function Ct(e,t,n,a,r,i,l){this.acceptsBooleans=t===sa||t===It||t===ca,this.attributeName=a,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var it={},Li=["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"];Li.forEach(function(e){it[e]=new Ct(e,oa,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0],n=e[1];it[t]=new Ct(t,gn,!1,n,null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){it[e]=new Ct(e,sa,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){it[e]=new Ct(e,sa,!1,e,null,!1,!1)}),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach(function(e){it[e]=new Ct(e,It,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){it[e]=new Ct(e,It,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){it[e]=new Ct(e,ca,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){it[e]=new Ct(e,_i,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){it[e]=new Ct(e,Oi,!1,e.toLowerCase(),null,!1,!1)});var ir=/[\-\:]([a-z])/g,jl=function(e){return e[1].toUpperCase()};["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach(function(e){var t=e.replace(ir,jl);it[t]=new Ct(t,gn,!1,e,null,!1,!1)}),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach(function(e){var t=e.replace(ir,jl);it[t]=new Ct(t,gn,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ir,jl);it[t]=new Ct(t,gn,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){it[e]=new Ct(e,gn,!1,e.toLowerCase(),null,!1,!1)});var df="xlinkHref";it[df]=new Ct("xlinkHref",gn,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){it[e]=new Ct(e,gn,!1,e.toLowerCase(),null,!0,!0)});var vf=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*\:/i,Bo=!1;function $o(e){!Bo&&vf.test(e)&&(Bo=!0,f("A future version of React will block javascript: URLs as a security precaution. Use event handlers instead if you can. If you need to generate unsafe HTML try using dangerouslySetInnerHTML instead. React was passed %s.",JSON.stringify(e)))}function Qr(e,t,n,a){if(a.mustUseProperty){var r=a.propertyName;return e[r]}else{ua(n,t),a.sanitizeURL&&$o(""+n);var i=a.attributeName,l=null;if(a.type===ca){if(e.hasAttribute(i)){var u=e.getAttribute(i);return u===""?!0:Ke(t,n,a,!1)?u:u===""+n?n:u}}else if(e.hasAttribute(i)){if(Ke(t,n,a,!1))return e.getAttribute(i);if(a.type===It)return n;l=e.getAttribute(i)}return Ke(t,n,a,!1)?l===null?n:l:l===""+n?n:l}}function Mi(e,t,n,a){{if(!wt(t))return;if(!e.hasAttribute(t))return n===void 0?void 0:null;var r=e.getAttribute(t);return ua(n,t),r===""+n?n:r}}function Ui(e,t,n,a){var r=fa(t);if(!ct(t,r,a)){if(Ke(t,n,r,a)&&(n=null),a||r===null){if(wt(t)){var i=t;n===null?e.removeAttribute(i):(ua(n,t),e.setAttribute(i,""+n))}return}var l=r.mustUseProperty;if(l){var u=r.propertyName;if(n===null){var o=r.type;e[u]=o===It?!1:""}else e[u]=n;return}var d=r.attributeName,v=r.attributeNamespace;if(n===null)e.removeAttribute(d);else{var b=r.type,g;b===It||b===ca&&n===!0?g="":(ua(n,d),g=""+n,r.sanitizeURL&&$o(g.toString())),v?e.setAttributeNS(v,d,g):e.setAttribute(d,g)}}}var lr=Symbol.for("react.element"),_a=Symbol.for("react.portal"),ur=Symbol.for("react.fragment"),Vl=Symbol.for("react.strict_mode"),Ai=Symbol.for("react.profiler"),Ni=Symbol.for("react.provider"),Bl=Symbol.for("react.context"),tt=Symbol.for("react.forward_ref"),ki=Symbol.for("react.suspense"),zi=Symbol.for("react.suspense_list"),Wr=Symbol.for("react.memo"),Ot=Symbol.for("react.lazy"),pf=Symbol.for("react.scope"),hf=Symbol.for("react.debug_trace_mode"),Yo=Symbol.for("react.offscreen"),mf=Symbol.for("react.legacy_hidden"),yf=Symbol.for("react.cache"),gf=Symbol.for("react.tracing_marker"),Po=Symbol.iterator,bf="@@iterator";function La(e){if(e===null||typeof e!="object")return null;var t=Po&&e[Po]||e[bf];return typeof t=="function"?t:null}var Se=Object.assign,Ir=0,or,$l,Yl,Pl,ql,Gl,Ql;function Wl(){}Wl.__reactDisabledLog=!0;function qo(){{if(Ir===0){or=console.log,$l=console.info,Yl=console.warn,Pl=console.error,ql=console.group,Gl=console.groupCollapsed,Ql=console.groupEnd;var e={configurable:!0,enumerable:!0,value:Wl,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}Ir++}}function Sf(){{if(Ir--,Ir===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Se({},e,{value:or}),info:Se({},e,{value:$l}),warn:Se({},e,{value:Yl}),error:Se({},e,{value:Pl}),group:Se({},e,{value:ql}),groupCollapsed:Se({},e,{value:Gl}),groupEnd:Se({},e,{value:Ql})})}Ir<0&&f("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Il=m.ReactCurrentDispatcher,Xr;function Mn(e,t,n){{if(Xr===void 0)try{throw Error()}catch(r){var a=r.stack.trim().match(/\n( *(at )?)/);Xr=a&&a[1]||""}return`
`+Xr+e}}var sr=!1,cr;{var Hi=typeof WeakMap=="function"?WeakMap:Map;cr=new Hi}function Xl(e,t){if(!e||sr)return"";{var n=cr.get(e);if(n!==void 0)return n}var a;sr=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var i;i=Il.current,Il.current=null,qo();try{if(t){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(l,[])}catch(D){a=D}Reflect.construct(e,[],l)}else{try{l.call()}catch(D){a=D}e.call(l.prototype)}}else{try{throw Error()}catch(D){a=D}e()}}catch(D){if(D&&a&&typeof D.stack=="string"){for(var u=D.stack.split(`
`),o=a.stack.split(`
`),d=u.length-1,v=o.length-1;d>=1&&v>=0&&u[d]!==o[v];)v--;for(;d>=1&&v>=0;d--,v--)if(u[d]!==o[v]){if(d!==1||v!==1)do if(d--,v--,v<0||u[d]!==o[v]){var b=`
`+u[d].replace(" at new "," at ");return e.displayName&&b.includes("<anonymous>")&&(b=b.replace("<anonymous>",e.displayName)),typeof e=="function"&&cr.set(e,b),b}while(d>=1&&v>=0);break}}}finally{sr=!1,Il.current=i,Sf(),Error.prepareStackTrace=r}var g=e?e.displayName||e.name:"",R=g?Mn(g):"";return typeof e=="function"&&cr.set(e,R),R}function Go(e,t,n){return Xl(e,!0)}function Kl(e,t,n){return Xl(e,!1)}function Cf(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function fr(e,t,n){if(e==null)return"";if(typeof e=="function")return Xl(e,Cf(e));if(typeof e=="string")return Mn(e);switch(e){case ki:return Mn("Suspense");case zi:return Mn("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case tt:return Kl(e.render);case Wr:return fr(e.type,t,n);case Ot:{var a=e,r=a._payload,i=a._init;try{return fr(i(r),t,n)}catch{}}}return""}function Qo(e){switch(e._debugOwner&&e._debugOwner.type,e._debugSource,e.tag){case B:return Mn(e.type);case Ft:return Mn("Lazy");case ie:return Mn("Suspense");case qe:return Mn("SuspenseList");case U:case V:case Ce:return Kl(e.type);case Z:return Kl(e.type.render);case w:return Go(e.type);default:return""}}function Jl(e){try{var t="",n=e;do t+=Qo(n),n=n.return;while(n);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Fi(e,t,n){var a=e.displayName;if(a)return a;var r=t.displayName||t.name||"";return r!==""?n+"("+r+")":n}function Wo(e){return e.displayName||"Context"}function Oe(e){if(e==null)return null;if(typeof e.tag=="number"&&f("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ur:return"Fragment";case _a:return"Portal";case Ai:return"Profiler";case Vl:return"StrictMode";case ki:return"Suspense";case zi:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Bl:var t=e;return Wo(t)+".Consumer";case Ni:var n=e;return Wo(n._context)+".Provider";case tt:return Fi(e,e.render,"ForwardRef");case Wr:var a=e.displayName||null;return a!==null?a:Oe(e.type)||"Memo";case Ot:{var r=e,i=r._payload,l=r._init;try{return Oe(l(i))}catch{return null}}}return null}function Zl(e,t,n){var a=t.displayName||t.name||"";return e.displayName||(a!==""?n+"("+a+")":n)}function eu(e){return e.displayName||"Context"}function pe(e){var t=e.tag,n=e.type;switch(t){case Qt:return"Cache";case we:var a=n;return eu(a)+".Consumer";case I:var r=n;return eu(r._context)+".Provider";case et:return"DehydratedFragment";case Z:return Zl(n,n.render,"ForwardRef");case ye:return"Fragment";case B:return n;case F:return"Portal";case M:return"Root";case J:return"Text";case Ft:return Oe(n);case ne:return n===Vl?"StrictMode":"Mode";case Fe:return"Offscreen";case ve:return"Profiler";case ia:return"Scope";case ie:return"Suspense";case qe:return"SuspenseList";case bt:return"TracingMarker";case w:case U:case vn:case V:case Be:case Ce:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;break}return null}var Io=m.ReactDebugCurrentFrame,Vt=null,Kr=!1;function dr(){{if(Vt===null)return null;var e=Vt._debugOwner;if(e!==null&&typeof e<"u")return pe(e)}return null}function Xo(){return Vt===null?"":Jl(Vt)}function Et(){Io.getCurrentStack=null,Vt=null,Kr=!1}function Je(e){Io.getCurrentStack=e===null?null:Xo,Vt=e,Kr=!1}function Ko(){return Vt}function bn(e){Kr=e}function ln(e){return""+e}function da(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return yn(e),e;default:return""}}var Ef={button:!0,checkbox:!0,image:!0,hidden:!0,radio:!0,reset:!0,submit:!0};function ji(e,t){Ef[t.type]||t.onChange||t.onInput||t.readOnly||t.disabled||t.value==null||f("You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`."),t.onChange||t.readOnly||t.disabled||t.checked==null||f("You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.")}function Jr(e){var t=e.type,n=e.nodeName;return n&&n.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jo(e){return e._valueTracker}function vr(e){e._valueTracker=null}function Zo(e){var t="";return e&&(Jr(e)?t=e.checked?"true":"false":t=e.value),t}function es(e){var t=Jr(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t);yn(e[t]);var a=""+e[t];if(!(e.hasOwnProperty(t)||typeof n>"u"||typeof n.get!="function"||typeof n.set!="function")){var r=n.get,i=n.set;Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(u){yn(u),a=""+u,i.call(this,u)}}),Object.defineProperty(e,t,{enumerable:n.enumerable});var l={getValue:function(){return a},setValue:function(u){yn(u),a=""+u},stopTracking:function(){vr(e),delete e[t]}};return l}}function Ma(e){Jo(e)||(e._valueTracker=es(e))}function Vi(e){if(!e)return!1;var t=Jo(e);if(!t)return!0;var n=t.getValue(),a=Zo(e);return a!==n?(t.setValue(a),!0):!1}function pr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Bi=!1,ts=!1,ns=!1,as=!1;function rs(e){var t=e.type==="checkbox"||e.type==="radio";return t?e.checked!=null:e.value!=null}function s(e,t){var n=e,a=t.checked,r=Se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:a??n._wrapperState.initialChecked});return r}function h(e,t){ji("input",t),t.checked!==void 0&&t.defaultChecked!==void 0&&!ts&&(f("%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://reactjs.org/link/controlled-components",dr()||"A component",t.type),ts=!0),t.value!==void 0&&t.defaultValue!==void 0&&!Bi&&(f("%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://reactjs.org/link/controlled-components",dr()||"A component",t.type),Bi=!0);var n=e,a=t.defaultValue==null?"":t.defaultValue;n._wrapperState={initialChecked:t.checked!=null?t.checked:t.defaultChecked,initialValue:da(t.value!=null?t.value:a),controlled:rs(t)}}function x(e,t){var n=e,a=t.checked;a!=null&&Ui(n,"checked",a,!1)}function O(e,t){var n=e;{var a=rs(t);!n._wrapperState.controlled&&a&&!as&&(f("A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://reactjs.org/link/controlled-components"),as=!0),n._wrapperState.controlled&&!a&&!ns&&(f("A component is changing a controlled input to be uncontrolled. This is likely caused by the value changing from a defined to undefined, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://reactjs.org/link/controlled-components"),ns=!0)}x(e,t);var r=da(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&n.value===""||n.value!=r)&&(n.value=ln(r)):n.value!==ln(r)&&(n.value=ln(r));else if(i==="submit"||i==="reset"){n.removeAttribute("value");return}t.hasOwnProperty("value")?se(n,t.type,r):t.hasOwnProperty("defaultValue")&&se(n,t.type,da(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(n.defaultChecked=!!t.defaultChecked)}function j(e,t,n){var a=e;if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type,i=r==="submit"||r==="reset";if(i&&(t.value===void 0||t.value===null))return;var l=ln(a._wrapperState.initialValue);n||l!==a.value&&(a.value=l),a.defaultValue=l}var u=a.name;u!==""&&(a.name=""),a.defaultChecked=!a.defaultChecked,a.defaultChecked=!!a._wrapperState.initialChecked,u!==""&&(a.name=u)}function ue(e,t){var n=e;O(n,t),K(n,t)}function K(e,t){var n=t.name;if(t.type==="radio"&&n!=null){for(var a=e;a.parentNode;)a=a.parentNode;ua(n,"name");for(var r=a.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),i=0;i<r.length;i++){var l=r[i];if(!(l===e||l.form!==e.form)){var u=Ps(l);if(!u)throw new Error("ReactDOMInput: Mixing React and non-React radio inputs with the same `name` is not supported.");Vi(l),O(l,u)}}}}function se(e,t,n){(t!=="number"||pr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=ln(e._wrapperState.initialValue):e.defaultValue!==ln(n)&&(e.defaultValue=ln(n)))}var Ee=!1,Ne=!1,$e=!1;function Ye(e,t){t.value==null&&(typeof t.children=="object"&&t.children!==null?c.Children.forEach(t.children,function(n){n!=null&&(typeof n=="string"||typeof n=="number"||Ne||(Ne=!0,f("Cannot infer the option value of complex children. Pass a `value` prop or use a plain string as children to <option>.")))}):t.dangerouslySetInnerHTML!=null&&($e||($e=!0,f("Pass a `value` prop if you set dangerouslyInnerHTML so React knows which value should be selected.")))),t.selected!=null&&!Ee&&(f("Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>."),Ee=!0)}function We(e,t){t.value!=null&&e.setAttribute("value",ln(da(t.value)))}var nt=Array.isArray;function Me(e){return nt(e)}var hr;hr=!1;function $i(){var e=dr();return e?`

Check the render method of \``+e+"`.":""}var tu=["value","defaultValue"];function Rf(e){{ji("select",e);for(var t=0;t<tu.length;t++){var n=tu[t];if(e[n]!=null){var a=Me(e[n]);e.multiple&&!a?f("The `%s` prop supplied to <select> must be an array if `multiple` is true.%s",n,$i()):!e.multiple&&a&&f("The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s",n,$i())}}}}function Ua(e,t,n,a){var r=e.options;if(t){for(var i=n,l={},u=0;u<i.length;u++)l["$"+i[u]]=!0;for(var o=0;o<r.length;o++){var d=l.hasOwnProperty("$"+r[o].value);r[o].selected!==d&&(r[o].selected=d),d&&a&&(r[o].defaultSelected=!0)}}else{for(var v=ln(da(n)),b=null,g=0;g<r.length;g++){if(r[g].value===v){r[g].selected=!0,a&&(r[g].defaultSelected=!0);return}b===null&&!r[g].disabled&&(b=r[g])}b!==null&&(b.selected=!0)}}function nu(e,t){return Se({},t,{value:void 0})}function au(e,t){var n=e;Rf(t),n._wrapperState={wasMultiple:!!t.multiple},t.value!==void 0&&t.defaultValue!==void 0&&!hr&&(f("Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled select element and remove one of these props. More info: https://reactjs.org/link/controlled-components"),hr=!0)}function Tf(e,t){var n=e;n.multiple=!!t.multiple;var a=t.value;a!=null?Ua(n,!!t.multiple,a,!1):t.defaultValue!=null&&Ua(n,!!t.multiple,t.defaultValue,!0)}function vC(e,t){var n=e,a=n._wrapperState.wasMultiple;n._wrapperState.wasMultiple=!!t.multiple;var r=t.value;r!=null?Ua(n,!!t.multiple,r,!1):a!==!!t.multiple&&(t.defaultValue!=null?Ua(n,!!t.multiple,t.defaultValue,!0):Ua(n,!!t.multiple,t.multiple?[]:"",!1))}function pC(e,t){var n=e,a=t.value;a!=null&&Ua(n,!!t.multiple,a,!1)}var Fh=!1;function xf(e,t){var n=e;if(t.dangerouslySetInnerHTML!=null)throw new Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");var a=Se({},t,{value:void 0,defaultValue:void 0,children:ln(n._wrapperState.initialValue)});return a}function jh(e,t){var n=e;ji("textarea",t),t.value!==void 0&&t.defaultValue!==void 0&&!Fh&&(f("%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled textarea and remove one of these props. More info: https://reactjs.org/link/controlled-components",dr()||"A component"),Fh=!0);var a=t.value;if(a==null){var r=t.children,i=t.defaultValue;if(r!=null){f("Use the `defaultValue` or `value` props instead of setting children on <textarea>.");{if(i!=null)throw new Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Me(r)){if(r.length>1)throw new Error("<textarea> can only have at most one child.");r=r[0]}i=r}}i==null&&(i=""),a=i}n._wrapperState={initialValue:da(a)}}function Vh(e,t){var n=e,a=da(t.value),r=da(t.defaultValue);if(a!=null){var i=ln(a);i!==n.value&&(n.value=i),t.defaultValue==null&&n.defaultValue!==i&&(n.defaultValue=i)}r!=null&&(n.defaultValue=ln(r))}function Bh(e,t){var n=e,a=n.textContent;a===n._wrapperState.initialValue&&a!==""&&a!==null&&(n.value=a)}function hC(e,t){Vh(e,t)}var Aa="http://www.w3.org/1999/xhtml",mC="http://www.w3.org/1998/Math/MathML",Df="http://www.w3.org/2000/svg";function wf(e){switch(e){case"svg":return Df;case"math":return mC;default:return Aa}}function Of(e,t){return e==null||e===Aa?wf(t):e===Df&&t==="foreignObject"?Aa:e}var yC=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,a,r){MSApp.execUnsafeLocalFunction(function(){return e(t,n,a,r)})}:e},is,$h=yC(function(e,t){if(e.namespaceURI===Df&&!("innerHTML"in e)){is=is||document.createElement("div"),is.innerHTML="<svg>"+t.valueOf().toString()+"</svg>";for(var n=is.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild);return}e.innerHTML=t}),un=1,Na=3,ut=8,ka=9,_f=11,ls=function(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===Na){n.nodeValue=t;return}}e.textContent=t},gC={animation:["animationDelay","animationDirection","animationDuration","animationFillMode","animationIterationCount","animationName","animationPlayState","animationTimingFunction"],background:["backgroundAttachment","backgroundClip","backgroundColor","backgroundImage","backgroundOrigin","backgroundPositionX","backgroundPositionY","backgroundRepeat","backgroundSize"],backgroundPosition:["backgroundPositionX","backgroundPositionY"],border:["borderBottomColor","borderBottomStyle","borderBottomWidth","borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRightColor","borderRightStyle","borderRightWidth","borderTopColor","borderTopStyle","borderTopWidth"],borderBlockEnd:["borderBlockEndColor","borderBlockEndStyle","borderBlockEndWidth"],borderBlockStart:["borderBlockStartColor","borderBlockStartStyle","borderBlockStartWidth"],borderBottom:["borderBottomColor","borderBottomStyle","borderBottomWidth"],borderColor:["borderBottomColor","borderLeftColor","borderRightColor","borderTopColor"],borderImage:["borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth"],borderInlineEnd:["borderInlineEndColor","borderInlineEndStyle","borderInlineEndWidth"],borderInlineStart:["borderInlineStartColor","borderInlineStartStyle","borderInlineStartWidth"],borderLeft:["borderLeftColor","borderLeftStyle","borderLeftWidth"],borderRadius:["borderBottomLeftRadius","borderBottomRightRadius","borderTopLeftRadius","borderTopRightRadius"],borderRight:["borderRightColor","borderRightStyle","borderRightWidth"],borderStyle:["borderBottomStyle","borderLeftStyle","borderRightStyle","borderTopStyle"],borderTop:["borderTopColor","borderTopStyle","borderTopWidth"],borderWidth:["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth"],columnRule:["columnRuleColor","columnRuleStyle","columnRuleWidth"],columns:["columnCount","columnWidth"],flex:["flexBasis","flexGrow","flexShrink"],flexFlow:["flexDirection","flexWrap"],font:["fontFamily","fontFeatureSettings","fontKerning","fontLanguageOverride","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition","fontWeight","lineHeight"],fontVariant:["fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition"],gap:["columnGap","rowGap"],grid:["gridAutoColumns","gridAutoFlow","gridAutoRows","gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],gridArea:["gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart"],gridColumn:["gridColumnEnd","gridColumnStart"],gridColumnGap:["columnGap"],gridGap:["columnGap","rowGap"],gridRow:["gridRowEnd","gridRowStart"],gridRowGap:["rowGap"],gridTemplate:["gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],listStyle:["listStyleImage","listStylePosition","listStyleType"],margin:["marginBottom","marginLeft","marginRight","marginTop"],marker:["markerEnd","markerMid","markerStart"],mask:["maskClip","maskComposite","maskImage","maskMode","maskOrigin","maskPositionX","maskPositionY","maskRepeat","maskSize"],maskPosition:["maskPositionX","maskPositionY"],outline:["outlineColor","outlineStyle","outlineWidth"],overflow:["overflowX","overflowY"],padding:["paddingBottom","paddingLeft","paddingRight","paddingTop"],placeContent:["alignContent","justifyContent"],placeItems:["alignItems","justifyItems"],placeSelf:["alignSelf","justifySelf"],textDecoration:["textDecorationColor","textDecorationLine","textDecorationStyle"],textEmphasis:["textEmphasisColor","textEmphasisStyle"],transition:["transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction"],wordWrap:["overflowWrap"]},ru={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0};function bC(e,t){return e+t.charAt(0).toUpperCase()+t.substring(1)}var SC=["Webkit","ms","Moz","O"];Object.keys(ru).forEach(function(e){SC.forEach(function(t){ru[bC(t,e)]=ru[e]})});function Lf(e,t,n){var a=t==null||typeof t=="boolean"||t==="";return a?"":!n&&typeof t=="number"&&t!==0&&!(ru.hasOwnProperty(e)&&ru[e])?t+"px":(Oa(t,e),(""+t).trim())}var CC=/([A-Z])/g,EC=/^ms-/;function RC(e){return e.replace(CC,"-$1").toLowerCase().replace(EC,"-ms-")}var Yh=function(){};{var TC=/^(?:webkit|moz|o)[A-Z]/,xC=/^-ms-/,DC=/-(.)/g,Ph=/;\s*$/,Yi={},Mf={},qh=!1,Gh=!1,wC=function(e){return e.replace(DC,function(t,n){return n.toUpperCase()})},OC=function(e){Yi.hasOwnProperty(e)&&Yi[e]||(Yi[e]=!0,f("Unsupported style property %s. Did you mean %s?",e,wC(e.replace(xC,"ms-"))))},_C=function(e){Yi.hasOwnProperty(e)&&Yi[e]||(Yi[e]=!0,f("Unsupported vendor-prefixed style property %s. Did you mean %s?",e,e.charAt(0).toUpperCase()+e.slice(1)))},LC=function(e,t){Mf.hasOwnProperty(t)&&Mf[t]||(Mf[t]=!0,f(`Style property values shouldn't contain a semicolon. Try "%s: %s" instead.`,e,t.replace(Ph,"")))},MC=function(e,t){qh||(qh=!0,f("`NaN` is an invalid value for the `%s` css style property.",e))},UC=function(e,t){Gh||(Gh=!0,f("`Infinity` is an invalid value for the `%s` css style property.",e))};Yh=function(e,t){e.indexOf("-")>-1?OC(e):TC.test(e)?_C(e):Ph.test(t)&&LC(e,t),typeof t=="number"&&(isNaN(t)?MC(e,t):isFinite(t)||UC(e,t))}}var AC=Yh;function NC(e){{var t="",n="";for(var a in e)if(e.hasOwnProperty(a)){var r=e[a];if(r!=null){var i=a.indexOf("--")===0;t+=n+(i?a:RC(a))+":",t+=Lf(a,r,i),n=";"}}return t||null}}function Qh(e,t){var n=e.style;for(var a in t)if(t.hasOwnProperty(a)){var r=a.indexOf("--")===0;r||AC(a,t[a]);var i=Lf(a,t[a],r);a==="float"&&(a="cssFloat"),r?n.setProperty(a,i):n[a]=i}}function kC(e){return e==null||typeof e=="boolean"||e===""}function Wh(e){var t={};for(var n in e)for(var a=gC[n]||[n],r=0;r<a.length;r++)t[a[r]]=n;return t}function zC(e,t){{if(!t)return;var n=Wh(e),a=Wh(t),r={};for(var i in n){var l=n[i],u=a[i];if(u&&l!==u){var o=l+","+u;if(r[o])continue;r[o]=!0,f("%s a style property during rerender (%s) when a conflicting property is set (%s) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.",kC(e[l])?"Removing":"Updating",l,u)}}}}var HC={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},FC=Se({menuitem:!0},HC),jC="__html";function Uf(e,t){if(t){if(FC[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw new Error(e+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw new Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if(typeof t.dangerouslySetInnerHTML!="object"||!(jC in t.dangerouslySetInnerHTML))throw new Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.")}if(!t.suppressContentEditableWarning&&t.contentEditable&&t.children!=null&&f("A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional."),t.style!=null&&typeof t.style!="object")throw new Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.")}}function Zr(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var us={accept:"accept",acceptcharset:"acceptCharset","accept-charset":"acceptCharset",accesskey:"accessKey",action:"action",allowfullscreen:"allowFullScreen",alt:"alt",as:"as",async:"async",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",capture:"capture",cellpadding:"cellPadding",cellspacing:"cellSpacing",challenge:"challenge",charset:"charSet",checked:"checked",children:"children",cite:"cite",class:"className",classid:"classID",classname:"className",cols:"cols",colspan:"colSpan",content:"content",contenteditable:"contentEditable",contextmenu:"contextMenu",controls:"controls",controlslist:"controlsList",coords:"coords",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",data:"data",datetime:"dateTime",default:"default",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",defer:"defer",dir:"dir",disabled:"disabled",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback",download:"download",draggable:"draggable",enctype:"encType",enterkeyhint:"enterKeyHint",for:"htmlFor",form:"form",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",headers:"headers",height:"height",hidden:"hidden",high:"high",href:"href",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",icon:"icon",id:"id",imagesizes:"imageSizes",imagesrcset:"imageSrcSet",innerhtml:"innerHTML",inputmode:"inputMode",integrity:"integrity",is:"is",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",kind:"kind",label:"label",lang:"lang",list:"list",loop:"loop",low:"low",manifest:"manifest",marginwidth:"marginWidth",marginheight:"marginHeight",max:"max",maxlength:"maxLength",media:"media",mediagroup:"mediaGroup",method:"method",min:"min",minlength:"minLength",multiple:"multiple",muted:"muted",name:"name",nomodule:"noModule",nonce:"nonce",novalidate:"noValidate",open:"open",optimum:"optimum",pattern:"pattern",placeholder:"placeholder",playsinline:"playsInline",poster:"poster",preload:"preload",profile:"profile",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rel:"rel",required:"required",reversed:"reversed",role:"role",rows:"rows",rowspan:"rowSpan",sandbox:"sandbox",scope:"scope",scoped:"scoped",scrolling:"scrolling",seamless:"seamless",selected:"selected",shape:"shape",size:"size",sizes:"sizes",span:"span",spellcheck:"spellCheck",src:"src",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",start:"start",step:"step",style:"style",summary:"summary",tabindex:"tabIndex",target:"target",title:"title",type:"type",usemap:"useMap",value:"value",width:"width",wmode:"wmode",wrap:"wrap",about:"about",accentheight:"accentHeight","accent-height":"accentHeight",accumulate:"accumulate",additive:"additive",alignmentbaseline:"alignmentBaseline","alignment-baseline":"alignmentBaseline",allowreorder:"allowReorder",alphabetic:"alphabetic",amplitude:"amplitude",arabicform:"arabicForm","arabic-form":"arabicForm",ascent:"ascent",attributename:"attributeName",attributetype:"attributeType",autoreverse:"autoReverse",azimuth:"azimuth",basefrequency:"baseFrequency",baselineshift:"baselineShift","baseline-shift":"baselineShift",baseprofile:"baseProfile",bbox:"bbox",begin:"begin",bias:"bias",by:"by",calcmode:"calcMode",capheight:"capHeight","cap-height":"capHeight",clip:"clip",clippath:"clipPath","clip-path":"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","clip-rule":"clipRule",color:"color",colorinterpolation:"colorInterpolation","color-interpolation":"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters","color-interpolation-filters":"colorInterpolationFilters",colorprofile:"colorProfile","color-profile":"colorProfile",colorrendering:"colorRendering","color-rendering":"colorRendering",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",cursor:"cursor",cx:"cx",cy:"cy",d:"d",datatype:"datatype",decelerate:"decelerate",descent:"descent",diffuseconstant:"diffuseConstant",direction:"direction",display:"display",divisor:"divisor",dominantbaseline:"dominantBaseline","dominant-baseline":"dominantBaseline",dur:"dur",dx:"dx",dy:"dy",edgemode:"edgeMode",elevation:"elevation",enablebackground:"enableBackground","enable-background":"enableBackground",end:"end",exponent:"exponent",externalresourcesrequired:"externalResourcesRequired",fill:"fill",fillopacity:"fillOpacity","fill-opacity":"fillOpacity",fillrule:"fillRule","fill-rule":"fillRule",filter:"filter",filterres:"filterRes",filterunits:"filterUnits",floodopacity:"floodOpacity","flood-opacity":"floodOpacity",floodcolor:"floodColor","flood-color":"floodColor",focusable:"focusable",fontfamily:"fontFamily","font-family":"fontFamily",fontsize:"fontSize","font-size":"fontSize",fontsizeadjust:"fontSizeAdjust","font-size-adjust":"fontSizeAdjust",fontstretch:"fontStretch","font-stretch":"fontStretch",fontstyle:"fontStyle","font-style":"fontStyle",fontvariant:"fontVariant","font-variant":"fontVariant",fontweight:"fontWeight","font-weight":"fontWeight",format:"format",from:"from",fx:"fx",fy:"fy",g1:"g1",g2:"g2",glyphname:"glyphName","glyph-name":"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal","glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical","glyph-orientation-vertical":"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",hanging:"hanging",horizadvx:"horizAdvX","horiz-adv-x":"horizAdvX",horizoriginx:"horizOriginX","horiz-origin-x":"horizOriginX",ideographic:"ideographic",imagerendering:"imageRendering","image-rendering":"imageRendering",in2:"in2",in:"in",inlist:"inlist",intercept:"intercept",k1:"k1",k2:"k2",k3:"k3",k4:"k4",k:"k",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",kerning:"kerning",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",letterspacing:"letterSpacing","letter-spacing":"letterSpacing",lightingcolor:"lightingColor","lighting-color":"lightingColor",limitingconeangle:"limitingConeAngle",local:"local",markerend:"markerEnd","marker-end":"markerEnd",markerheight:"markerHeight",markermid:"markerMid","marker-mid":"markerMid",markerstart:"markerStart","marker-start":"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",mask:"mask",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",mathematical:"mathematical",mode:"mode",numoctaves:"numOctaves",offset:"offset",opacity:"opacity",operator:"operator",order:"order",orient:"orient",orientation:"orientation",origin:"origin",overflow:"overflow",overlineposition:"overlinePosition","overline-position":"overlinePosition",overlinethickness:"overlineThickness","overline-thickness":"overlineThickness",paintorder:"paintOrder","paint-order":"paintOrder",panose1:"panose1","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointerevents:"pointerEvents","pointer-events":"pointerEvents",points:"points",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",prefix:"prefix",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",property:"property",r:"r",radius:"radius",refx:"refX",refy:"refY",renderingintent:"renderingIntent","rendering-intent":"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",resource:"resource",restart:"restart",result:"result",results:"results",rotate:"rotate",rx:"rx",ry:"ry",scale:"scale",security:"security",seed:"seed",shaperendering:"shapeRendering","shape-rendering":"shapeRendering",slope:"slope",spacing:"spacing",specularconstant:"specularConstant",specularexponent:"specularExponent",speed:"speed",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stemh:"stemh",stemv:"stemv",stitchtiles:"stitchTiles",stopcolor:"stopColor","stop-color":"stopColor",stopopacity:"stopOpacity","stop-opacity":"stopOpacity",strikethroughposition:"strikethroughPosition","strikethrough-position":"strikethroughPosition",strikethroughthickness:"strikethroughThickness","strikethrough-thickness":"strikethroughThickness",string:"string",stroke:"stroke",strokedasharray:"strokeDasharray","stroke-dasharray":"strokeDasharray",strokedashoffset:"strokeDashoffset","stroke-dashoffset":"strokeDashoffset",strokelinecap:"strokeLinecap","stroke-linecap":"strokeLinecap",strokelinejoin:"strokeLinejoin","stroke-linejoin":"strokeLinejoin",strokemiterlimit:"strokeMiterlimit","stroke-miterlimit":"strokeMiterlimit",strokewidth:"strokeWidth","stroke-width":"strokeWidth",strokeopacity:"strokeOpacity","stroke-opacity":"strokeOpacity",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textanchor:"textAnchor","text-anchor":"textAnchor",textdecoration:"textDecoration","text-decoration":"textDecoration",textlength:"textLength",textrendering:"textRendering","text-rendering":"textRendering",to:"to",transform:"transform",typeof:"typeof",u1:"u1",u2:"u2",underlineposition:"underlinePosition","underline-position":"underlinePosition",underlinethickness:"underlineThickness","underline-thickness":"underlineThickness",unicode:"unicode",unicodebidi:"unicodeBidi","unicode-bidi":"unicodeBidi",unicoderange:"unicodeRange","unicode-range":"unicodeRange",unitsperem:"unitsPerEm","units-per-em":"unitsPerEm",unselectable:"unselectable",valphabetic:"vAlphabetic","v-alphabetic":"vAlphabetic",values:"values",vectoreffect:"vectorEffect","vector-effect":"vectorEffect",version:"version",vertadvy:"vertAdvY","vert-adv-y":"vertAdvY",vertoriginx:"vertOriginX","vert-origin-x":"vertOriginX",vertoriginy:"vertOriginY","vert-origin-y":"vertOriginY",vhanging:"vHanging","v-hanging":"vHanging",videographic:"vIdeographic","v-ideographic":"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",visibility:"visibility",vmathematical:"vMathematical","v-mathematical":"vMathematical",vocab:"vocab",widths:"widths",wordspacing:"wordSpacing","word-spacing":"wordSpacing",writingmode:"writingMode","writing-mode":"writingMode",x1:"x1",x2:"x2",x:"x",xchannelselector:"xChannelSelector",xheight:"xHeight","x-height":"xHeight",xlinkactuate:"xlinkActuate","xlink:actuate":"xlinkActuate",xlinkarcrole:"xlinkArcrole","xlink:arcrole":"xlinkArcrole",xlinkhref:"xlinkHref","xlink:href":"xlinkHref",xlinkrole:"xlinkRole","xlink:role":"xlinkRole",xlinkshow:"xlinkShow","xlink:show":"xlinkShow",xlinktitle:"xlinkTitle","xlink:title":"xlinkTitle",xlinktype:"xlinkType","xlink:type":"xlinkType",xmlbase:"xmlBase","xml:base":"xmlBase",xmllang:"xmlLang","xml:lang":"xmlLang",xmlns:"xmlns","xml:space":"xmlSpace",xmlnsxlink:"xmlnsXlink","xmlns:xlink":"xmlnsXlink",xmlspace:"xmlSpace",y1:"y1",y2:"y2",y:"y",ychannelselector:"yChannelSelector",z:"z",zoomandpan:"zoomAndPan"},Ih={"aria-current":0,"aria-description":0,"aria-details":0,"aria-disabled":0,"aria-hidden":0,"aria-invalid":0,"aria-keyshortcuts":0,"aria-label":0,"aria-roledescription":0,"aria-autocomplete":0,"aria-checked":0,"aria-expanded":0,"aria-haspopup":0,"aria-level":0,"aria-modal":0,"aria-multiline":0,"aria-multiselectable":0,"aria-orientation":0,"aria-placeholder":0,"aria-pressed":0,"aria-readonly":0,"aria-required":0,"aria-selected":0,"aria-sort":0,"aria-valuemax":0,"aria-valuemin":0,"aria-valuenow":0,"aria-valuetext":0,"aria-atomic":0,"aria-busy":0,"aria-live":0,"aria-relevant":0,"aria-dropeffect":0,"aria-grabbed":0,"aria-activedescendant":0,"aria-colcount":0,"aria-colindex":0,"aria-colspan":0,"aria-controls":0,"aria-describedby":0,"aria-errormessage":0,"aria-flowto":0,"aria-labelledby":0,"aria-owns":0,"aria-posinset":0,"aria-rowcount":0,"aria-rowindex":0,"aria-rowspan":0,"aria-setsize":0},Pi={},VC=new RegExp("^(aria)-["+ae+"]*$"),BC=new RegExp("^(aria)[A-Z]["+ae+"]*$");function $C(e,t){{if(rn.call(Pi,t)&&Pi[t])return!0;if(BC.test(t)){var n="aria-"+t.slice(4).toLowerCase(),a=Ih.hasOwnProperty(n)?n:null;if(a==null)return f("Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.",t),Pi[t]=!0,!0;if(t!==a)return f("Invalid ARIA attribute `%s`. Did you mean `%s`?",t,a),Pi[t]=!0,!0}if(VC.test(t)){var r=t.toLowerCase(),i=Ih.hasOwnProperty(r)?r:null;if(i==null)return Pi[t]=!0,!1;if(t!==i)return f("Unknown ARIA attribute `%s`. Did you mean `%s`?",t,i),Pi[t]=!0,!0}}return!0}function YC(e,t){{var n=[];for(var a in t){var r=$C(e,a);r||n.push(a)}var i=n.map(function(l){return"`"+l+"`"}).join(", ");n.length===1?f("Invalid aria prop %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props",i,e):n.length>1&&f("Invalid aria props %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props",i,e)}}function PC(e,t){Zr(e,t)||YC(e,t)}var Xh=!1;function qC(e,t){{if(e!=="input"&&e!=="textarea"&&e!=="select")return;t!=null&&t.value===null&&!Xh&&(Xh=!0,e==="select"&&t.multiple?f("`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.",e):f("`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.",e))}}var Kh=function(){};{var Xt={},Jh=/^on./,GC=/^on[^A-Z]/,QC=new RegExp("^(aria)-["+ae+"]*$"),WC=new RegExp("^(aria)[A-Z]["+ae+"]*$");Kh=function(e,t,n,a){if(rn.call(Xt,t)&&Xt[t])return!0;var r=t.toLowerCase();if(r==="onfocusin"||r==="onfocusout")return f("React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React."),Xt[t]=!0,!0;if(a!=null){var i=a.registrationNameDependencies,l=a.possibleRegistrationNames;if(i.hasOwnProperty(t))return!0;var u=l.hasOwnProperty(r)?l[r]:null;if(u!=null)return f("Invalid event handler property `%s`. Did you mean `%s`?",t,u),Xt[t]=!0,!0;if(Jh.test(t))return f("Unknown event handler property `%s`. It will be ignored.",t),Xt[t]=!0,!0}else if(Jh.test(t))return GC.test(t)&&f("Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.",t),Xt[t]=!0,!0;if(QC.test(t)||WC.test(t))return!0;if(r==="innerhtml")return f("Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`."),Xt[t]=!0,!0;if(r==="aria")return f("The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead."),Xt[t]=!0,!0;if(r==="is"&&n!==null&&n!==void 0&&typeof n!="string")return f("Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.",typeof n),Xt[t]=!0,!0;if(typeof n=="number"&&isNaN(n))return f("Received NaN for the `%s` attribute. If this is expected, cast the value to a string.",t),Xt[t]=!0,!0;var o=fa(t),d=o!==null&&o.type===oa;if(us.hasOwnProperty(r)){var v=us[r];if(v!==t)return f("Invalid DOM property `%s`. Did you mean `%s`?",t,v),Xt[t]=!0,!0}else if(!d&&t!==r)return f("React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.",t,r),Xt[t]=!0,!0;return typeof n=="boolean"&&Ln(t,n,o,!1)?(n?f('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.',n,t,t,n,t):f('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.',n,t,t,n,t,t,t),Xt[t]=!0,!0):d?!0:Ln(t,n,o,!1)?(Xt[t]=!0,!1):((n==="false"||n==="true")&&o!==null&&o.type===It&&(f("Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?",n,t,n==="false"?"The browser will interpret it as a truthy value.":'Although this works, it will not work as expected if you pass the string "false".',t,n),Xt[t]=!0),!0)}}var IC=function(e,t,n){{var a=[];for(var r in t){var i=Kh(e,r,t[r],n);i||a.push(r)}var l=a.map(function(u){return"`"+u+"`"}).join(", ");a.length===1?f("Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://reactjs.org/link/attribute-behavior ",l,e):a.length>1&&f("Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://reactjs.org/link/attribute-behavior ",l,e)}};function XC(e,t,n){Zr(e,t)||IC(e,t,n)}var Zh=1,Af=2,iu=4,KC=Zh|Af|iu,lu=null;function JC(e){lu!==null&&f("Expected currently replaying event to be null. This error is likely caused by a bug in React. Please file an issue."),lu=e}function ZC(){lu===null&&f("Expected currently replaying event to not be null. This error is likely caused by a bug in React. Please file an issue."),lu=null}function eE(e){return e===lu}function Nf(e){var t=e.target||e.srcElement||window;return t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===Na?t.parentNode:t}var kf=null,qi=null,Gi=null;function em(e){var t=Rr(e);if(t){if(typeof kf!="function")throw new Error("setRestoreImplementation() needs to be called to handle a target for controlled events. This error is likely caused by a bug in React. Please file an issue.");var n=t.stateNode;if(n){var a=Ps(n);kf(t.stateNode,t.type,a)}}}function tE(e){kf=e}function tm(e){qi?Gi?Gi.push(e):Gi=[e]:qi=e}function nE(){return qi!==null||Gi!==null}function nm(){if(qi){var e=qi,t=Gi;if(qi=null,Gi=null,em(e),t)for(var n=0;n<t.length;n++)em(t[n])}}var am=function(e,t){return e(t)},rm=function(){},zf=!1;function aE(){var e=nE();e&&(rm(),nm())}function im(e,t,n){if(zf)return e(t,n);zf=!0;try{return am(e,t,n)}finally{zf=!1,aE()}}function rE(e,t,n){am=e,rm=n}function iE(e){return e==="button"||e==="input"||e==="select"||e==="textarea"}function lE(e,t,n){switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":return!!(n.disabled&&iE(t));default:return!1}}function uu(e,t){var n=e.stateNode;if(n===null)return null;var a=Ps(n);if(a===null)return null;var r=a[t];if(lE(t,e.type,a))return null;if(r&&typeof r!="function")throw new Error("Expected `"+t+"` listener to be a function, instead got a value of `"+typeof r+"` type.");return r}var Hf=!1;if(St)try{var ou={};Object.defineProperty(ou,"passive",{get:function(){Hf=!0}}),window.addEventListener("test",ou,ou),window.removeEventListener("test",ou,ou)}catch{Hf=!1}function lm(e,t,n,a,r,i,l,u,o){var d=Array.prototype.slice.call(arguments,3);try{t.apply(n,d)}catch(v){this.onError(v)}}var um=lm;if(typeof window<"u"&&typeof window.dispatchEvent=="function"&&typeof document<"u"&&typeof document.createEvent=="function"){var Ff=document.createElement("react");um=function(t,n,a,r,i,l,u,o,d){if(typeof document>"u"||document===null)throw new Error("The `document` global was defined when React was initialized, but is not defined anymore. This can happen in a test environment if a component schedules an update from an asynchronous callback, but the test has already finished running. To solve this, you can either unmount the component at the end of your test (and ensure that any asynchronous operations get canceled in `componentWillUnmount`), or you can change the test itself to be asynchronous.");var v=document.createEvent("Event"),b=!1,g=!0,R=window.event,D=Object.getOwnPropertyDescriptor(window,"event");function _(){Ff.removeEventListener(L,te,!1),typeof window.event<"u"&&window.hasOwnProperty("event")&&(window.event=R)}var Y=Array.prototype.slice.call(arguments,3);function te(){b=!0,_(),n.apply(a,Y),g=!1}var ee,xe=!1,ge=!1;function C(E){if(ee=E.error,xe=!0,ee===null&&E.colno===0&&E.lineno===0&&(ge=!0),E.defaultPrevented&&ee!=null&&typeof ee=="object")try{ee._suppressLogging=!0}catch{}}var L="react-"+(t||"invokeguardedcallback");if(window.addEventListener("error",C),Ff.addEventListener(L,te,!1),v.initEvent(L,!1,!1),Ff.dispatchEvent(v),D&&Object.defineProperty(window,"event",D),b&&g&&(xe?ge&&(ee=new Error("A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://reactjs.org/link/crossorigin-error for more information.")):ee=new Error(`An error was thrown inside one of your components, but React doesn't know what it was. This is likely due to browser flakiness. React does its best to preserve the "Pause on exceptions" behavior of the DevTools, which requires some DEV-mode only tricks. It's possible that these don't work in your browser. Try triggering the error in production mode, or switching to a modern browser. If you suspect that this is actually an issue with React, please file an issue.`),this.onError(ee)),window.removeEventListener("error",C),!b)return _(),lm.apply(this,arguments)}}var uE=um,Qi=!1,os=null,ss=!1,jf=null,oE={onError:function(e){Qi=!0,os=e}};function Vf(e,t,n,a,r,i,l,u,o){Qi=!1,os=null,uE.apply(oE,arguments)}function sE(e,t,n,a,r,i,l,u,o){if(Vf.apply(this,arguments),Qi){var d=Bf();ss||(ss=!0,jf=d)}}function cE(){if(ss){var e=jf;throw ss=!1,jf=null,e}}function fE(){return Qi}function Bf(){if(Qi){var e=os;return Qi=!1,os=null,e}else throw new Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.")}function Wi(e){return e._reactInternals}function dE(e){return e._reactInternals!==void 0}function vE(e,t){e._reactInternals=t}var le=0,Ii=1,ot=2,De=4,ei=16,su=32,$f=64,Ue=128,za=256,mr=512,ti=1024,Pn=2048,Ha=4096,ni=8192,cs=16384,pE=Pn|De|$f|mr|ti|cs,hE=32767,cu=32768,Kt=65536,Yf=131072,om=1048576,Pf=2097152,ai=4194304,qf=8388608,Fa=16777216,fs=33554432,Gf=De|ti|0,Qf=ot|De|ei|su|mr|Ha|ni,fu=De|$f|mr|ni,Xi=Pn|ei,ja=ai|qf|Pf,mE=m.ReactCurrentOwner;function ri(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{var a=t;do t=a,(t.flags&(ot|Ha))!==le&&(n=t.return),a=t.return;while(a)}return t.tag===M?n:null}function sm(e){if(e.tag===ie){var t=e.memoizedState;if(t===null){var n=e.alternate;n!==null&&(t=n.memoizedState)}if(t!==null)return t.dehydrated}return null}function cm(e){return e.tag===M?e.stateNode.containerInfo:null}function yE(e){return ri(e)===e}function gE(e){{var t=mE.current;if(t!==null&&t.tag===w){var n=t,a=n.stateNode;a._warnedAboutRefsInRender||f("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",pe(n)||"A component"),a._warnedAboutRefsInRender=!0}}var r=Wi(e);return r?ri(r)===r:!1}function fm(e){if(ri(e)!==e)throw new Error("Unable to find node on an unmounted component.")}function dm(e){var t=e.alternate;if(!t){var n=ri(e);if(n===null)throw new Error("Unable to find node on an unmounted component.");return n!==e?null:e}for(var a=e,r=t;;){var i=a.return;if(i===null)break;var l=i.alternate;if(l===null){var u=i.return;if(u!==null){a=r=u;continue}break}if(i.child===l.child){for(var o=i.child;o;){if(o===a)return fm(i),e;if(o===r)return fm(i),t;o=o.sibling}throw new Error("Unable to find node on an unmounted component.")}if(a.return!==r.return)a=i,r=l;else{for(var d=!1,v=i.child;v;){if(v===a){d=!0,a=i,r=l;break}if(v===r){d=!0,r=i,a=l;break}v=v.sibling}if(!d){for(v=l.child;v;){if(v===a){d=!0,a=l,r=i;break}if(v===r){d=!0,r=l,a=i;break}v=v.sibling}if(!d)throw new Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(a.alternate!==r)throw new Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(a.tag!==M)throw new Error("Unable to find node on an unmounted component.");return a.stateNode.current===a?e:t}function vm(e){var t=dm(e);return t!==null?pm(t):null}function pm(e){if(e.tag===B||e.tag===J)return e;for(var t=e.child;t!==null;){var n=pm(t);if(n!==null)return n;t=t.sibling}return null}function bE(e){var t=dm(e);return t!==null?hm(t):null}function hm(e){if(e.tag===B||e.tag===J)return e;for(var t=e.child;t!==null;){if(t.tag!==F){var n=hm(t);if(n!==null)return n}t=t.sibling}return null}var mm=p.unstable_scheduleCallback,SE=p.unstable_cancelCallback,CE=p.unstable_shouldYield,EE=p.unstable_requestPaint,Rt=p.unstable_now,RE=p.unstable_getCurrentPriorityLevel,ds=p.unstable_ImmediatePriority,Wf=p.unstable_UserBlockingPriority,ii=p.unstable_NormalPriority,TE=p.unstable_LowPriority,If=p.unstable_IdlePriority,xE=p.unstable_yieldValue,DE=p.unstable_setDisableYieldValue,Ki=null,Bt=null,G=null,va=!1,qn=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u";function wE(e){if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u")return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return f("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools"),!0;try{ar&&(e=Se({},e,{getLaneLabelMap:AE,injectProfilingHooks:UE})),Ki=t.inject(e),Bt=t}catch(n){f("React instrumentation encountered an error: %s.",n)}return!!t.checkDCE}function OE(e,t){if(Bt&&typeof Bt.onScheduleFiberRoot=="function")try{Bt.onScheduleFiberRoot(Ki,e,t)}catch(n){va||(va=!0,f("React instrumentation encountered an error: %s",n))}}function _E(e,t){if(Bt&&typeof Bt.onCommitFiberRoot=="function")try{var n=(e.current.flags&Ue)===Ue;if(Yr){var a;switch(t){case En:a=ds;break;case Ba:a=Wf;break;case $a:a=ii;break;case bs:a=If;break;default:a=ii;break}Bt.onCommitFiberRoot(Ki,e,a,n)}}catch(r){va||(va=!0,f("React instrumentation encountered an error: %s",r))}}function LE(e){if(Bt&&typeof Bt.onPostCommitFiberRoot=="function")try{Bt.onPostCommitFiberRoot(Ki,e)}catch(t){va||(va=!0,f("React instrumentation encountered an error: %s",t))}}function ME(e){if(Bt&&typeof Bt.onCommitFiberUnmount=="function")try{Bt.onCommitFiberUnmount(Ki,e)}catch(t){va||(va=!0,f("React instrumentation encountered an error: %s",t))}}function Tt(e){if(typeof xE=="function"&&(DE(e),S(e)),Bt&&typeof Bt.setStrictMode=="function")try{Bt.setStrictMode(Ki,e)}catch(t){va||(va=!0,f("React instrumentation encountered an error: %s",t))}}function UE(e){G=e}function AE(){{for(var e=new Map,t=1,n=0;n<Kf;n++){var a=ZE(t);e.set(t,a),t*=2}return e}}function NE(e){G!==null&&typeof G.markCommitStarted=="function"&&G.markCommitStarted(e)}function ym(){G!==null&&typeof G.markCommitStopped=="function"&&G.markCommitStopped()}function du(e){G!==null&&typeof G.markComponentRenderStarted=="function"&&G.markComponentRenderStarted(e)}function Ji(){G!==null&&typeof G.markComponentRenderStopped=="function"&&G.markComponentRenderStopped()}function kE(e){G!==null&&typeof G.markComponentPassiveEffectMountStarted=="function"&&G.markComponentPassiveEffectMountStarted(e)}function zE(){G!==null&&typeof G.markComponentPassiveEffectMountStopped=="function"&&G.markComponentPassiveEffectMountStopped()}function HE(e){G!==null&&typeof G.markComponentPassiveEffectUnmountStarted=="function"&&G.markComponentPassiveEffectUnmountStarted(e)}function FE(){G!==null&&typeof G.markComponentPassiveEffectUnmountStopped=="function"&&G.markComponentPassiveEffectUnmountStopped()}function jE(e){G!==null&&typeof G.markComponentLayoutEffectMountStarted=="function"&&G.markComponentLayoutEffectMountStarted(e)}function VE(){G!==null&&typeof G.markComponentLayoutEffectMountStopped=="function"&&G.markComponentLayoutEffectMountStopped()}function gm(e){G!==null&&typeof G.markComponentLayoutEffectUnmountStarted=="function"&&G.markComponentLayoutEffectUnmountStarted(e)}function bm(){G!==null&&typeof G.markComponentLayoutEffectUnmountStopped=="function"&&G.markComponentLayoutEffectUnmountStopped()}function BE(e,t,n){G!==null&&typeof G.markComponentErrored=="function"&&G.markComponentErrored(e,t,n)}function $E(e,t,n){G!==null&&typeof G.markComponentSuspended=="function"&&G.markComponentSuspended(e,t,n)}function YE(e){G!==null&&typeof G.markLayoutEffectsStarted=="function"&&G.markLayoutEffectsStarted(e)}function PE(){G!==null&&typeof G.markLayoutEffectsStopped=="function"&&G.markLayoutEffectsStopped()}function qE(e){G!==null&&typeof G.markPassiveEffectsStarted=="function"&&G.markPassiveEffectsStarted(e)}function GE(){G!==null&&typeof G.markPassiveEffectsStopped=="function"&&G.markPassiveEffectsStopped()}function Sm(e){G!==null&&typeof G.markRenderStarted=="function"&&G.markRenderStarted(e)}function QE(){G!==null&&typeof G.markRenderYielded=="function"&&G.markRenderYielded()}function Cm(){G!==null&&typeof G.markRenderStopped=="function"&&G.markRenderStopped()}function WE(e){G!==null&&typeof G.markRenderScheduled=="function"&&G.markRenderScheduled(e)}function IE(e,t){G!==null&&typeof G.markForceUpdateScheduled=="function"&&G.markForceUpdateScheduled(e,t)}function Xf(e,t){G!==null&&typeof G.markStateUpdateScheduled=="function"&&G.markStateUpdateScheduled(e,t)}var re=0,Re=1,ke=2,at=8,pa=16,Em=Math.clz32?Math.clz32:JE,XE=Math.log,KE=Math.LN2;function JE(e){var t=e>>>0;return t===0?32:31-(XE(t)/KE|0)|0}var Kf=31,N=0,xt=0,ce=1,Zi=2,Va=4,li=8,ha=16,vu=32,el=4194240,pu=64,Jf=128,Zf=256,ed=512,td=1024,nd=2048,ad=4096,rd=8192,id=16384,ld=32768,ud=65536,od=131072,sd=262144,cd=524288,fd=1048576,dd=2097152,vs=130023424,tl=4194304,vd=8388608,pd=16777216,hd=33554432,md=67108864,Rm=tl,hu=134217728,Tm=268435455,mu=268435456,ui=536870912,Sn=1073741824;function ZE(e){{if(e&ce)return"Sync";if(e&Zi)return"InputContinuousHydration";if(e&Va)return"InputContinuous";if(e&li)return"DefaultHydration";if(e&ha)return"Default";if(e&vu)return"TransitionHydration";if(e&el)return"Transition";if(e&vs)return"Retry";if(e&hu)return"SelectiveHydration";if(e&mu)return"IdleHydration";if(e&ui)return"Idle";if(e&Sn)return"Offscreen"}}var Ge=-1,ps=pu,hs=tl;function yu(e){switch(oi(e)){case ce:return ce;case Zi:return Zi;case Va:return Va;case li:return li;case ha:return ha;case vu:return vu;case pu:case Jf:case Zf:case ed:case td:case nd:case ad:case rd:case id:case ld:case ud:case od:case sd:case cd:case fd:case dd:return e&el;case tl:case vd:case pd:case hd:case md:return e&vs;case hu:return hu;case mu:return mu;case ui:return ui;case Sn:return Sn;default:return f("Should have found matching lanes. This is a bug in React."),e}}function ms(e,t){var n=e.pendingLanes;if(n===N)return N;var a=N,r=e.suspendedLanes,i=e.pingedLanes,l=n&Tm;if(l!==N){var u=l&~r;if(u!==N)a=yu(u);else{var o=l&i;o!==N&&(a=yu(o))}}else{var d=n&~r;d!==N?a=yu(d):i!==N&&(a=yu(i))}if(a===N)return N;if(t!==N&&t!==a&&(t&r)===N){var v=oi(a),b=oi(t);if(v>=b||v===ha&&(b&el)!==N)return t}(a&Va)!==N&&(a|=n&ha);var g=e.entangledLanes;if(g!==N)for(var R=e.entanglements,D=a&g;D>0;){var _=si(D),Y=1<<_;a|=R[_],D&=~Y}return a}function eR(e,t){for(var n=e.eventTimes,a=Ge;t>0;){var r=si(t),i=1<<r,l=n[r];l>a&&(a=l),t&=~i}return a}function tR(e,t){switch(e){case ce:case Zi:case Va:return t+250;case li:case ha:case vu:case pu:case Jf:case Zf:case ed:case td:case nd:case ad:case rd:case id:case ld:case ud:case od:case sd:case cd:case fd:case dd:return t+5e3;case tl:case vd:case pd:case hd:case md:return Ge;case hu:case mu:case ui:case Sn:return Ge;default:return f("Should have found matching lanes. This is a bug in React."),Ge}}function nR(e,t){for(var n=e.pendingLanes,a=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=n;l>0;){var u=si(l),o=1<<u,d=i[u];d===Ge?((o&a)===N||(o&r)!==N)&&(i[u]=tR(o,t)):d<=t&&(e.expiredLanes|=o),l&=~o}}function aR(e){return yu(e.pendingLanes)}function yd(e){var t=e.pendingLanes&~Sn;return t!==N?t:t&Sn?Sn:N}function rR(e){return(e&ce)!==N}function gd(e){return(e&Tm)!==N}function xm(e){return(e&vs)===e}function iR(e){var t=ce|Va|ha;return(e&t)===N}function lR(e){return(e&el)===e}function ys(e,t){var n=Zi|Va|li|ha;return(t&n)!==N}function uR(e,t){return(t&e.expiredLanes)!==N}function Dm(e){return(e&el)!==N}function wm(){var e=ps;return ps<<=1,(ps&el)===N&&(ps=pu),e}function oR(){var e=hs;return hs<<=1,(hs&vs)===N&&(hs=tl),e}function oi(e){return e&-e}function gu(e){return oi(e)}function si(e){return 31-Em(e)}function bd(e){return si(e)}function Cn(e,t){return(e&t)!==N}function nl(e,t){return(e&t)===t}function he(e,t){return e|t}function gs(e,t){return e&~t}function Om(e,t){return e&t}function KL(e){return e}function sR(e,t){return e!==xt&&e<t?e:t}function Sd(e){for(var t=[],n=0;n<Kf;n++)t.push(e);return t}function bu(e,t,n){e.pendingLanes|=t,t!==ui&&(e.suspendedLanes=N,e.pingedLanes=N);var a=e.eventTimes,r=bd(t);a[r]=n}function cR(e,t){e.suspendedLanes|=t,e.pingedLanes&=~t;for(var n=e.expirationTimes,a=t;a>0;){var r=si(a),i=1<<r;n[r]=Ge,a&=~i}}function _m(e,t,n){e.pingedLanes|=e.suspendedLanes&t}function fR(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=N,e.pingedLanes=N,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t;for(var a=e.entanglements,r=e.eventTimes,i=e.expirationTimes,l=n;l>0;){var u=si(l),o=1<<u;a[u]=N,r[u]=Ge,i[u]=Ge,l&=~o}}function Cd(e,t){for(var n=e.entangledLanes|=t,a=e.entanglements,r=n;r;){var i=si(r),l=1<<i;l&t|a[i]&t&&(a[i]|=t),r&=~l}}function dR(e,t){var n=oi(t),a;switch(n){case Va:a=Zi;break;case ha:a=li;break;case pu:case Jf:case Zf:case ed:case td:case nd:case ad:case rd:case id:case ld:case ud:case od:case sd:case cd:case fd:case dd:case tl:case vd:case pd:case hd:case md:a=vu;break;case ui:a=mu;break;default:a=xt;break}return(a&(e.suspendedLanes|t))!==xt?xt:a}function Lm(e,t,n){if(qn)for(var a=e.pendingUpdatersLaneMap;n>0;){var r=bd(n),i=1<<r,l=a[r];l.add(t),n&=~i}}function Mm(e,t){if(qn)for(var n=e.pendingUpdatersLaneMap,a=e.memoizedUpdaters;t>0;){var r=bd(t),i=1<<r,l=n[r];l.size>0&&(l.forEach(function(u){var o=u.alternate;(o===null||!a.has(o))&&a.add(u)}),l.clear()),t&=~i}}function Um(e,t){return null}var En=ce,Ba=Va,$a=ha,bs=ui,Su=xt;function Gn(){return Su}function Dt(e){Su=e}function vR(e,t){var n=Su;try{return Su=e,t()}finally{Su=n}}function pR(e,t){return e!==0&&e<t?e:t}function hR(e,t){return e>t?e:t}function Ed(e,t){return e!==0&&e<t}function Am(e){var t=oi(e);return Ed(En,t)?Ed(Ba,t)?gd(t)?$a:bs:Ba:En}function Ss(e){var t=e.current.memoizedState;return t.isDehydrated}var Nm;function mR(e){Nm=e}function yR(e){Nm(e)}var Rd;function gR(e){Rd=e}var km;function bR(e){km=e}var zm;function SR(e){zm=e}var Hm;function CR(e){Hm=e}var Td=!1,Cs=[],yr=null,gr=null,br=null,Cu=new Map,Eu=new Map,Sr=[],ER=["mousedown","mouseup","touchcancel","touchend","touchstart","auxclick","dblclick","pointercancel","pointerdown","pointerup","dragend","dragstart","drop","compositionend","compositionstart","keydown","keypress","keyup","input","textInput","copy","cut","paste","click","change","contextmenu","reset","submit"];function RR(e){return ER.indexOf(e)>-1}function TR(e,t,n,a,r){return{blockedOn:e,domEventName:t,eventSystemFlags:n,nativeEvent:r,targetContainers:[a]}}function Fm(e,t){switch(e){case"focusin":case"focusout":yr=null;break;case"dragenter":case"dragleave":gr=null;break;case"mouseover":case"mouseout":br=null;break;case"pointerover":case"pointerout":{var n=t.pointerId;Cu.delete(n);break}case"gotpointercapture":case"lostpointercapture":{var a=t.pointerId;Eu.delete(a);break}}}function Ru(e,t,n,a,r,i){if(e===null||e.nativeEvent!==i){var l=TR(t,n,a,r,i);if(t!==null){var u=Rr(t);u!==null&&Rd(u)}return l}e.eventSystemFlags|=a;var o=e.targetContainers;return r!==null&&o.indexOf(r)===-1&&o.push(r),e}function xR(e,t,n,a,r){switch(t){case"focusin":{var i=r;return yr=Ru(yr,e,t,n,a,i),!0}case"dragenter":{var l=r;return gr=Ru(gr,e,t,n,a,l),!0}case"mouseover":{var u=r;return br=Ru(br,e,t,n,a,u),!0}case"pointerover":{var o=r,d=o.pointerId;return Cu.set(d,Ru(Cu.get(d)||null,e,t,n,a,o)),!0}case"gotpointercapture":{var v=r,b=v.pointerId;return Eu.set(b,Ru(Eu.get(b)||null,e,t,n,a,v)),!0}}return!1}function jm(e){var t=di(e.target);if(t!==null){var n=ri(t);if(n!==null){var a=n.tag;if(a===ie){var r=sm(n);if(r!==null){e.blockedOn=r,Hm(e.priority,function(){km(n)});return}}else if(a===M){var i=n.stateNode;if(Ss(i)){e.blockedOn=cm(n);return}}}}e.blockedOn=null}function DR(e){for(var t=zm(),n={blockedOn:null,target:e,priority:t},a=0;a<Sr.length&&Ed(t,Sr[a].priority);a++);Sr.splice(a,0,n),a===0&&jm(n)}function Es(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;t.length>0;){var n=t[0],a=wd(e.domEventName,e.eventSystemFlags,n,e.nativeEvent);if(a===null){var r=e.nativeEvent,i=new r.constructor(r.type,r);JC(i),r.target.dispatchEvent(i),ZC()}else{var l=Rr(a);return l!==null&&Rd(l),e.blockedOn=a,!1}t.shift()}return!0}function Vm(e,t,n){Es(e)&&n.delete(t)}function wR(){Td=!1,yr!==null&&Es(yr)&&(yr=null),gr!==null&&Es(gr)&&(gr=null),br!==null&&Es(br)&&(br=null),Cu.forEach(Vm),Eu.forEach(Vm)}function Tu(e,t){e.blockedOn===t&&(e.blockedOn=null,Td||(Td=!0,p.unstable_scheduleCallback(p.unstable_NormalPriority,wR)))}function xu(e){if(Cs.length>0){Tu(Cs[0],e);for(var t=1;t<Cs.length;t++){var n=Cs[t];n.blockedOn===e&&(n.blockedOn=null)}}yr!==null&&Tu(yr,e),gr!==null&&Tu(gr,e),br!==null&&Tu(br,e);var a=function(u){return Tu(u,e)};Cu.forEach(a),Eu.forEach(a);for(var r=0;r<Sr.length;r++){var i=Sr[r];i.blockedOn===e&&(i.blockedOn=null)}for(;Sr.length>0;){var l=Sr[0];if(l.blockedOn!==null)break;jm(l),l.blockedOn===null&&Sr.shift()}}var al=m.ReactCurrentBatchConfig,xd=!0;function Bm(e){xd=!!e}function OR(){return xd}function _R(e,t,n){var a=$m(t),r;switch(a){case En:r=LR;break;case Ba:r=MR;break;case $a:default:r=Dd;break}return r.bind(null,t,n,e)}function LR(e,t,n,a){var r=Gn(),i=al.transition;al.transition=null;try{Dt(En),Dd(e,t,n,a)}finally{Dt(r),al.transition=i}}function MR(e,t,n,a){var r=Gn(),i=al.transition;al.transition=null;try{Dt(Ba),Dd(e,t,n,a)}finally{Dt(r),al.transition=i}}function Dd(e,t,n,a){xd&&UR(e,t,n,a)}function UR(e,t,n,a){var r=wd(e,t,n,a);if(r===null){Bd(e,t,a,Rs,n),Fm(e,a);return}if(xR(r,e,t,n,a)){a.stopPropagation();return}if(Fm(e,a),t&iu&&RR(e)){for(;r!==null;){var i=Rr(r);i!==null&&yR(i);var l=wd(e,t,n,a);if(l===null&&Bd(e,t,a,Rs,n),l===r)break;r=l}r!==null&&a.stopPropagation();return}Bd(e,t,a,null,n)}var Rs=null;function wd(e,t,n,a){Rs=null;var r=Nf(a),i=di(r);if(i!==null){var l=ri(i);if(l===null)i=null;else{var u=l.tag;if(u===ie){var o=sm(l);if(o!==null)return o;i=null}else if(u===M){var d=l.stateNode;if(Ss(d))return cm(l);i=null}else l!==i&&(i=null)}}return Rs=i,null}function $m(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return En;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return Ba;case"message":{var t=RE();switch(t){case ds:return En;case Wf:return Ba;case ii:case TE:return $a;case If:return bs;default:return $a}}default:return $a}}function AR(e,t,n){return e.addEventListener(t,n,!1),n}function NR(e,t,n){return e.addEventListener(t,n,!0),n}function kR(e,t,n,a){return e.addEventListener(t,n,{capture:!0,passive:a}),n}function zR(e,t,n,a){return e.addEventListener(t,n,{passive:a}),n}var Du=null,Od=null,wu=null;function HR(e){return Du=e,Od=Pm(),!0}function FR(){Du=null,Od=null,wu=null}function Ym(){if(wu)return wu;var e,t=Od,n=t.length,a,r=Pm(),i=r.length;for(e=0;e<n&&t[e]===r[e];e++);var l=n-e;for(a=1;a<=l&&t[n-a]===r[i-a];a++);var u=a>1?1-a:void 0;return wu=r.slice(e,u),wu}function Pm(){return"value"in Du?Du.value:Du.textContent}function Ts(e){var t,n=e.keyCode;return"charCode"in e?(t=e.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),t>=32||t===13?t:0}function xs(){return!0}function qm(){return!1}function Rn(e){function t(n,a,r,i,l){this._reactName=n,this._targetInst=r,this.type=a,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var u in e)if(e.hasOwnProperty(u)){var o=e[u];o?this[u]=o(i):this[u]=i[u]}var d=i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1;return d?this.isDefaultPrevented=xs:this.isDefaultPrevented=qm,this.isPropagationStopped=qm,this}return Se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xs)},persist:function(){},isPersistent:xs}),t}var rl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_d=Rn(rl),Ou=Se({},rl,{view:0,detail:0}),jR=Rn(Ou),Ld,Md,_u;function VR(e){e!==_u&&(_u&&e.type==="mousemove"?(Ld=e.screenX-_u.screenX,Md=e.screenY-_u.screenY):(Ld=0,Md=0),_u=e)}var Ds=Se({},Ou,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ad,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(VR(e),Ld)},movementY:function(e){return"movementY"in e?e.movementY:Md}}),Gm=Rn(Ds),BR=Se({},Ds,{dataTransfer:0}),$R=Rn(BR),YR=Se({},Ou,{relatedTarget:0}),Ud=Rn(YR),PR=Se({},rl,{animationName:0,elapsedTime:0,pseudoElement:0}),qR=Rn(PR),GR=Se({},rl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),QR=Rn(GR),WR=Se({},rl,{data:0}),Qm=Rn(WR),IR=Qm,XR={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},KR={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"};function JR(e){if(e.key){var t=XR[e.key]||e.key;if(t!=="Unidentified")return t}if(e.type==="keypress"){var n=Ts(e);return n===13?"Enter":String.fromCharCode(n)}return e.type==="keydown"||e.type==="keyup"?KR[e.keyCode]||"Unidentified":""}var ZR={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eT(e){var t=this,n=t.nativeEvent;if(n.getModifierState)return n.getModifierState(e);var a=ZR[e];return a?!!n[a]:!1}function Ad(e){return eT}var tT=Se({},Ou,{key:JR,code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ad,charCode:function(e){return e.type==="keypress"?Ts(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ts(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),nT=Rn(tT),aT=Se({},Ds,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wm=Rn(aT),rT=Se({},Ou,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ad}),iT=Rn(rT),lT=Se({},rl,{propertyName:0,elapsedTime:0,pseudoElement:0}),uT=Rn(lT),oT=Se({},Ds,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),sT=Rn(oT),cT=[9,13,27,32],Im=229,Nd=St&&"CompositionEvent"in window,Lu=null;St&&"documentMode"in document&&(Lu=document.documentMode);var fT=St&&"TextEvent"in window&&!Lu,Xm=St&&(!Nd||Lu&&Lu>8&&Lu<=11),Km=32,Jm=String.fromCharCode(Km);function dT(){Yn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Yn("onCompositionEnd",["compositionend","focusout","keydown","keypress","keyup","mousedown"]),Yn("onCompositionStart",["compositionstart","focusout","keydown","keypress","keyup","mousedown"]),Yn("onCompositionUpdate",["compositionupdate","focusout","keydown","keypress","keyup","mousedown"])}var Zm=!1;function vT(e){return(e.ctrlKey||e.altKey||e.metaKey)&&!(e.ctrlKey&&e.altKey)}function pT(e){switch(e){case"compositionstart":return"onCompositionStart";case"compositionend":return"onCompositionEnd";case"compositionupdate":return"onCompositionUpdate"}}function hT(e,t){return e==="keydown"&&t.keyCode===Im}function ey(e,t){switch(e){case"keyup":return cT.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==Im;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ty(e){var t=e.detail;return typeof t=="object"&&"data"in t?t.data:null}function ny(e){return e.locale==="ko"}var il=!1;function mT(e,t,n,a,r){var i,l;if(Nd?i=pT(t):il?ey(t,a)&&(i="onCompositionEnd"):hT(t,a)&&(i="onCompositionStart"),!i)return null;Xm&&!ny(a)&&(!il&&i==="onCompositionStart"?il=HR(r):i==="onCompositionEnd"&&il&&(l=Ym()));var u=Ms(n,i);if(u.length>0){var o=new Qm(i,t,null,a,r);if(e.push({event:o,listeners:u}),l)o.data=l;else{var d=ty(a);d!==null&&(o.data=d)}}}function yT(e,t){switch(e){case"compositionend":return ty(t);case"keypress":var n=t.which;return n!==Km?null:(Zm=!0,Jm);case"textInput":var a=t.data;return a===Jm&&Zm?null:a;default:return null}}function gT(e,t){if(il){if(e==="compositionend"||!Nd&&ey(e,t)){var n=Ym();return FR(),il=!1,n}return null}switch(e){case"paste":return null;case"keypress":if(!vT(t)){if(t.char&&t.char.length>1)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xm&&!ny(t)?null:t.data;default:return null}}function bT(e,t,n,a,r){var i;if(fT?i=yT(t,a):i=gT(t,a),!i)return null;var l=Ms(n,"onBeforeInput");if(l.length>0){var u=new IR("onBeforeInput","beforeinput",null,a,r);e.push({event:u,listeners:l}),u.data=i}}function ST(e,t,n,a,r,i,l){mT(e,t,n,a,r),bT(e,t,n,a,r)}var CT={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ay(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!CT[e.type]:t==="textarea"}/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function ET(e){if(!St)return!1;var t="on"+e,n=t in document;if(!n){var a=document.createElement("div");a.setAttribute(t,"return;"),n=typeof a[t]=="function"}return n}function RT(){Yn("onChange",["change","click","focusin","focusout","input","keydown","keyup","selectionchange"])}function ry(e,t,n,a){tm(a);var r=Ms(t,"onChange");if(r.length>0){var i=new _d("onChange","change",null,n,a);e.push({event:i,listeners:r})}}var Mu=null,Uu=null;function TT(e){var t=e.nodeName&&e.nodeName.toLowerCase();return t==="select"||t==="input"&&e.type==="file"}function xT(e){var t=[];ry(t,Uu,e,Nf(e)),im(DT,t)}function DT(e){Ey(e,0)}function ws(e){var t=fl(e);if(Vi(t))return e}function wT(e,t){if(e==="change")return t}var iy=!1;St&&(iy=ET("input")&&(!document.documentMode||document.documentMode>9));function OT(e,t){Mu=e,Uu=t,Mu.attachEvent("onpropertychange",uy)}function ly(){Mu&&(Mu.detachEvent("onpropertychange",uy),Mu=null,Uu=null)}function uy(e){e.propertyName==="value"&&ws(Uu)&&xT(e)}function _T(e,t,n){e==="focusin"?(ly(),OT(t,n)):e==="focusout"&&ly()}function LT(e,t){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ws(Uu)}function MT(e){var t=e.nodeName;return t&&t.toLowerCase()==="input"&&(e.type==="checkbox"||e.type==="radio")}function UT(e,t){if(e==="click")return ws(t)}function AT(e,t){if(e==="input"||e==="change")return ws(t)}function NT(e){var t=e._wrapperState;!t||!t.controlled||e.type!=="number"||se(e,"number",e.value)}function kT(e,t,n,a,r,i,l){var u=n?fl(n):window,o,d;if(TT(u)?o=wT:ay(u)?iy?o=AT:(o=LT,d=_T):MT(u)&&(o=UT),o){var v=o(t,n);if(v){ry(e,v,a,r);return}}d&&d(t,u,n),t==="focusout"&&NT(u)}function zT(){la("onMouseEnter",["mouseout","mouseover"]),la("onMouseLeave",["mouseout","mouseover"]),la("onPointerEnter",["pointerout","pointerover"]),la("onPointerLeave",["pointerout","pointerover"])}function HT(e,t,n,a,r,i,l){var u=t==="mouseover"||t==="pointerover",o=t==="mouseout"||t==="pointerout";if(u&&!eE(a)){var d=a.relatedTarget||a.fromElement;if(d&&(di(d)||Qu(d)))return}if(!(!o&&!u)){var v;if(r.window===r)v=r;else{var b=r.ownerDocument;b?v=b.defaultView||b.parentWindow:v=window}var g,R;if(o){var D=a.relatedTarget||a.toElement;if(g=n,R=D?di(D):null,R!==null){var _=ri(R);(R!==_||R.tag!==B&&R.tag!==J)&&(R=null)}}else g=null,R=n;if(g!==R){var Y=Gm,te="onMouseLeave",ee="onMouseEnter",xe="mouse";(t==="pointerout"||t==="pointerover")&&(Y=Wm,te="onPointerLeave",ee="onPointerEnter",xe="pointer");var ge=g==null?v:fl(g),C=R==null?v:fl(R),L=new Y(te,xe+"leave",g,a,r);L.target=ge,L.relatedTarget=C;var E=null,z=di(r);if(z===n){var W=new Y(ee,xe+"enter",R,a,r);W.target=C,W.relatedTarget=ge,E=W}ux(e,L,E,g,R)}}}function FT(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tn=typeof Object.is=="function"?Object.is:FT;function Au(e,t){if(Tn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(var r=0;r<n.length;r++){var i=n[r];if(!rn.call(t,i)||!Tn(e[i],t[i]))return!1}return!0}function oy(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function jT(e){for(;e;){if(e.nextSibling)return e.nextSibling;e=e.parentNode}}function sy(e,t){for(var n=oy(e),a=0,r=0;n;){if(n.nodeType===Na){if(r=a+n.textContent.length,a<=t&&r>=t)return{node:n,offset:t-a};a=r}n=oy(jT(n))}}function VT(e){var t=e.ownerDocument,n=t&&t.defaultView||window,a=n.getSelection&&n.getSelection();if(!a||a.rangeCount===0)return null;var r=a.anchorNode,i=a.anchorOffset,l=a.focusNode,u=a.focusOffset;try{r.nodeType,l.nodeType}catch{return null}return BT(e,r,i,l,u)}function BT(e,t,n,a,r){var i=0,l=-1,u=-1,o=0,d=0,v=e,b=null;e:for(;;){for(var g=null;v===t&&(n===0||v.nodeType===Na)&&(l=i+n),v===a&&(r===0||v.nodeType===Na)&&(u=i+r),v.nodeType===Na&&(i+=v.nodeValue.length),(g=v.firstChild)!==null;)b=v,v=g;for(;;){if(v===e)break e;if(b===t&&++o===n&&(l=i),b===a&&++d===r&&(u=i),(g=v.nextSibling)!==null)break;v=b,b=v.parentNode}v=g}return l===-1||u===-1?null:{start:l,end:u}}function $T(e,t){var n=e.ownerDocument||document,a=n&&n.defaultView||window;if(a.getSelection){var r=a.getSelection(),i=e.textContent.length,l=Math.min(t.start,i),u=t.end===void 0?l:Math.min(t.end,i);if(!r.extend&&l>u){var o=u;u=l,l=o}var d=sy(e,l),v=sy(e,u);if(d&&v){if(r.rangeCount===1&&r.anchorNode===d.node&&r.anchorOffset===d.offset&&r.focusNode===v.node&&r.focusOffset===v.offset)return;var b=n.createRange();b.setStart(d.node,d.offset),r.removeAllRanges(),l>u?(r.addRange(b),r.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),r.addRange(b))}}}function cy(e){return e&&e.nodeType===Na}function fy(e,t){return!e||!t?!1:e===t?!0:cy(e)?!1:cy(t)?fy(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1}function YT(e){return e&&e.ownerDocument&&fy(e.ownerDocument.documentElement,e)}function PT(e){try{return typeof e.contentWindow.location.href=="string"}catch{return!1}}function dy(){for(var e=window,t=pr();t instanceof e.HTMLIFrameElement;){if(PT(t))e=t.contentWindow;else return t;t=pr(e.document)}return t}function kd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function qT(){var e=dy();return{focusedElem:e,selectionRange:kd(e)?QT(e):null}}function GT(e){var t=dy(),n=e.focusedElem,a=e.selectionRange;if(t!==n&&YT(n)){a!==null&&kd(n)&&WT(n,a);for(var r=[],i=n;i=i.parentNode;)i.nodeType===un&&r.push({element:i,left:i.scrollLeft,top:i.scrollTop});typeof n.focus=="function"&&n.focus();for(var l=0;l<r.length;l++){var u=r[l];u.element.scrollLeft=u.left,u.element.scrollTop=u.top}}}function QT(e){var t;return"selectionStart"in e?t={start:e.selectionStart,end:e.selectionEnd}:t=VT(e),t||{start:0,end:0}}function WT(e,t){var n=t.start,a=t.end;a===void 0&&(a=n),"selectionStart"in e?(e.selectionStart=n,e.selectionEnd=Math.min(a,e.value.length)):$T(e,t)}var IT=St&&"documentMode"in document&&document.documentMode<=11;function XT(){Yn("onSelect",["focusout","contextmenu","dragend","focusin","keydown","keyup","mousedown","mouseup","selectionchange"])}var ll=null,zd=null,Nu=null,Hd=!1;function KT(e){if("selectionStart"in e&&kd(e))return{start:e.selectionStart,end:e.selectionEnd};var t=e.ownerDocument&&e.ownerDocument.defaultView||window,n=t.getSelection();return{anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}}function JT(e){return e.window===e?e.document:e.nodeType===ka?e:e.ownerDocument}function vy(e,t,n){var a=JT(n);if(!(Hd||ll==null||ll!==pr(a))){var r=KT(ll);if(!Nu||!Au(Nu,r)){Nu=r;var i=Ms(zd,"onSelect");if(i.length>0){var l=new _d("onSelect","select",null,t,n);e.push({event:l,listeners:i}),l.target=ll}}}}function ZT(e,t,n,a,r,i,l){var u=n?fl(n):window;switch(t){case"focusin":(ay(u)||u.contentEditable==="true")&&(ll=u,zd=n,Nu=null);break;case"focusout":ll=null,zd=null,Nu=null;break;case"mousedown":Hd=!0;break;case"contextmenu":case"mouseup":case"dragend":Hd=!1,vy(e,a,r);break;case"selectionchange":if(IT)break;case"keydown":case"keyup":vy(e,a,r)}}function Os(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ul={animationend:Os("Animation","AnimationEnd"),animationiteration:Os("Animation","AnimationIteration"),animationstart:Os("Animation","AnimationStart"),transitionend:Os("Transition","TransitionEnd")},Fd={},py={};St&&(py=document.createElement("div").style,"AnimationEvent"in window||(delete ul.animationend.animation,delete ul.animationiteration.animation,delete ul.animationstart.animation),"TransitionEvent"in window||delete ul.transitionend.transition);function _s(e){if(Fd[e])return Fd[e];if(!ul[e])return e;var t=ul[e];for(var n in t)if(t.hasOwnProperty(n)&&n in py)return Fd[e]=t[n];return e}var hy=_s("animationend"),my=_s("animationiteration"),yy=_s("animationstart"),gy=_s("transitionend"),by=new Map,Sy=["abort","auxClick","cancel","canPlay","canPlayThrough","click","close","contextMenu","copy","cut","drag","dragEnd","dragEnter","dragExit","dragLeave","dragOver","dragStart","drop","durationChange","emptied","encrypted","ended","error","gotPointerCapture","input","invalid","keyDown","keyPress","keyUp","load","loadedData","loadedMetadata","loadStart","lostPointerCapture","mouseDown","mouseMove","mouseOut","mouseOver","mouseUp","paste","pause","play","playing","pointerCancel","pointerDown","pointerMove","pointerOut","pointerOver","pointerUp","progress","rateChange","reset","resize","seeked","seeking","stalled","submit","suspend","timeUpdate","touchCancel","touchEnd","touchStart","volumeChange","scroll","toggle","touchMove","waiting","wheel"];function Cr(e,t){by.set(e,t),Yn(t,[e])}function ex(){for(var e=0;e<Sy.length;e++){var t=Sy[e],n=t.toLowerCase(),a=t[0].toUpperCase()+t.slice(1);Cr(n,"on"+a)}Cr(hy,"onAnimationEnd"),Cr(my,"onAnimationIteration"),Cr(yy,"onAnimationStart"),Cr("dblclick","onDoubleClick"),Cr("focusin","onFocus"),Cr("focusout","onBlur"),Cr(gy,"onTransitionEnd")}function tx(e,t,n,a,r,i,l){var u=by.get(t);if(u!==void 0){var o=_d,d=t;switch(t){case"keypress":if(Ts(a)===0)return;case"keydown":case"keyup":o=nT;break;case"focusin":d="focus",o=Ud;break;case"focusout":d="blur",o=Ud;break;case"beforeblur":case"afterblur":o=Ud;break;case"click":if(a.button===2)return;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=Gm;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=$R;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=iT;break;case hy:case my:case yy:o=qR;break;case gy:o=uT;break;case"scroll":o=jR;break;case"wheel":o=sT;break;case"copy":case"cut":case"paste":o=QR;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=Wm;break}var v=(i&iu)!==0;{var b=!v&&t==="scroll",g=ix(n,u,a.type,v,b);if(g.length>0){var R=new o(u,d,null,a,r);e.push({event:R,listeners:g})}}}}ex(),zT(),RT(),XT(),dT();function nx(e,t,n,a,r,i,l){tx(e,t,n,a,r,i);var u=(i&KC)===0;u&&(HT(e,t,n,a,r),kT(e,t,n,a,r),ZT(e,t,n,a,r),ST(e,t,n,a,r))}var ku=["abort","canplay","canplaythrough","durationchange","emptied","encrypted","ended","error","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","resize","seeked","seeking","stalled","suspend","timeupdate","volumechange","waiting"],jd=new Set(["cancel","close","invalid","load","scroll","toggle"].concat(ku));function Cy(e,t,n){var a=e.type||"unknown-event";e.currentTarget=n,sE(a,t,void 0,e),e.currentTarget=null}function ax(e,t,n){var a;if(n)for(var r=t.length-1;r>=0;r--){var i=t[r],l=i.instance,u=i.currentTarget,o=i.listener;if(l!==a&&e.isPropagationStopped())return;Cy(e,o,u),a=l}else for(var d=0;d<t.length;d++){var v=t[d],b=v.instance,g=v.currentTarget,R=v.listener;if(b!==a&&e.isPropagationStopped())return;Cy(e,R,g),a=b}}function Ey(e,t){for(var n=(t&iu)!==0,a=0;a<e.length;a++){var r=e[a],i=r.event,l=r.listeners;ax(i,l,n)}cE()}function rx(e,t,n,a,r){var i=Nf(n),l=[];nx(l,e,a,n,i,t),Ey(l,t)}function Ie(e,t){jd.has(e)||f('Did not expect a listenToNonDelegatedEvent() call for "%s". This is a bug in React. Please file an issue.',e);var n=!1,a=ND(t),r=ox(e);a.has(r)||(Ry(t,e,Af,n),a.add(r))}function Vd(e,t,n){jd.has(e)&&!t&&f('Did not expect a listenToNativeEvent() call for "%s" in the bubble phase. This is a bug in React. Please file an issue.',e);var a=0;t&&(a|=iu),Ry(n,e,a,t)}var Ls="_reactListening"+Math.random().toString(36).slice(2);function zu(e){if(!e[Ls]){e[Ls]=!0,qr.forEach(function(n){n!=="selectionchange"&&(jd.has(n)||Vd(n,!1,e),Vd(n,!0,e))});var t=e.nodeType===ka?e:e.ownerDocument;t!==null&&(t[Ls]||(t[Ls]=!0,Vd("selectionchange",!1,t)))}}function Ry(e,t,n,a,r){var i=_R(e,t,n),l=void 0;Hf&&(t==="touchstart"||t==="touchmove"||t==="wheel")&&(l=!0),e=e,a?l!==void 0?kR(e,t,i,l):NR(e,t,i):l!==void 0?zR(e,t,i,l):AR(e,t,i)}function Ty(e,t){return e===t||e.nodeType===ut&&e.parentNode===t}function Bd(e,t,n,a,r){var i=a;if(!(t&Zh)&&!(t&Af)){var l=r;if(a!==null){var u=a;e:for(;;){if(u===null)return;var o=u.tag;if(o===M||o===F){var d=u.stateNode.containerInfo;if(Ty(d,l))break;if(o===F)for(var v=u.return;v!==null;){var b=v.tag;if(b===M||b===F){var g=v.stateNode.containerInfo;if(Ty(g,l))return}v=v.return}for(;d!==null;){var R=di(d);if(R===null)return;var D=R.tag;if(D===B||D===J){u=i=R;continue e}d=d.parentNode}}u=u.return}}}im(function(){return rx(e,t,n,i)})}function Hu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ix(e,t,n,a,r,i){for(var l=t!==null?t+"Capture":null,u=a?l:t,o=[],d=e,v=null;d!==null;){var b=d,g=b.stateNode,R=b.tag;if(R===B&&g!==null&&(v=g,u!==null)){var D=uu(d,u);D!=null&&o.push(Hu(d,D,v))}if(r)break;d=d.return}return o}function Ms(e,t){for(var n=t+"Capture",a=[],r=e;r!==null;){var i=r,l=i.stateNode,u=i.tag;if(u===B&&l!==null){var o=l,d=uu(r,n);d!=null&&a.unshift(Hu(r,d,o));var v=uu(r,t);v!=null&&a.push(Hu(r,v,o))}r=r.return}return a}function ol(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==B);return e||null}function lx(e,t){for(var n=e,a=t,r=0,i=n;i;i=ol(i))r++;for(var l=0,u=a;u;u=ol(u))l++;for(;r-l>0;)n=ol(n),r--;for(;l-r>0;)a=ol(a),l--;for(var o=r;o--;){if(n===a||a!==null&&n===a.alternate)return n;n=ol(n),a=ol(a)}return null}function xy(e,t,n,a,r){for(var i=t._reactName,l=[],u=n;u!==null&&u!==a;){var o=u,d=o.alternate,v=o.stateNode,b=o.tag;if(d!==null&&d===a)break;if(b===B&&v!==null){var g=v;if(r){var R=uu(u,i);R!=null&&l.unshift(Hu(u,R,g))}else if(!r){var D=uu(u,i);D!=null&&l.push(Hu(u,D,g))}}u=u.return}l.length!==0&&e.push({event:t,listeners:l})}function ux(e,t,n,a,r){var i=a&&r?lx(a,r):null;a!==null&&xy(e,t,a,i,!1),r!==null&&n!==null&&xy(e,n,r,i,!0)}function ox(e,t){return e+"__bubble"}var on=!1,Fu="dangerouslySetInnerHTML",Us="suppressContentEditableWarning",Er="suppressHydrationWarning",Dy="autoFocus",ci="children",fi="style",As="__html",$d,Ns,ju,wy,ks,Oy,_y;$d={dialog:!0,webview:!0},Ns=function(e,t){PC(e,t),qC(e,t),XC(e,t,{registrationNameDependencies:pn,possibleRegistrationNames:wa})},Oy=St&&!document.documentMode,ju=function(e,t,n){if(!on){var a=zs(n),r=zs(t);r!==a&&(on=!0,f("Prop `%s` did not match. Server: %s Client: %s",e,JSON.stringify(r),JSON.stringify(a)))}},wy=function(e){if(!on){on=!0;var t=[];e.forEach(function(n){t.push(n)}),f("Extra attributes from the server: %s",t)}},ks=function(e,t){t===!1?f("Expected `%s` listener to be a function, instead got `false`.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.",e,e,e):f("Expected `%s` listener to be a function, instead got a value of `%s` type.",e,typeof t)},_y=function(e,t){var n=e.namespaceURI===Aa?e.ownerDocument.createElement(e.tagName):e.ownerDocument.createElementNS(e.namespaceURI,e.tagName);return n.innerHTML=t,n.innerHTML};var sx=/\r\n?/g,cx=/\u0000|\uFFFD/g;function zs(e){rr(e);var t=typeof e=="string"?e:""+e;return t.replace(sx,`
`).replace(cx,"")}function Hs(e,t,n,a){var r=zs(t),i=zs(e);if(i!==r&&(a&&(on||(on=!0,f('Text content did not match. Server: "%s" Client: "%s"',i,r))),n&&me))throw new Error("Text content does not match server-rendered HTML.")}function Ly(e){return e.nodeType===ka?e:e.ownerDocument}function fx(){}function Fs(e){e.onclick=fx}function dx(e,t,n,a,r){for(var i in a)if(a.hasOwnProperty(i)){var l=a[i];if(i===fi)l&&Object.freeze(l),Qh(t,l);else if(i===Fu){var u=l?l[As]:void 0;u!=null&&$h(t,u)}else if(i===ci)if(typeof l=="string"){var o=e!=="textarea"||l!=="";o&&ls(t,l)}else typeof l=="number"&&ls(t,""+l);else i===Us||i===Er||i===Dy||(pn.hasOwnProperty(i)?l!=null&&(typeof l!="function"&&ks(i,l),i==="onScroll"&&Ie("scroll",t)):l!=null&&Ui(t,i,l,r))}}function vx(e,t,n,a){for(var r=0;r<t.length;r+=2){var i=t[r],l=t[r+1];i===fi?Qh(e,l):i===Fu?$h(e,l):i===ci?ls(e,l):Ui(e,i,l,a)}}function px(e,t,n,a){var r,i=Ly(n),l,u=a;if(u===Aa&&(u=wf(e)),u===Aa){if(r=Zr(e,t),!r&&e!==e.toLowerCase()&&f("<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.",e),e==="script"){var o=i.createElement("div");o.innerHTML="<script><\/script>";var d=o.firstChild;l=o.removeChild(d)}else if(typeof t.is=="string")l=i.createElement(e,{is:t.is});else if(l=i.createElement(e),e==="select"){var v=l;t.multiple?v.multiple=!0:t.size&&(v.size=t.size)}}else l=i.createElementNS(u,e);return u===Aa&&!r&&Object.prototype.toString.call(l)==="[object HTMLUnknownElement]"&&!rn.call($d,e)&&($d[e]=!0,f("The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.",e)),l}function hx(e,t){return Ly(t).createTextNode(e)}function mx(e,t,n,a){var r=Zr(t,n);Ns(t,n);var i;switch(t){case"dialog":Ie("cancel",e),Ie("close",e),i=n;break;case"iframe":case"object":case"embed":Ie("load",e),i=n;break;case"video":case"audio":for(var l=0;l<ku.length;l++)Ie(ku[l],e);i=n;break;case"source":Ie("error",e),i=n;break;case"img":case"image":case"link":Ie("error",e),Ie("load",e),i=n;break;case"details":Ie("toggle",e),i=n;break;case"input":h(e,n),i=s(e,n),Ie("invalid",e);break;case"option":Ye(e,n),i=n;break;case"select":au(e,n),i=nu(e,n),Ie("invalid",e);break;case"textarea":jh(e,n),i=xf(e,n),Ie("invalid",e);break;default:i=n}switch(Uf(t,i),dx(t,e,a,i,r),t){case"input":Ma(e),j(e,n,!1);break;case"textarea":Ma(e),Bh(e);break;case"option":We(e,n);break;case"select":Tf(e,n);break;default:typeof i.onClick=="function"&&Fs(e);break}}function yx(e,t,n,a,r){Ns(t,a);var i=null,l,u;switch(t){case"input":l=s(e,n),u=s(e,a),i=[];break;case"select":l=nu(e,n),u=nu(e,a),i=[];break;case"textarea":l=xf(e,n),u=xf(e,a),i=[];break;default:l=n,u=a,typeof l.onClick!="function"&&typeof u.onClick=="function"&&Fs(e);break}Uf(t,u);var o,d,v=null;for(o in l)if(!(u.hasOwnProperty(o)||!l.hasOwnProperty(o)||l[o]==null))if(o===fi){var b=l[o];for(d in b)b.hasOwnProperty(d)&&(v||(v={}),v[d]="")}else o===Fu||o===ci||o===Us||o===Er||o===Dy||(pn.hasOwnProperty(o)?i||(i=[]):(i=i||[]).push(o,null));for(o in u){var g=u[o],R=l!=null?l[o]:void 0;if(!(!u.hasOwnProperty(o)||g===R||g==null&&R==null))if(o===fi)if(g&&Object.freeze(g),R){for(d in R)R.hasOwnProperty(d)&&(!g||!g.hasOwnProperty(d))&&(v||(v={}),v[d]="");for(d in g)g.hasOwnProperty(d)&&R[d]!==g[d]&&(v||(v={}),v[d]=g[d])}else v||(i||(i=[]),i.push(o,v)),v=g;else if(o===Fu){var D=g?g[As]:void 0,_=R?R[As]:void 0;D!=null&&_!==D&&(i=i||[]).push(o,D)}else o===ci?(typeof g=="string"||typeof g=="number")&&(i=i||[]).push(o,""+g):o===Us||o===Er||(pn.hasOwnProperty(o)?(g!=null&&(typeof g!="function"&&ks(o,g),o==="onScroll"&&Ie("scroll",e)),!i&&R!==g&&(i=[])):(i=i||[]).push(o,g))}return v&&(zC(v,u[fi]),(i=i||[]).push(fi,v)),i}function gx(e,t,n,a,r){n==="input"&&r.type==="radio"&&r.name!=null&&x(e,r);var i=Zr(n,a),l=Zr(n,r);switch(vx(e,t,i,l),n){case"input":O(e,r);break;case"textarea":Vh(e,r);break;case"select":vC(e,r);break}}function bx(e){{var t=e.toLowerCase();return us.hasOwnProperty(t)&&us[t]||null}}function Sx(e,t,n,a,r,i,l){var u,o;switch(u=Zr(t,n),Ns(t,n),t){case"dialog":Ie("cancel",e),Ie("close",e);break;case"iframe":case"object":case"embed":Ie("load",e);break;case"video":case"audio":for(var d=0;d<ku.length;d++)Ie(ku[d],e);break;case"source":Ie("error",e);break;case"img":case"image":case"link":Ie("error",e),Ie("load",e);break;case"details":Ie("toggle",e);break;case"input":h(e,n),Ie("invalid",e);break;case"option":Ye(e,n);break;case"select":au(e,n),Ie("invalid",e);break;case"textarea":jh(e,n),Ie("invalid",e);break}Uf(t,n);{o=new Set;for(var v=e.attributes,b=0;b<v.length;b++){var g=v[b].name.toLowerCase();switch(g){case"value":break;case"checked":break;case"selected":break;default:o.add(v[b].name)}}}var R=null;for(var D in n)if(n.hasOwnProperty(D)){var _=n[D];if(D===ci)typeof _=="string"?e.textContent!==_&&(n[Er]!==!0&&Hs(e.textContent,_,i,l),R=[ci,_]):typeof _=="number"&&e.textContent!==""+_&&(n[Er]!==!0&&Hs(e.textContent,_,i,l),R=[ci,""+_]);else if(pn.hasOwnProperty(D))_!=null&&(typeof _!="function"&&ks(D,_),D==="onScroll"&&Ie("scroll",e));else if(l&&typeof u=="boolean"){var Y=void 0,te=u&&Bn?null:fa(D);if(n[Er]!==!0){if(!(D===Us||D===Er||D==="value"||D==="checked"||D==="selected")){if(D===Fu){var ee=e.innerHTML,xe=_?_[As]:void 0;if(xe!=null){var ge=_y(e,xe);ge!==ee&&ju(D,ee,ge)}}else if(D===fi){if(o.delete(D),Oy){var C=NC(_);Y=e.getAttribute("style"),C!==Y&&ju(D,Y,C)}}else if(u&&!Bn)o.delete(D.toLowerCase()),Y=Mi(e,D,_),_!==Y&&ju(D,Y,_);else if(!ct(D,te,u)&&!Ke(D,_,te,u)){var L=!1;if(te!==null)o.delete(te.attributeName),Y=Qr(e,D,_,te);else{var E=a;if(E===Aa&&(E=wf(t)),E===Aa)o.delete(D.toLowerCase());else{var z=bx(D);z!==null&&z!==D&&(L=!0,o.delete(z)),o.delete(D)}Y=Mi(e,D,_)}var W=Bn;!W&&_!==Y&&!L&&ju(D,Y,_)}}}}}switch(l&&o.size>0&&n[Er]!==!0&&wy(o),t){case"input":Ma(e),j(e,n,!0);break;case"textarea":Ma(e),Bh(e);break;case"select":case"option":break;default:typeof n.onClick=="function"&&Fs(e);break}return R}function Cx(e,t,n){var a=e.nodeValue!==t;return a}function Yd(e,t){{if(on)return;on=!0,f("Did not expect server HTML to contain a <%s> in <%s>.",t.nodeName.toLowerCase(),e.nodeName.toLowerCase())}}function Pd(e,t){{if(on)return;on=!0,f('Did not expect server HTML to contain the text node "%s" in <%s>.',t.nodeValue,e.nodeName.toLowerCase())}}function qd(e,t,n){{if(on)return;on=!0,f("Expected server HTML to contain a matching <%s> in <%s>.",t,e.nodeName.toLowerCase())}}function Gd(e,t){{if(t===""||on)return;on=!0,f('Expected server HTML to contain a matching text node for "%s" in <%s>.',t,e.nodeName.toLowerCase())}}function Ex(e,t,n){switch(t){case"input":ue(e,n);return;case"textarea":hC(e,n);return;case"select":pC(e,n);return}}var Vu=function(){},Bu=function(){};{var Rx=["address","applet","area","article","aside","base","basefont","bgsound","blockquote","body","br","button","caption","center","col","colgroup","dd","details","dir","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","img","input","isindex","li","link","listing","main","marquee","menu","menuitem","meta","nav","noembed","noframes","noscript","object","ol","p","param","plaintext","pre","script","section","select","source","style","summary","table","tbody","td","template","textarea","tfoot","th","thead","title","tr","track","ul","wbr","xmp"],My=["applet","caption","html","table","td","th","marquee","object","template","foreignObject","desc","title"],Tx=My.concat(["button"]),xx=["dd","dt","li","option","optgroup","p","rp","rt"],Uy={current:null,formTag:null,aTagInScope:null,buttonTagInScope:null,nobrTagInScope:null,pTagInButtonScope:null,listItemTagAutoclosing:null,dlItemTagAutoclosing:null};Bu=function(e,t){var n=Se({},e||Uy),a={tag:t};return My.indexOf(t)!==-1&&(n.aTagInScope=null,n.buttonTagInScope=null,n.nobrTagInScope=null),Tx.indexOf(t)!==-1&&(n.pTagInButtonScope=null),Rx.indexOf(t)!==-1&&t!=="address"&&t!=="div"&&t!=="p"&&(n.listItemTagAutoclosing=null,n.dlItemTagAutoclosing=null),n.current=a,t==="form"&&(n.formTag=a),t==="a"&&(n.aTagInScope=a),t==="button"&&(n.buttonTagInScope=a),t==="nobr"&&(n.nobrTagInScope=a),t==="p"&&(n.pTagInButtonScope=a),t==="li"&&(n.listItemTagAutoclosing=a),(t==="dd"||t==="dt")&&(n.dlItemTagAutoclosing=a),n};var Dx=function(e,t){switch(t){case"select":return e==="option"||e==="optgroup"||e==="#text";case"optgroup":return e==="option"||e==="#text";case"option":return e==="#text";case"tr":return e==="th"||e==="td"||e==="style"||e==="script"||e==="template";case"tbody":case"thead":case"tfoot":return e==="tr"||e==="style"||e==="script"||e==="template";case"colgroup":return e==="col"||e==="template";case"table":return e==="caption"||e==="colgroup"||e==="tbody"||e==="tfoot"||e==="thead"||e==="style"||e==="script"||e==="template";case"head":return e==="base"||e==="basefont"||e==="bgsound"||e==="link"||e==="meta"||e==="title"||e==="noscript"||e==="noframes"||e==="style"||e==="script"||e==="template";case"html":return e==="head"||e==="body"||e==="frameset";case"frameset":return e==="frame";case"#document":return e==="html"}switch(e){case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t!=="h1"&&t!=="h2"&&t!=="h3"&&t!=="h4"&&t!=="h5"&&t!=="h6";case"rp":case"rt":return xx.indexOf(t)===-1;case"body":case"caption":case"col":case"colgroup":case"frameset":case"frame":case"head":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return t==null}return!0},wx=function(e,t){switch(e){case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"menu":case"nav":case"ol":case"p":case"section":case"summary":case"ul":case"pre":case"listing":case"table":case"hr":case"xmp":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t.pTagInButtonScope;case"form":return t.formTag||t.pTagInButtonScope;case"li":return t.listItemTagAutoclosing;case"dd":case"dt":return t.dlItemTagAutoclosing;case"button":return t.buttonTagInScope;case"a":return t.aTagInScope;case"nobr":return t.nobrTagInScope}return null},Ay={};Vu=function(e,t,n){n=n||Uy;var a=n.current,r=a&&a.tag;t!=null&&(e!=null&&f("validateDOMNesting: when childText is passed, childTag should be null"),e="#text");var i=Dx(e,r)?null:a,l=i?null:wx(e,n),u=i||l;if(u){var o=u.tag,d=!!i+"|"+e+"|"+o;if(!Ay[d]){Ay[d]=!0;var v=e,b="";if(e==="#text"?/\S/.test(t)?v="Text nodes":(v="Whitespace text nodes",b=" Make sure you don't have any extra whitespace between tags on each line of your source code."):v="<"+e+">",i){var g="";o==="table"&&e==="tr"&&(g+=" Add a <tbody>, <thead> or <tfoot> to your code to match the DOM tree generated by the browser."),f("validateDOMNesting(...): %s cannot appear as a child of <%s>.%s%s",v,o,b,g)}else f("validateDOMNesting(...): %s cannot appear as a descendant of <%s>.",v,o)}}}}var js="suppressHydrationWarning",Vs="$",Bs="/$",$u="$?",Yu="$!",Ox="style",Qd=null,Wd=null;function _x(e){var t,n,a=e.nodeType;switch(a){case ka:case _f:{t=a===ka?"#document":"#fragment";var r=e.documentElement;n=r?r.namespaceURI:Of(null,"");break}default:{var i=a===ut?e.parentNode:e,l=i.namespaceURI||null;t=i.tagName,n=Of(l,t);break}}{var u=t.toLowerCase(),o=Bu(null,u);return{namespace:n,ancestorInfo:o}}}function Lx(e,t,n){{var a=e,r=Of(a.namespace,t),i=Bu(a.ancestorInfo,t);return{namespace:r,ancestorInfo:i}}}function JL(e){return e}function Mx(e){Qd=OR(),Wd=qT();var t=null;return Bm(!1),t}function Ux(e){GT(Wd),Bm(Qd),Qd=null,Wd=null}function Ax(e,t,n,a,r){var i;{var l=a;if(Vu(e,null,l.ancestorInfo),typeof t.children=="string"||typeof t.children=="number"){var u=""+t.children,o=Bu(l.ancestorInfo,e);Vu(null,u,o)}i=l.namespace}var d=px(e,t,n,i);return Gu(r,d),nv(d,t),d}function Nx(e,t){e.appendChild(t)}function kx(e,t,n,a,r){switch(mx(e,t,n,a),t){case"button":case"input":case"select":case"textarea":return!!n.autoFocus;case"img":return!0;default:return!1}}function zx(e,t,n,a,r,i){{var l=i;if(typeof a.children!=typeof n.children&&(typeof a.children=="string"||typeof a.children=="number")){var u=""+a.children,o=Bu(l.ancestorInfo,t);Vu(null,u,o)}}return yx(e,t,n,a)}function Id(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}function Hx(e,t,n,a){{var r=n;Vu(null,e,r.ancestorInfo)}var i=hx(e,t);return Gu(a,i),i}function Fx(){var e=window.event;return e===void 0?$a:$m(e.type)}var Xd=typeof setTimeout=="function"?setTimeout:void 0,jx=typeof clearTimeout=="function"?clearTimeout:void 0,Kd=-1,Ny=typeof Promise=="function"?Promise:void 0,Vx=typeof queueMicrotask=="function"?queueMicrotask:typeof Ny<"u"?function(e){return Ny.resolve(null).then(e).catch(Bx)}:Xd;function Bx(e){setTimeout(function(){throw e})}function $x(e,t,n,a){switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&e.focus();return;case"img":{n.src&&(e.src=n.src);return}}}function Yx(e,t,n,a,r,i){gx(e,t,n,a,r),nv(e,r)}function ky(e){ls(e,"")}function Px(e,t,n){e.nodeValue=n}function qx(e,t){e.appendChild(t)}function Gx(e,t){var n;e.nodeType===ut?(n=e.parentNode,n.insertBefore(t,e)):(n=e,n.appendChild(t));var a=e._reactRootContainer;a==null&&n.onclick===null&&Fs(n)}function Qx(e,t,n){e.insertBefore(t,n)}function Wx(e,t,n){e.nodeType===ut?e.parentNode.insertBefore(t,n):e.insertBefore(t,n)}function Ix(e,t){e.removeChild(t)}function Xx(e,t){e.nodeType===ut?e.parentNode.removeChild(t):e.removeChild(t)}function Jd(e,t){var n=t,a=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===ut){var i=r.data;if(i===Bs)if(a===0){e.removeChild(r),xu(t);return}else a--;else(i===Vs||i===$u||i===Yu)&&a++}n=r}while(n);xu(t)}function Kx(e,t){e.nodeType===ut?Jd(e.parentNode,t):e.nodeType===un&&Jd(e,t),xu(e)}function Jx(e){e=e;var t=e.style;typeof t.setProperty=="function"?t.setProperty("display","none","important"):t.display="none"}function Zx(e){e.nodeValue=""}function eD(e,t){e=e;var n=t[Ox],a=n!=null&&n.hasOwnProperty("display")?n.display:null;e.style.display=Lf("display",a)}function tD(e,t){e.nodeValue=t}function nD(e){e.nodeType===un?e.textContent="":e.nodeType===ka&&e.documentElement&&e.removeChild(e.documentElement)}function aD(e,t,n){return e.nodeType!==un||t.toLowerCase()!==e.nodeName.toLowerCase()?null:e}function rD(e,t){return t===""||e.nodeType!==Na?null:e}function iD(e){return e.nodeType!==ut?null:e}function zy(e){return e.data===$u}function Zd(e){return e.data===Yu}function lD(e){var t=e.nextSibling&&e.nextSibling.dataset,n,a,r;return t&&(n=t.dgst,a=t.msg,r=t.stck),{message:a,digest:n,stack:r}}function uD(e,t){e._reactRetry=t}function $s(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===un||t===Na)break;if(t===ut){var n=e.data;if(n===Vs||n===Yu||n===$u)break;if(n===Bs)return null}}return e}function Pu(e){return $s(e.nextSibling)}function oD(e){return $s(e.firstChild)}function sD(e){return $s(e.firstChild)}function cD(e){return $s(e.nextSibling)}function fD(e,t,n,a,r,i,l){Gu(i,e),nv(e,n);var u;{var o=r;u=o.namespace}var d=(i.mode&Re)!==re;return Sx(e,t,n,u,a,d,l)}function dD(e,t,n,a){return Gu(n,e),n.mode&Re,Cx(e,t)}function vD(e,t){Gu(t,e)}function pD(e){for(var t=e.nextSibling,n=0;t;){if(t.nodeType===ut){var a=t.data;if(a===Bs){if(n===0)return Pu(t);n--}else(a===Vs||a===Yu||a===$u)&&n++}t=t.nextSibling}return null}function Hy(e){for(var t=e.previousSibling,n=0;t;){if(t.nodeType===ut){var a=t.data;if(a===Vs||a===Yu||a===$u){if(n===0)return t;n--}else a===Bs&&n++}t=t.previousSibling}return null}function hD(e){xu(e)}function mD(e){xu(e)}function yD(e){return e!=="head"&&e!=="body"}function gD(e,t,n,a){var r=!0;Hs(t.nodeValue,n,a,r)}function bD(e,t,n,a,r,i){if(t[js]!==!0){var l=!0;Hs(a.nodeValue,r,i,l)}}function SD(e,t){t.nodeType===un?Yd(e,t):t.nodeType===ut||Pd(e,t)}function CD(e,t){{var n=e.parentNode;n!==null&&(t.nodeType===un?Yd(n,t):t.nodeType===ut||Pd(n,t))}}function ED(e,t,n,a,r){(r||t[js]!==!0)&&(a.nodeType===un?Yd(n,a):a.nodeType===ut||Pd(n,a))}function RD(e,t,n){qd(e,t)}function TD(e,t){Gd(e,t)}function xD(e,t,n){{var a=e.parentNode;a!==null&&qd(a,t)}}function DD(e,t){{var n=e.parentNode;n!==null&&Gd(n,t)}}function wD(e,t,n,a,r,i){(i||t[js]!==!0)&&qd(n,a)}function OD(e,t,n,a,r){(r||t[js]!==!0)&&Gd(n,a)}function _D(e){f("An error occurred during hydration. The server HTML was replaced with client content in <%s>.",e.nodeName.toLowerCase())}function LD(e){zu(e)}var sl=Math.random().toString(36).slice(2),cl="__reactFiber$"+sl,ev="__reactProps$"+sl,qu="__reactContainer$"+sl,tv="__reactEvents$"+sl,MD="__reactListeners$"+sl,UD="__reactHandles$"+sl;function AD(e){delete e[cl],delete e[ev],delete e[tv],delete e[MD],delete e[UD]}function Gu(e,t){t[cl]=e}function Ys(e,t){t[qu]=e}function Fy(e){e[qu]=null}function Qu(e){return!!e[qu]}function di(e){var t=e[cl];if(t)return t;for(var n=e.parentNode;n;){if(t=n[qu]||n[cl],t){var a=t.alternate;if(t.child!==null||a!==null&&a.child!==null)for(var r=Hy(e);r!==null;){var i=r[cl];if(i)return i;r=Hy(r)}return t}e=n,n=e.parentNode}return null}function Rr(e){var t=e[cl]||e[qu];return t&&(t.tag===B||t.tag===J||t.tag===ie||t.tag===M)?t:null}function fl(e){if(e.tag===B||e.tag===J)return e.stateNode;throw new Error("getNodeFromInstance: Invalid argument.")}function Ps(e){return e[ev]||null}function nv(e,t){e[ev]=t}function ND(e){var t=e[tv];return t===void 0&&(t=e[tv]=new Set),t}var jy={},Vy=m.ReactDebugCurrentFrame;function qs(e){if(e){var t=e._owner,n=fr(e.type,e._source,t?t.type:null);Vy.setExtraStackFrame(n)}else Vy.setExtraStackFrame(null)}function Qn(e,t,n,a,r){{var i=Function.call.bind(rn);for(var l in e)if(i(e,l)){var u=void 0;try{if(typeof e[l]!="function"){var o=Error((a||"React class")+": "+n+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw o.name="Invariant Violation",o}u=e[l](t,l,a,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(d){u=d}u&&!(u instanceof Error)&&(qs(r),f("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",a||"React class",n,l,typeof u),qs(null)),u instanceof Error&&!(u.message in jy)&&(jy[u.message]=!0,qs(r),f("Failed %s type: %s",n,u.message),qs(null))}}}var av=[],Gs;Gs=[];var Ya=-1;function Tr(e){return{current:e}}function $t(e,t){if(Ya<0){f("Unexpected pop.");return}t!==Gs[Ya]&&f("Unexpected Fiber popped."),e.current=av[Ya],av[Ya]=null,Gs[Ya]=null,Ya--}function Yt(e,t,n){Ya++,av[Ya]=e.current,Gs[Ya]=n,e.current=t}var rv;rv={};var xn={};Object.freeze(xn);var Pa=Tr(xn),ma=Tr(!1),iv=xn;function dl(e,t,n){return n&&ya(t)?iv:Pa.current}function By(e,t,n){{var a=e.stateNode;a.__reactInternalMemoizedUnmaskedChildContext=t,a.__reactInternalMemoizedMaskedChildContext=n}}function vl(e,t){{var n=e.type,a=n.contextTypes;if(!a)return xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={};for(var l in a)i[l]=t[l];{var u=pe(e)||"Unknown";Qn(a,i,"context",u)}return r&&By(e,t,i),i}}function Qs(){return ma.current}function ya(e){{var t=e.childContextTypes;return t!=null}}function Ws(e){$t(ma,e),$t(Pa,e)}function lv(e){$t(ma,e),$t(Pa,e)}function $y(e,t,n){{if(Pa.current!==xn)throw new Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");Yt(Pa,t,e),Yt(ma,n,e)}}function Yy(e,t,n){{var a=e.stateNode,r=t.childContextTypes;if(typeof a.getChildContext!="function"){{var i=pe(e)||"Unknown";rv[i]||(rv[i]=!0,f("%s.childContextTypes is specified but there is no getChildContext() method on the instance. You can either define getChildContext() on %s or remove childContextTypes from it.",i,i))}return n}var l=a.getChildContext();for(var u in l)if(!(u in r))throw new Error((pe(e)||"Unknown")+'.getChildContext(): key "'+u+'" is not defined in childContextTypes.');{var o=pe(e)||"Unknown";Qn(r,l,"child context",o)}return Se({},n,l)}}function Is(e){{var t=e.stateNode,n=t&&t.__reactInternalMemoizedMergedChildContext||xn;return iv=Pa.current,Yt(Pa,n,e),Yt(ma,ma.current,e),!0}}function Py(e,t,n){{var a=e.stateNode;if(!a)throw new Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");if(n){var r=Yy(e,t,iv);a.__reactInternalMemoizedMergedChildContext=r,$t(ma,e),$t(Pa,e),Yt(Pa,r,e),Yt(ma,n,e)}else $t(ma,e),Yt(ma,n,e)}}function kD(e){{if(!yE(e)||e.tag!==w)throw new Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var t=e;do{switch(t.tag){case M:return t.stateNode.context;case w:{var n=t.type;if(ya(n))return t.stateNode.__reactInternalMemoizedMergedChildContext;break}}t=t.return}while(t!==null);throw new Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.")}}var xr=0,Xs=1,qa=null,uv=!1,ov=!1;function qy(e){qa===null?qa=[e]:qa.push(e)}function zD(e){uv=!0,qy(e)}function Gy(){uv&&Dr()}function Dr(){if(!ov&&qa!==null){ov=!0;var e=0,t=Gn();try{var n=!0,a=qa;for(Dt(En);e<a.length;e++){var r=a[e];do r=r(n);while(r!==null)}qa=null,uv=!1}catch(i){throw qa!==null&&(qa=qa.slice(e+1)),mm(ds,Dr),i}finally{Dt(t),ov=!1}}return null}var pl=[],hl=0,Ks=null,Js=0,Un=[],An=0,vi=null,Ga=1,Qa="";function HD(e){return hi(),(e.flags&om)!==le}function FD(e){return hi(),Js}function jD(){var e=Qa,t=Ga,n=t&~VD(t);return n.toString(32)+e}function pi(e,t){hi(),pl[hl++]=Js,pl[hl++]=Ks,Ks=e,Js=t}function Qy(e,t,n){hi(),Un[An++]=Ga,Un[An++]=Qa,Un[An++]=vi,vi=e;var a=Ga,r=Qa,i=Zs(a)-1,l=a&~(1<<i),u=n+1,o=Zs(t)+i;if(o>30){var d=i-i%5,v=(1<<d)-1,b=(l&v).toString(32),g=l>>d,R=i-d,D=Zs(t)+R,_=u<<R,Y=_|g,te=b+r;Ga=1<<D|Y,Qa=te}else{var ee=u<<i,xe=ee|l,ge=r;Ga=1<<o|xe,Qa=ge}}function sv(e){hi();var t=e.return;if(t!==null){var n=1,a=0;pi(e,n),Qy(e,n,a)}}function Zs(e){return 32-Em(e)}function VD(e){return 1<<Zs(e)-1}function cv(e){for(;e===Ks;)Ks=pl[--hl],pl[hl]=null,Js=pl[--hl],pl[hl]=null;for(;e===vi;)vi=Un[--An],Un[An]=null,Qa=Un[--An],Un[An]=null,Ga=Un[--An],Un[An]=null}function BD(){return hi(),vi!==null?{id:Ga,overflow:Qa}:null}function $D(e,t){hi(),Un[An++]=Ga,Un[An++]=Qa,Un[An++]=vi,Ga=t.id,Qa=t.overflow,vi=e}function hi(){Lt()||f("Expected to be hydrating. This is a bug in React. Please file an issue.")}var _t=null,Nn=null,Wn=!1,mi=!1,wr=null;function YD(){Wn&&f("We should not be hydrating here. This is a bug in React. Please file a bug.")}function Wy(){mi=!0}function PD(){return mi}function qD(e){var t=e.stateNode.containerInfo;return Nn=sD(t),_t=e,Wn=!0,wr=null,mi=!1,!0}function GD(e,t,n){return Nn=cD(t),_t=e,Wn=!0,wr=null,mi=!1,n!==null&&$D(e,n),!0}function Iy(e,t){switch(e.tag){case M:{SD(e.stateNode.containerInfo,t);break}case B:{var n=(e.mode&Re)!==re;ED(e.type,e.memoizedProps,e.stateNode,t,n);break}case ie:{var a=e.memoizedState;a.dehydrated!==null&&CD(a.dehydrated,t);break}}}function Xy(e,t){Iy(e,t);var n=XO();n.stateNode=t,n.return=e;var a=e.deletions;a===null?(e.deletions=[n],e.flags|=ei):a.push(n)}function fv(e,t){{if(mi)return;switch(e.tag){case M:{var n=e.stateNode.containerInfo;switch(t.tag){case B:var a=t.type;t.pendingProps,RD(n,a);break;case J:var r=t.pendingProps;TD(n,r);break}break}case B:{var i=e.type,l=e.memoizedProps,u=e.stateNode;switch(t.tag){case B:{var o=t.type,d=t.pendingProps,v=(e.mode&Re)!==re;wD(i,l,u,o,d,v);break}case J:{var b=t.pendingProps,g=(e.mode&Re)!==re;OD(i,l,u,b,g);break}}break}case ie:{var R=e.memoizedState,D=R.dehydrated;if(D!==null)switch(t.tag){case B:var _=t.type;t.pendingProps,xD(D,_);break;case J:var Y=t.pendingProps;DD(D,Y);break}break}default:return}}}function Ky(e,t){t.flags=t.flags&~Ha|ot,fv(e,t)}function Jy(e,t){switch(e.tag){case B:{var n=e.type;e.pendingProps;var a=aD(t,n);return a!==null?(e.stateNode=a,_t=e,Nn=oD(a),!0):!1}case J:{var r=e.pendingProps,i=rD(t,r);return i!==null?(e.stateNode=i,_t=e,Nn=null,!0):!1}case ie:{var l=iD(t);if(l!==null){var u={dehydrated:l,treeContext:BD(),retryLane:Sn};e.memoizedState=u;var o=KO(l);return o.return=e,e.child=o,_t=e,Nn=null,!0}return!1}default:return!1}}function dv(e){return(e.mode&Re)!==re&&(e.flags&Ue)===le}function vv(e){throw new Error("Hydration failed because the initial UI does not match what was rendered on the server.")}function pv(e){if(Wn){var t=Nn;if(!t){dv(e)&&(fv(_t,e),vv()),Ky(_t,e),Wn=!1,_t=e;return}var n=t;if(!Jy(e,t)){dv(e)&&(fv(_t,e),vv()),t=Pu(n);var a=_t;if(!t||!Jy(e,t)){Ky(_t,e),Wn=!1,_t=e;return}Xy(a,n)}}}function QD(e,t,n){var a=e.stateNode,r=!mi,i=fD(a,e.type,e.memoizedProps,t,n,e,r);return e.updateQueue=i,i!==null}function WD(e){var t=e.stateNode,n=e.memoizedProps,a=dD(t,n,e);if(a){var r=_t;if(r!==null)switch(r.tag){case M:{var i=r.stateNode.containerInfo,l=(r.mode&Re)!==re;gD(i,t,n,l);break}case B:{var u=r.type,o=r.memoizedProps,d=r.stateNode,v=(r.mode&Re)!==re;bD(u,o,d,t,n,v);break}}}return a}function ID(e){var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");vD(n,e)}function XD(e){var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");return pD(n)}function Zy(e){for(var t=e.return;t!==null&&t.tag!==B&&t.tag!==M&&t.tag!==ie;)t=t.return;_t=t}function ec(e){if(e!==_t)return!1;if(!Wn)return Zy(e),Wn=!0,!1;if(e.tag!==M&&(e.tag!==B||yD(e.type)&&!Id(e.type,e.memoizedProps))){var t=Nn;if(t)if(dv(e))eg(e),vv();else for(;t;)Xy(e,t),t=Pu(t)}return Zy(e),e.tag===ie?Nn=XD(e):Nn=_t?Pu(e.stateNode):null,!0}function KD(){return Wn&&Nn!==null}function eg(e){for(var t=Nn;t;)Iy(e,t),t=Pu(t)}function ml(){_t=null,Nn=null,Wn=!1,mi=!1}function tg(){wr!==null&&(Wb(wr),wr=null)}function Lt(){return Wn}function hv(e){wr===null?wr=[e]:wr.push(e)}var JD=m.ReactCurrentBatchConfig,ZD=null;function e0(){return JD.transition}var In={recordUnsafeLifecycleWarnings:function(e,t){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(e,t){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}};{var t0=function(e){for(var t=null,n=e;n!==null;)n.mode&at&&(t=n),n=n.return;return t},yi=function(e){var t=[];return e.forEach(function(n){t.push(n)}),t.sort().join(", ")},Wu=[],Iu=[],Xu=[],Ku=[],Ju=[],Zu=[],gi=new Set;In.recordUnsafeLifecycleWarnings=function(e,t){gi.has(e.type)||(typeof t.componentWillMount=="function"&&t.componentWillMount.__suppressDeprecationWarning!==!0&&Wu.push(e),e.mode&at&&typeof t.UNSAFE_componentWillMount=="function"&&Iu.push(e),typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps.__suppressDeprecationWarning!==!0&&Xu.push(e),e.mode&at&&typeof t.UNSAFE_componentWillReceiveProps=="function"&&Ku.push(e),typeof t.componentWillUpdate=="function"&&t.componentWillUpdate.__suppressDeprecationWarning!==!0&&Ju.push(e),e.mode&at&&typeof t.UNSAFE_componentWillUpdate=="function"&&Zu.push(e))},In.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;Wu.length>0&&(Wu.forEach(function(g){e.add(pe(g)||"Component"),gi.add(g.type)}),Wu=[]);var t=new Set;Iu.length>0&&(Iu.forEach(function(g){t.add(pe(g)||"Component"),gi.add(g.type)}),Iu=[]);var n=new Set;Xu.length>0&&(Xu.forEach(function(g){n.add(pe(g)||"Component"),gi.add(g.type)}),Xu=[]);var a=new Set;Ku.length>0&&(Ku.forEach(function(g){a.add(pe(g)||"Component"),gi.add(g.type)}),Ku=[]);var r=new Set;Ju.length>0&&(Ju.forEach(function(g){r.add(pe(g)||"Component"),gi.add(g.type)}),Ju=[]);var i=new Set;if(Zu.length>0&&(Zu.forEach(function(g){i.add(pe(g)||"Component"),gi.add(g.type)}),Zu=[]),t.size>0){var l=yi(t);f(`Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.

Please update the following components: %s`,l)}if(a.size>0){var u=yi(a);f(`Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state

Please update the following components: %s`,u)}if(i.size>0){var o=yi(i);f(`Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.

Please update the following components: %s`,o)}if(e.size>0){var d=yi(e);T(`componentWillMount has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.
* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,d)}if(n.size>0){var v=yi(n);T(`componentWillReceiveProps has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,v)}if(r.size>0){var b=yi(r);T(`componentWillUpdate has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,b)}};var tc=new Map,ng=new Set;In.recordLegacyContextWarning=function(e,t){var n=t0(e);if(n===null){f("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.");return}if(!ng.has(e.type)){var a=tc.get(n);(e.type.contextTypes!=null||e.type.childContextTypes!=null||t!==null&&typeof t.getChildContext=="function")&&(a===void 0&&(a=[],tc.set(n,a)),a.push(e))}},In.flushLegacyContextWarning=function(){tc.forEach(function(e,t){if(e.length!==0){var n=e[0],a=new Set;e.forEach(function(i){a.add(pe(i)||"Component"),ng.add(i.type)});var r=yi(a);try{Je(n),f(`Legacy context API has been detected within a strict-mode tree.

The old API will be supported in all 16.x releases, but applications using it should migrate to the new version.

Please update the following components: %s

Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)}finally{Et()}}})},In.discardPendingWarnings=function(){Wu=[],Iu=[],Xu=[],Ku=[],Ju=[],Zu=[],tc=new Map}}var mv,yv,gv,bv,Sv,ag=function(e,t){};mv=!1,yv=!1,gv={},bv={},Sv={},ag=function(e,t){if(!(e===null||typeof e!="object")&&!(!e._store||e._store.validated||e.key!=null)){if(typeof e._store!="object")throw new Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");e._store.validated=!0;var n=pe(t)||"Component";bv[n]||(bv[n]=!0,f('Each child in a list should have a unique "key" prop. See https://reactjs.org/link/warning-keys for more information.'))}};function n0(e){return e.prototype&&e.prototype.isReactComponent}function eo(e,t,n){var a=n.ref;if(a!==null&&typeof a!="function"&&typeof a!="object"){if((e.mode&at||$n)&&!(n._owner&&n._self&&n._owner.stateNode!==n._self)&&!(n._owner&&n._owner.tag!==w)&&!(typeof n.type=="function"&&!n0(n.type))&&n._owner){var r=pe(e)||"Component";gv[r]||(f('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. We recommend using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',r,a),gv[r]=!0)}if(n._owner){var i=n._owner,l;if(i){var u=i;if(u.tag!==w)throw new Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");l=u.stateNode}if(!l)throw new Error("Missing owner for string ref "+a+". This error is likely caused by a bug in React. Please file an issue.");var o=l;_n(a,"ref");var d=""+a;if(t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d)return t.ref;var v=function(b){var g=o.refs;b===null?delete g[d]:g[d]=b};return v._stringRef=d,v}else{if(typeof a!="string")throw new Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!n._owner)throw new Error("Element ref was specified as a string ("+a+`) but no owner was set. This could happen for one of the following reasons:
1. You may be adding a ref to a function component
2. You may be adding a ref to a component that was not created inside a component's render method
3. You have multiple copies of React loaded
See https://reactjs.org/link/refs-must-have-owner for more information.`)}}return a}function nc(e,t){var n=Object.prototype.toString.call(t);throw new Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}function ac(e){{var t=pe(e)||"Component";if(Sv[t])return;Sv[t]=!0,f("Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.")}}function rg(e){var t=e._payload,n=e._init;return n(t)}function ig(e){function t(C,L){if(e){var E=C.deletions;E===null?(C.deletions=[L],C.flags|=ei):E.push(L)}}function n(C,L){if(!e)return null;for(var E=L;E!==null;)t(C,E),E=E.sibling;return null}function a(C,L){for(var E=new Map,z=L;z!==null;)z.key!==null?E.set(z.key,z):E.set(z.index,z),z=z.sibling;return E}function r(C,L){var E=wi(C,L);return E.index=0,E.sibling=null,E}function i(C,L,E){if(C.index=E,!e)return C.flags|=om,L;var z=C.alternate;if(z!==null){var W=z.index;return W<L?(C.flags|=ot,L):W}else return C.flags|=ot,L}function l(C){return e&&C.alternate===null&&(C.flags|=ot),C}function u(C,L,E,z){if(L===null||L.tag!==J){var W=hh(E,C.mode,z);return W.return=C,W}else{var P=r(L,E);return P.return=C,P}}function o(C,L,E,z){var W=E.type;if(W===ur)return v(C,L,E.props.children,z,E.key);if(L!==null&&(L.elementType===W||cS(L,E)||typeof W=="object"&&W!==null&&W.$$typeof===Ot&&rg(W)===L.type)){var P=r(L,E.props);return P.ref=eo(C,L,E),P.return=C,P._debugSource=E._source,P._debugOwner=E._owner,P}var oe=ph(E,C.mode,z);return oe.ref=eo(C,L,E),oe.return=C,oe}function d(C,L,E,z){if(L===null||L.tag!==F||L.stateNode.containerInfo!==E.containerInfo||L.stateNode.implementation!==E.implementation){var W=mh(E,C.mode,z);return W.return=C,W}else{var P=r(L,E.children||[]);return P.return=C,P}}function v(C,L,E,z,W){if(L===null||L.tag!==ye){var P=Fr(E,C.mode,z,W);return P.return=C,P}else{var oe=r(L,E);return oe.return=C,oe}}function b(C,L,E){if(typeof L=="string"&&L!==""||typeof L=="number"){var z=hh(""+L,C.mode,E);return z.return=C,z}if(typeof L=="object"&&L!==null){switch(L.$$typeof){case lr:{var W=ph(L,C.mode,E);return W.ref=eo(C,null,L),W.return=C,W}case _a:{var P=mh(L,C.mode,E);return P.return=C,P}case Ot:{var oe=L._payload,de=L._init;return b(C,de(oe),E)}}if(Me(L)||La(L)){var He=Fr(L,C.mode,E,null);return He.return=C,He}nc(C,L)}return typeof L=="function"&&ac(C),null}function g(C,L,E,z){var W=L!==null?L.key:null;if(typeof E=="string"&&E!==""||typeof E=="number")return W!==null?null:u(C,L,""+E,z);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case lr:return E.key===W?o(C,L,E,z):null;case _a:return E.key===W?d(C,L,E,z):null;case Ot:{var P=E._payload,oe=E._init;return g(C,L,oe(P),z)}}if(Me(E)||La(E))return W!==null?null:v(C,L,E,z,null);nc(C,E)}return typeof E=="function"&&ac(C),null}function R(C,L,E,z,W){if(typeof z=="string"&&z!==""||typeof z=="number"){var P=C.get(E)||null;return u(L,P,""+z,W)}if(typeof z=="object"&&z!==null){switch(z.$$typeof){case lr:{var oe=C.get(z.key===null?E:z.key)||null;return o(L,oe,z,W)}case _a:{var de=C.get(z.key===null?E:z.key)||null;return d(L,de,z,W)}case Ot:var He=z._payload,_e=z._init;return R(C,L,E,_e(He),W)}if(Me(z)||La(z)){var lt=C.get(E)||null;return v(L,lt,z,W,null)}nc(L,z)}return typeof z=="function"&&ac(L),null}function D(C,L,E){{if(typeof C!="object"||C===null)return L;switch(C.$$typeof){case lr:case _a:ag(C,E);var z=C.key;if(typeof z!="string")break;if(L===null){L=new Set,L.add(z);break}if(!L.has(z)){L.add(z);break}f("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.",z);break;case Ot:var W=C._payload,P=C._init;D(P(W),L,E);break}}return L}function _(C,L,E,z){for(var W=null,P=0;P<E.length;P++){var oe=E[P];W=D(oe,W,C)}for(var de=null,He=null,_e=L,lt=0,Le=0,rt=null;_e!==null&&Le<E.length;Le++){_e.index>Le?(rt=_e,_e=null):rt=_e.sibling;var qt=g(C,_e,E[Le],z);if(qt===null){_e===null&&(_e=rt);break}e&&_e&&qt.alternate===null&&t(C,_e),lt=i(qt,lt,Le),He===null?de=qt:He.sibling=qt,He=qt,_e=rt}if(Le===E.length){if(n(C,_e),Lt()){var Ht=Le;pi(C,Ht)}return de}if(_e===null){for(;Le<E.length;Le++){var wn=b(C,E[Le],z);wn!==null&&(lt=i(wn,lt,Le),He===null?de=wn:He.sibling=wn,He=wn)}if(Lt()){var tn=Le;pi(C,tn)}return de}for(var nn=a(C,_e);Le<E.length;Le++){var Gt=R(nn,C,Le,E[Le],z);Gt!==null&&(e&&Gt.alternate!==null&&nn.delete(Gt.key===null?Le:Gt.key),lt=i(Gt,lt,Le),He===null?de=Gt:He.sibling=Gt,He=Gt)}if(e&&nn.forEach(function(kl){return t(C,kl)}),Lt()){var er=Le;pi(C,er)}return de}function Y(C,L,E,z){var W=La(E);if(typeof W!="function")throw new Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");{typeof Symbol=="function"&&E[Symbol.toStringTag]==="Generator"&&(yv||f("Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. Keep in mind you might need to polyfill these features for older browsers."),yv=!0),E.entries===W&&(mv||f("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),mv=!0);var P=W.call(E);if(P)for(var oe=null,de=P.next();!de.done;de=P.next()){var He=de.value;oe=D(He,oe,C)}}var _e=W.call(E);if(_e==null)throw new Error("An iterable object provided no iterator.");for(var lt=null,Le=null,rt=L,qt=0,Ht=0,wn=null,tn=_e.next();rt!==null&&!tn.done;Ht++,tn=_e.next()){rt.index>Ht?(wn=rt,rt=null):wn=rt.sibling;var nn=g(C,rt,tn.value,z);if(nn===null){rt===null&&(rt=wn);break}e&&rt&&nn.alternate===null&&t(C,rt),qt=i(nn,qt,Ht),Le===null?lt=nn:Le.sibling=nn,Le=nn,rt=wn}if(tn.done){if(n(C,rt),Lt()){var Gt=Ht;pi(C,Gt)}return lt}if(rt===null){for(;!tn.done;Ht++,tn=_e.next()){var er=b(C,tn.value,z);er!==null&&(qt=i(er,qt,Ht),Le===null?lt=er:Le.sibling=er,Le=er)}if(Lt()){var kl=Ht;pi(C,kl)}return lt}for(var Ao=a(C,rt);!tn.done;Ht++,tn=_e.next()){var xa=R(Ao,C,Ht,tn.value,z);xa!==null&&(e&&xa.alternate!==null&&Ao.delete(xa.key===null?Ht:xa.key),qt=i(xa,qt,Ht),Le===null?lt=xa:Le.sibling=xa,Le=xa)}if(e&&Ao.forEach(function(O_){return t(C,O_)}),Lt()){var w_=Ht;pi(C,w_)}return lt}function te(C,L,E,z){if(L!==null&&L.tag===J){n(C,L.sibling);var W=r(L,E);return W.return=C,W}n(C,L);var P=hh(E,C.mode,z);return P.return=C,P}function ee(C,L,E,z){for(var W=E.key,P=L;P!==null;){if(P.key===W){var oe=E.type;if(oe===ur){if(P.tag===ye){n(C,P.sibling);var de=r(P,E.props.children);return de.return=C,de._debugSource=E._source,de._debugOwner=E._owner,de}}else if(P.elementType===oe||cS(P,E)||typeof oe=="object"&&oe!==null&&oe.$$typeof===Ot&&rg(oe)===P.type){n(C,P.sibling);var He=r(P,E.props);return He.ref=eo(C,P,E),He.return=C,He._debugSource=E._source,He._debugOwner=E._owner,He}n(C,P);break}else t(C,P);P=P.sibling}if(E.type===ur){var _e=Fr(E.props.children,C.mode,z,E.key);return _e.return=C,_e}else{var lt=ph(E,C.mode,z);return lt.ref=eo(C,L,E),lt.return=C,lt}}function xe(C,L,E,z){for(var W=E.key,P=L;P!==null;){if(P.key===W)if(P.tag===F&&P.stateNode.containerInfo===E.containerInfo&&P.stateNode.implementation===E.implementation){n(C,P.sibling);var oe=r(P,E.children||[]);return oe.return=C,oe}else{n(C,P);break}else t(C,P);P=P.sibling}var de=mh(E,C.mode,z);return de.return=C,de}function ge(C,L,E,z){var W=typeof E=="object"&&E!==null&&E.type===ur&&E.key===null;if(W&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case lr:return l(ee(C,L,E,z));case _a:return l(xe(C,L,E,z));case Ot:var P=E._payload,oe=E._init;return ge(C,L,oe(P),z)}if(Me(E))return _(C,L,E,z);if(La(E))return Y(C,L,E,z);nc(C,E)}return typeof E=="string"&&E!==""||typeof E=="number"?l(te(C,L,""+E,z)):(typeof E=="function"&&ac(C),n(C,L))}return ge}var yl=ig(!0),lg=ig(!1);function a0(e,t){if(e!==null&&t.child!==e.child)throw new Error("Resuming work not yet implemented.");if(t.child!==null){var n=t.child,a=wi(n,n.pendingProps);for(t.child=a,a.return=t;n.sibling!==null;)n=n.sibling,a=a.sibling=wi(n,n.pendingProps),a.return=t;a.sibling=null}}function r0(e,t){for(var n=e.child;n!==null;)qO(n,t),n=n.sibling}var Cv=Tr(null),Ev;Ev={};var rc=null,gl=null,Rv=null,ic=!1;function lc(){rc=null,gl=null,Rv=null,ic=!1}function ug(){ic=!0}function og(){ic=!1}function sg(e,t,n){Yt(Cv,t._currentValue,e),t._currentValue=n,t._currentRenderer!==void 0&&t._currentRenderer!==null&&t._currentRenderer!==Ev&&f("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer=Ev}function Tv(e,t){var n=Cv.current;$t(Cv,t),e._currentValue=n}function xv(e,t,n){for(var a=e;a!==null;){var r=a.alternate;if(nl(a.childLanes,t)?r!==null&&!nl(r.childLanes,t)&&(r.childLanes=he(r.childLanes,t)):(a.childLanes=he(a.childLanes,t),r!==null&&(r.childLanes=he(r.childLanes,t))),a===n)break;a=a.return}a!==n&&f("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function i0(e,t,n){l0(e,t,n)}function l0(e,t,n){var a=e.child;for(a!==null&&(a.return=e);a!==null;){var r=void 0,i=a.dependencies;if(i!==null){r=a.child;for(var l=i.firstContext;l!==null;){if(l.context===t){if(a.tag===w){var u=gu(n),o=Wa(Ge,u);o.tag=oc;var d=a.updateQueue;if(d!==null){var v=d.shared,b=v.pending;b===null?o.next=o:(o.next=b.next,b.next=o),v.pending=o}}a.lanes=he(a.lanes,n);var g=a.alternate;g!==null&&(g.lanes=he(g.lanes,n)),xv(a.return,n,e),i.lanes=he(i.lanes,n);break}l=l.next}}else if(a.tag===I)r=a.type===e.type?null:a.child;else if(a.tag===et){var R=a.return;if(R===null)throw new Error("We just came from a parent so we must have had a parent. This is a bug in React.");R.lanes=he(R.lanes,n);var D=R.alternate;D!==null&&(D.lanes=he(D.lanes,n)),xv(R,n,e),r=a.sibling}else r=a.child;if(r!==null)r.return=a;else for(r=a;r!==null;){if(r===e){r=null;break}var _=r.sibling;if(_!==null){_.return=r.return,r=_;break}r=r.return}a=r}}function bl(e,t){rc=e,gl=null,Rv=null;var n=e.dependencies;if(n!==null){var a=n.firstContext;a!==null&&(Cn(n.lanes,t)&&mo(),n.firstContext=null)}}function st(e){ic&&f("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");var t=e._currentValue;if(Rv!==e){var n={context:e,memoizedValue:t,next:null};if(gl===null){if(rc===null)throw new Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");gl=n,rc.dependencies={lanes:N,firstContext:n}}else gl=gl.next=n}return t}var bi=null;function Dv(e){bi===null?bi=[e]:bi.push(e)}function u0(){if(bi!==null){for(var e=0;e<bi.length;e++){var t=bi[e],n=t.interleaved;if(n!==null){t.interleaved=null;var a=n.next,r=t.pending;if(r!==null){var i=r.next;r.next=a,n.next=i}t.pending=n}}bi=null}}function cg(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Dv(t)):(n.next=r.next,r.next=n),t.interleaved=n,uc(e,a)}function o0(e,t,n,a){var r=t.interleaved;r===null?(n.next=n,Dv(t)):(n.next=r.next,r.next=n),t.interleaved=n}function s0(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Dv(t)):(n.next=r.next,r.next=n),t.interleaved=n,uc(e,a)}function sn(e,t){return uc(e,t)}var c0=uc;function uc(e,t){e.lanes=he(e.lanes,t);var n=e.alternate;n!==null&&(n.lanes=he(n.lanes,t)),n===null&&(e.flags&(ot|Ha))!==le&&lS(e);for(var a=e,r=e.return;r!==null;)r.childLanes=he(r.childLanes,t),n=r.alternate,n!==null?n.childLanes=he(n.childLanes,t):(r.flags&(ot|Ha))!==le&&lS(e),a=r,r=r.return;if(a.tag===M){var i=a.stateNode;return i}else return null}var fg=0,dg=1,oc=2,wv=3,sc=!1,Ov,cc;Ov=!1,cc=null;function _v(e){var t={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:N},effects:null};e.updateQueue=t}function vg(e,t){var n=t.updateQueue,a=e.updateQueue;if(n===a){var r={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects};t.updateQueue=r}}function Wa(e,t){var n={eventTime:e,lane:t,tag:fg,payload:null,callback:null,next:null};return n}function Or(e,t,n){var a=e.updateQueue;if(a===null)return null;var r=a.shared;if(cc===r&&!Ov&&(f("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback."),Ov=!0),oO()){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,c0(e,n)}else return s0(e,r,t,n)}function fc(e,t,n){var a=t.updateQueue;if(a!==null){var r=a.shared;if(Dm(n)){var i=r.lanes;i=Om(i,e.pendingLanes);var l=he(i,n);r.lanes=l,Cd(e,l)}}}function Lv(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null){var r=a.updateQueue;if(n===r){var i=null,l=null,u=n.firstBaseUpdate;if(u!==null){var o=u;do{var d={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};l===null?i=l=d:(l.next=d,l=d),o=o.next}while(o!==null);l===null?i=l=t:(l.next=t,l=t)}else i=l=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}}var v=n.lastBaseUpdate;v===null?n.firstBaseUpdate=t:v.next=t,n.lastBaseUpdate=t}function f0(e,t,n,a,r,i){switch(n.tag){case dg:{var l=n.payload;if(typeof l=="function"){ug();var u=l.call(i,a,r);{if(e.mode&at){Tt(!0);try{l.call(i,a,r)}finally{Tt(!1)}}og()}return u}return l}case wv:e.flags=e.flags&~Kt|Ue;case fg:{var o=n.payload,d;if(typeof o=="function"){ug(),d=o.call(i,a,r);{if(e.mode&at){Tt(!0);try{o.call(i,a,r)}finally{Tt(!1)}}og()}}else d=o;return d==null?a:Se({},a,d)}case oc:return sc=!0,a}return a}function dc(e,t,n,a){var r=e.updateQueue;sc=!1,cc=r.shared;var i=r.firstBaseUpdate,l=r.lastBaseUpdate,u=r.shared.pending;if(u!==null){r.shared.pending=null;var o=u,d=o.next;o.next=null,l===null?i=d:l.next=d,l=o;var v=e.alternate;if(v!==null){var b=v.updateQueue,g=b.lastBaseUpdate;g!==l&&(g===null?b.firstBaseUpdate=d:g.next=d,b.lastBaseUpdate=o)}}if(i!==null){var R=r.baseState,D=N,_=null,Y=null,te=null,ee=i;do{var xe=ee.lane,ge=ee.eventTime;if(nl(a,xe)){if(te!==null){var L={eventTime:ge,lane:xt,tag:ee.tag,payload:ee.payload,callback:ee.callback,next:null};te=te.next=L}R=f0(e,r,ee,R,t,n);var E=ee.callback;if(E!==null&&ee.lane!==xt){e.flags|=$f;var z=r.effects;z===null?r.effects=[ee]:z.push(ee)}}else{var C={eventTime:ge,lane:xe,tag:ee.tag,payload:ee.payload,callback:ee.callback,next:null};te===null?(Y=te=C,_=R):te=te.next=C,D=he(D,xe)}if(ee=ee.next,ee===null){if(u=r.shared.pending,u===null)break;var W=u,P=W.next;W.next=null,ee=P,r.lastBaseUpdate=W,r.shared.pending=null}}while(!0);te===null&&(_=R),r.baseState=_,r.firstBaseUpdate=Y,r.lastBaseUpdate=te;var oe=r.shared.interleaved;if(oe!==null){var de=oe;do D=he(D,de.lane),de=de.next;while(de!==oe)}else i===null&&(r.shared.lanes=N);Oo(D),e.lanes=D,e.memoizedState=R}cc=null}function d0(e,t){if(typeof e!="function")throw new Error("Invalid argument passed as callback. Expected a function. Instead "+("received: "+e));e.call(t)}function pg(){sc=!1}function vc(){return sc}function hg(e,t,n){var a=t.effects;if(t.effects=null,a!==null)for(var r=0;r<a.length;r++){var i=a[r],l=i.callback;l!==null&&(i.callback=null,d0(l,n))}}var to={},_r=Tr(to),no=Tr(to),pc=Tr(to);function hc(e){if(e===to)throw new Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return e}function mg(){var e=hc(pc.current);return e}function Mv(e,t){Yt(pc,t,e),Yt(no,e,e),Yt(_r,to,e);var n=_x(t);$t(_r,e),Yt(_r,n,e)}function Sl(e){$t(_r,e),$t(no,e),$t(pc,e)}function Uv(){var e=hc(_r.current);return e}function yg(e){hc(pc.current);var t=hc(_r.current),n=Lx(t,e.type);t!==n&&(Yt(no,e,e),Yt(_r,n,e))}function Av(e){no.current===e&&($t(_r,e),$t(no,e))}var v0=0,gg=1,bg=1,ao=2,Xn=Tr(v0);function Nv(e,t){return(e&t)!==0}function Cl(e){return e&gg}function kv(e,t){return e&gg|t}function p0(e,t){return e|t}function Lr(e,t){Yt(Xn,t,e)}function El(e){$t(Xn,e)}function h0(e,t){var n=e.memoizedState;return n!==null?n.dehydrated!==null:(e.memoizedProps,!0)}function mc(e){for(var t=e;t!==null;){if(t.tag===ie){var n=t.memoizedState;if(n!==null){var a=n.dehydrated;if(a===null||zy(a)||Zd(a))return t}}else if(t.tag===qe&&t.memoizedProps.revealOrder!==void 0){var r=(t.flags&Ue)!==le;if(r)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)return null;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var cn=0,ft=1,ga=2,dt=4,Mt=8,zv=[];function Hv(){for(var e=0;e<zv.length;e++){var t=zv[e];t._workInProgressVersionPrimary=null}zv.length=0}function m0(e,t){var n=t._getVersion,a=n(t._source);e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[t,a]:e.mutableSourceEagerHydrationData.push(t,a)}var Q=m.ReactCurrentDispatcher,ro=m.ReactCurrentBatchConfig,Fv,Rl;Fv=new Set;var Si=N,ze=null,vt=null,pt=null,yc=!1,io=!1,lo=0,y0=0,g0=25,A=null,kn=null,Mr=-1,jv=!1;function Ae(){{var e=A;kn===null?kn=[e]:kn.push(e)}}function $(){{var e=A;kn!==null&&(Mr++,kn[Mr]!==e&&b0(e))}}function Tl(e){e!=null&&!Me(e)&&f("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",A,typeof e)}function b0(e){{var t=pe(ze);if(!Fv.has(t)&&(Fv.add(t),kn!==null)){for(var n="",a=30,r=0;r<=Mr;r++){for(var i=kn[r],l=r===Mr?e:i,u=r+1+". "+i;u.length<a;)u+=" ";u+=l+`
`,n+=u}f(`React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`,t,n)}}}function Pt(){throw new Error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`)}function Vv(e,t){if(jv)return!1;if(t===null)return f("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",A),!1;e.length!==t.length&&f(`The final argument passed to %s changed size between renders. The order and size of this array must remain constant.

Previous: %s
Incoming: %s`,A,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!Tn(e[n],t[n]))return!1;return!0}function xl(e,t,n,a,r,i){Si=i,ze=t,kn=e!==null?e._debugHookTypes:null,Mr=-1,jv=e!==null&&e.type!==t.type,t.memoizedState=null,t.updateQueue=null,t.lanes=N,e!==null&&e.memoizedState!==null?Q.current=Bg:kn!==null?Q.current=Vg:Q.current=jg;var l=n(a,r);if(io){var u=0;do{if(io=!1,lo=0,u>=g0)throw new Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");u+=1,jv=!1,vt=null,pt=null,t.updateQueue=null,Mr=-1,Q.current=$g,l=n(a,r)}while(io)}Q.current=Lc,t._debugHookTypes=kn;var o=vt!==null&&vt.next!==null;if(Si=N,ze=null,vt=null,pt=null,A=null,kn=null,Mr=-1,e!==null&&(e.flags&ja)!==(t.flags&ja)&&(e.mode&Re)!==re&&f("Internal React error: Expected static flag was missing. Please notify the React team."),yc=!1,o)throw new Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return l}function Dl(){var e=lo!==0;return lo=0,e}function Sg(e,t,n){t.updateQueue=e.updateQueue,(t.mode&pa)!==re?t.flags&=~(fs|Fa|Pn|De):t.flags&=~(Pn|De),e.lanes=gs(e.lanes,n)}function Cg(){if(Q.current=Lc,yc){for(var e=ze.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}yc=!1}Si=N,ze=null,vt=null,pt=null,kn=null,Mr=-1,A=null,Ng=!1,io=!1,lo=0}function ba(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return pt===null?ze.memoizedState=pt=e:pt=pt.next=e,pt}function zn(){var e;if(vt===null){var t=ze.alternate;t!==null?e=t.memoizedState:e=null}else e=vt.next;var n;if(pt===null?n=ze.memoizedState:n=pt.next,n!==null)pt=n,n=pt.next,vt=e;else{if(e===null)throw new Error("Rendered more hooks than during the previous render.");vt=e;var a={memoizedState:vt.memoizedState,baseState:vt.baseState,baseQueue:vt.baseQueue,queue:vt.queue,next:null};pt===null?ze.memoizedState=pt=a:pt=pt.next=a}return pt}function Eg(){return{lastEffect:null,stores:null}}function Bv(e,t){return typeof t=="function"?t(e):t}function $v(e,t,n){var a=ba(),r;n!==void 0?r=n(t):r=t,a.memoizedState=a.baseState=r;var i={pending:null,interleaved:null,lanes:N,dispatch:null,lastRenderedReducer:e,lastRenderedState:r};a.queue=i;var l=i.dispatch=R0.bind(null,ze,i);return[a.memoizedState,l]}function Yv(e,t,n){var a=zn(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=vt,l=i.baseQueue,u=r.pending;if(u!==null){if(l!==null){var o=l.next,d=u.next;l.next=d,u.next=o}i.baseQueue!==l&&f("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),i.baseQueue=l=u,r.pending=null}if(l!==null){var v=l.next,b=i.baseState,g=null,R=null,D=null,_=v;do{var Y=_.lane;if(nl(Si,Y)){if(D!==null){var ee={lane:xt,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null};D=D.next=ee}if(_.hasEagerState)b=_.eagerState;else{var xe=_.action;b=e(b,xe)}}else{var te={lane:Y,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null};D===null?(R=D=te,g=b):D=D.next=te,ze.lanes=he(ze.lanes,Y),Oo(Y)}_=_.next}while(_!==null&&_!==v);D===null?g=b:D.next=R,Tn(b,a.memoizedState)||mo(),a.memoizedState=b,a.baseState=g,a.baseQueue=D,r.lastRenderedState=b}var ge=r.interleaved;if(ge!==null){var C=ge;do{var L=C.lane;ze.lanes=he(ze.lanes,L),Oo(L),C=C.next}while(C!==ge)}else l===null&&(r.lanes=N);var E=r.dispatch;return[a.memoizedState,E]}function Pv(e,t,n){var a=zn(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=r.dispatch,l=r.pending,u=a.memoizedState;if(l!==null){r.pending=null;var o=l.next,d=o;do{var v=d.action;u=e(u,v),d=d.next}while(d!==o);Tn(u,a.memoizedState)||mo(),a.memoizedState=u,a.baseQueue===null&&(a.baseState=u),r.lastRenderedState=u}return[u,i]}function ZL(e,t,n){}function eM(e,t,n){}function qv(e,t,n){var a=ze,r=ba(),i,l=Lt();if(l){if(n===void 0)throw new Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");i=n(),Rl||i!==n()&&(f("The result of getServerSnapshot should be cached to avoid an infinite loop"),Rl=!0)}else{if(i=t(),!Rl){var u=t();Tn(i,u)||(f("The result of getSnapshot should be cached to avoid an infinite loop"),Rl=!0)}var o=Ic();if(o===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");ys(o,Si)||Rg(a,t,i)}r.memoizedState=i;var d={value:i,getSnapshot:t};return r.queue=d,Ec(xg.bind(null,a,d,e),[e]),a.flags|=Pn,uo(ft|Mt,Tg.bind(null,a,d,i,t),void 0,null),i}function gc(e,t,n){var a=ze,r=zn(),i=t();if(!Rl){var l=t();Tn(i,l)||(f("The result of getSnapshot should be cached to avoid an infinite loop"),Rl=!0)}var u=r.memoizedState,o=!Tn(u,i);o&&(r.memoizedState=i,mo());var d=r.queue;if(so(xg.bind(null,a,d,e),[e]),d.getSnapshot!==t||o||pt!==null&&pt.memoizedState.tag&ft){a.flags|=Pn,uo(ft|Mt,Tg.bind(null,a,d,i,t),void 0,null);var v=Ic();if(v===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");ys(v,Si)||Rg(a,t,i)}return i}function Rg(e,t,n){e.flags|=cs;var a={getSnapshot:t,value:n},r=ze.updateQueue;if(r===null)r=Eg(),ze.updateQueue=r,r.stores=[a];else{var i=r.stores;i===null?r.stores=[a]:i.push(a)}}function Tg(e,t,n,a){t.value=n,t.getSnapshot=a,Dg(t)&&wg(e)}function xg(e,t,n){var a=function(){Dg(t)&&wg(e)};return n(a)}function Dg(e){var t=e.getSnapshot,n=e.value;try{var a=t();return!Tn(n,a)}catch{return!0}}function wg(e){var t=sn(e,ce);t!==null&&gt(t,e,ce,Ge)}function bc(e){var t=ba();typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e;var n={pending:null,interleaved:null,lanes:N,dispatch:null,lastRenderedReducer:Bv,lastRenderedState:e};t.queue=n;var a=n.dispatch=T0.bind(null,ze,n);return[t.memoizedState,a]}function Gv(e){return Yv(Bv)}function Qv(e){return Pv(Bv)}function uo(e,t,n,a){var r={tag:e,create:t,destroy:n,deps:a,next:null},i=ze.updateQueue;if(i===null)i=Eg(),ze.updateQueue=i,i.lastEffect=r.next=r;else{var l=i.lastEffect;if(l===null)i.lastEffect=r.next=r;else{var u=l.next;l.next=r,r.next=u,i.lastEffect=r}}return r}function Wv(e){var t=ba();{var n={current:e};return t.memoizedState=n,n}}function Sc(e){var t=zn();return t.memoizedState}function oo(e,t,n,a){var r=ba(),i=a===void 0?null:a;ze.flags|=e,r.memoizedState=uo(ft|t,n,void 0,i)}function Cc(e,t,n,a){var r=zn(),i=a===void 0?null:a,l=void 0;if(vt!==null){var u=vt.memoizedState;if(l=u.destroy,i!==null){var o=u.deps;if(Vv(i,o)){r.memoizedState=uo(t,n,l,i);return}}}ze.flags|=e,r.memoizedState=uo(ft|t,n,l,i)}function Ec(e,t){return(ze.mode&pa)!==re?oo(fs|Pn|qf,Mt,e,t):oo(Pn|qf,Mt,e,t)}function so(e,t){return Cc(Pn,Mt,e,t)}function Iv(e,t){return oo(De,ga,e,t)}function Rc(e,t){return Cc(De,ga,e,t)}function Xv(e,t){var n=De;return n|=ai,(ze.mode&pa)!==re&&(n|=Fa),oo(n,dt,e,t)}function Tc(e,t){return Cc(De,dt,e,t)}function Og(e,t){if(typeof t=="function"){var n=t,a=e();return n(a),function(){n(null)}}else if(t!=null){var r=t;r.hasOwnProperty("current")||f("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(r).join(", ")+"}");var i=e();return r.current=i,function(){r.current=null}}}function Kv(e,t,n){typeof t!="function"&&f("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null,r=De;return r|=ai,(ze.mode&pa)!==re&&(r|=Fa),oo(r,dt,Og.bind(null,t,e),a)}function xc(e,t,n){typeof t!="function"&&f("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null;return Cc(De,dt,Og.bind(null,t,e),a)}function S0(e,t){}var Dc=S0;function Jv(e,t){var n=ba(),a=t===void 0?null:t;return n.memoizedState=[e,a],e}function wc(e,t){var n=zn(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(Vv(a,i))return r[0]}return n.memoizedState=[e,a],e}function Zv(e,t){var n=ba(),a=t===void 0?null:t,r=e();return n.memoizedState=[r,a],r}function Oc(e,t){var n=zn(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(Vv(a,i))return r[0]}var l=e();return n.memoizedState=[l,a],l}function ep(e){var t=ba();return t.memoizedState=e,e}function _g(e){var t=zn(),n=vt,a=n.memoizedState;return Mg(t,a,e)}function Lg(e){var t=zn();if(vt===null)return t.memoizedState=e,e;var n=vt.memoizedState;return Mg(t,n,e)}function Mg(e,t,n){var a=!iR(Si);if(a){if(!Tn(n,t)){var r=wm();ze.lanes=he(ze.lanes,r),Oo(r),e.baseState=!0}return t}else return e.baseState&&(e.baseState=!1,mo()),e.memoizedState=n,n}function C0(e,t,n){var a=Gn();Dt(pR(a,Ba)),e(!0);var r=ro.transition;ro.transition={};var i=ro.transition;ro.transition._updatedFibers=new Set;try{e(!1),t()}finally{if(Dt(a),ro.transition=r,r===null&&i._updatedFibers){var l=i._updatedFibers.size;l>10&&T("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),i._updatedFibers.clear()}}}function tp(){var e=bc(!1),t=e[0],n=e[1],a=C0.bind(null,n),r=ba();return r.memoizedState=a,[t,a]}function Ug(){var e=Gv(),t=e[0],n=zn(),a=n.memoizedState;return[t,a]}function Ag(){var e=Qv(),t=e[0],n=zn(),a=n.memoizedState;return[t,a]}var Ng=!1;function E0(){return Ng}function np(){var e=ba(),t=Ic(),n=t.identifierPrefix,a;if(Lt()){var r=jD();a=":"+n+"R"+r;var i=lo++;i>0&&(a+="H"+i.toString(32)),a+=":"}else{var l=y0++;a=":"+n+"r"+l.toString(32)+":"}return e.memoizedState=a,a}function _c(){var e=zn(),t=e.memoizedState;return t}function R0(e,t,n){typeof arguments[3]=="function"&&f("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(kg(e))zg(t,r);else{var i=cg(e,t,r,a);if(i!==null){var l=en();gt(i,e,a,l),Hg(i,t,a)}}Fg(e,a)}function T0(e,t,n){typeof arguments[3]=="function"&&f("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(kg(e))zg(t,r);else{var i=e.alternate;if(e.lanes===N&&(i===null||i.lanes===N)){var l=t.lastRenderedReducer;if(l!==null){var u;u=Q.current,Q.current=Kn;try{var o=t.lastRenderedState,d=l(o,n);if(r.hasEagerState=!0,r.eagerState=d,Tn(d,o)){o0(e,t,r,a);return}}catch{}finally{Q.current=u}}}var v=cg(e,t,r,a);if(v!==null){var b=en();gt(v,e,a,b),Hg(v,t,a)}}Fg(e,a)}function kg(e){var t=e.alternate;return e===ze||t!==null&&t===ze}function zg(e,t){io=yc=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hg(e,t,n){if(Dm(n)){var a=t.lanes;a=Om(a,e.pendingLanes);var r=he(a,n);t.lanes=r,Cd(e,r)}}function Fg(e,t,n){Xf(e,t)}var Lc={readContext:st,useCallback:Pt,useContext:Pt,useEffect:Pt,useImperativeHandle:Pt,useInsertionEffect:Pt,useLayoutEffect:Pt,useMemo:Pt,useReducer:Pt,useRef:Pt,useState:Pt,useDebugValue:Pt,useDeferredValue:Pt,useTransition:Pt,useMutableSource:Pt,useSyncExternalStore:Pt,useId:Pt,unstable_isNewReconciler:an},jg=null,Vg=null,Bg=null,$g=null,Sa=null,Kn=null,Mc=null;{var ap=function(){f("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")},fe=function(){f("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://reactjs.org/link/rules-of-hooks")};jg={readContext:function(e){return st(e)},useCallback:function(e,t){return A="useCallback",Ae(),Tl(t),Jv(e,t)},useContext:function(e){return A="useContext",Ae(),st(e)},useEffect:function(e,t){return A="useEffect",Ae(),Tl(t),Ec(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",Ae(),Tl(n),Kv(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",Ae(),Tl(t),Iv(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",Ae(),Tl(t),Xv(e,t)},useMemo:function(e,t){A="useMemo",Ae(),Tl(t);var n=Q.current;Q.current=Sa;try{return Zv(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",Ae();var a=Q.current;Q.current=Sa;try{return $v(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",Ae(),Wv(e)},useState:function(e){A="useState",Ae();var t=Q.current;Q.current=Sa;try{return bc(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",Ae(),void 0},useDeferredValue:function(e){return A="useDeferredValue",Ae(),ep(e)},useTransition:function(){return A="useTransition",Ae(),tp()},useMutableSource:function(e,t,n){return A="useMutableSource",Ae(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",Ae(),qv(e,t,n)},useId:function(){return A="useId",Ae(),np()},unstable_isNewReconciler:an},Vg={readContext:function(e){return st(e)},useCallback:function(e,t){return A="useCallback",$(),Jv(e,t)},useContext:function(e){return A="useContext",$(),st(e)},useEffect:function(e,t){return A="useEffect",$(),Ec(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",$(),Kv(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",$(),Iv(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",$(),Xv(e,t)},useMemo:function(e,t){A="useMemo",$();var n=Q.current;Q.current=Sa;try{return Zv(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",$();var a=Q.current;Q.current=Sa;try{return $v(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",$(),Wv(e)},useState:function(e){A="useState",$();var t=Q.current;Q.current=Sa;try{return bc(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",$(),void 0},useDeferredValue:function(e){return A="useDeferredValue",$(),ep(e)},useTransition:function(){return A="useTransition",$(),tp()},useMutableSource:function(e,t,n){return A="useMutableSource",$(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",$(),qv(e,t,n)},useId:function(){return A="useId",$(),np()},unstable_isNewReconciler:an},Bg={readContext:function(e){return st(e)},useCallback:function(e,t){return A="useCallback",$(),wc(e,t)},useContext:function(e){return A="useContext",$(),st(e)},useEffect:function(e,t){return A="useEffect",$(),so(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",$(),xc(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",$(),Rc(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",$(),Tc(e,t)},useMemo:function(e,t){A="useMemo",$();var n=Q.current;Q.current=Kn;try{return Oc(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",$();var a=Q.current;Q.current=Kn;try{return Yv(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",$(),Sc()},useState:function(e){A="useState",$();var t=Q.current;Q.current=Kn;try{return Gv(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",$(),Dc()},useDeferredValue:function(e){return A="useDeferredValue",$(),_g(e)},useTransition:function(){return A="useTransition",$(),Ug()},useMutableSource:function(e,t,n){return A="useMutableSource",$(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",$(),gc(e,t)},useId:function(){return A="useId",$(),_c()},unstable_isNewReconciler:an},$g={readContext:function(e){return st(e)},useCallback:function(e,t){return A="useCallback",$(),wc(e,t)},useContext:function(e){return A="useContext",$(),st(e)},useEffect:function(e,t){return A="useEffect",$(),so(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",$(),xc(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",$(),Rc(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",$(),Tc(e,t)},useMemo:function(e,t){A="useMemo",$();var n=Q.current;Q.current=Mc;try{return Oc(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",$();var a=Q.current;Q.current=Mc;try{return Pv(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",$(),Sc()},useState:function(e){A="useState",$();var t=Q.current;Q.current=Mc;try{return Qv(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",$(),Dc()},useDeferredValue:function(e){return A="useDeferredValue",$(),Lg(e)},useTransition:function(){return A="useTransition",$(),Ag()},useMutableSource:function(e,t,n){return A="useMutableSource",$(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",$(),gc(e,t)},useId:function(){return A="useId",$(),_c()},unstable_isNewReconciler:an},Sa={readContext:function(e){return ap(),st(e)},useCallback:function(e,t){return A="useCallback",fe(),Ae(),Jv(e,t)},useContext:function(e){return A="useContext",fe(),Ae(),st(e)},useEffect:function(e,t){return A="useEffect",fe(),Ae(),Ec(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",fe(),Ae(),Kv(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",fe(),Ae(),Iv(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",fe(),Ae(),Xv(e,t)},useMemo:function(e,t){A="useMemo",fe(),Ae();var n=Q.current;Q.current=Sa;try{return Zv(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",fe(),Ae();var a=Q.current;Q.current=Sa;try{return $v(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",fe(),Ae(),Wv(e)},useState:function(e){A="useState",fe(),Ae();var t=Q.current;Q.current=Sa;try{return bc(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",fe(),Ae(),void 0},useDeferredValue:function(e){return A="useDeferredValue",fe(),Ae(),ep(e)},useTransition:function(){return A="useTransition",fe(),Ae(),tp()},useMutableSource:function(e,t,n){return A="useMutableSource",fe(),Ae(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",fe(),Ae(),qv(e,t,n)},useId:function(){return A="useId",fe(),Ae(),np()},unstable_isNewReconciler:an},Kn={readContext:function(e){return ap(),st(e)},useCallback:function(e,t){return A="useCallback",fe(),$(),wc(e,t)},useContext:function(e){return A="useContext",fe(),$(),st(e)},useEffect:function(e,t){return A="useEffect",fe(),$(),so(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",fe(),$(),xc(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",fe(),$(),Rc(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",fe(),$(),Tc(e,t)},useMemo:function(e,t){A="useMemo",fe(),$();var n=Q.current;Q.current=Kn;try{return Oc(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",fe(),$();var a=Q.current;Q.current=Kn;try{return Yv(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",fe(),$(),Sc()},useState:function(e){A="useState",fe(),$();var t=Q.current;Q.current=Kn;try{return Gv(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",fe(),$(),Dc()},useDeferredValue:function(e){return A="useDeferredValue",fe(),$(),_g(e)},useTransition:function(){return A="useTransition",fe(),$(),Ug()},useMutableSource:function(e,t,n){return A="useMutableSource",fe(),$(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",fe(),$(),gc(e,t)},useId:function(){return A="useId",fe(),$(),_c()},unstable_isNewReconciler:an},Mc={readContext:function(e){return ap(),st(e)},useCallback:function(e,t){return A="useCallback",fe(),$(),wc(e,t)},useContext:function(e){return A="useContext",fe(),$(),st(e)},useEffect:function(e,t){return A="useEffect",fe(),$(),so(e,t)},useImperativeHandle:function(e,t,n){return A="useImperativeHandle",fe(),$(),xc(e,t,n)},useInsertionEffect:function(e,t){return A="useInsertionEffect",fe(),$(),Rc(e,t)},useLayoutEffect:function(e,t){return A="useLayoutEffect",fe(),$(),Tc(e,t)},useMemo:function(e,t){A="useMemo",fe(),$();var n=Q.current;Q.current=Kn;try{return Oc(e,t)}finally{Q.current=n}},useReducer:function(e,t,n){A="useReducer",fe(),$();var a=Q.current;Q.current=Kn;try{return Pv(e,t,n)}finally{Q.current=a}},useRef:function(e){return A="useRef",fe(),$(),Sc()},useState:function(e){A="useState",fe(),$();var t=Q.current;Q.current=Kn;try{return Qv(e)}finally{Q.current=t}},useDebugValue:function(e,t){return A="useDebugValue",fe(),$(),Dc()},useDeferredValue:function(e){return A="useDeferredValue",fe(),$(),Lg(e)},useTransition:function(){return A="useTransition",fe(),$(),Ag()},useMutableSource:function(e,t,n){return A="useMutableSource",fe(),$(),void 0},useSyncExternalStore:function(e,t,n){return A="useSyncExternalStore",fe(),$(),gc(e,t)},useId:function(){return A="useId",fe(),$(),_c()},unstable_isNewReconciler:an}}var Ur=p.unstable_now,Yg=0,Uc=-1,co=-1,Ac=-1,rp=!1,Nc=!1;function Pg(){return rp}function x0(){Nc=!0}function D0(){rp=!1,Nc=!1}function w0(){rp=Nc,Nc=!1}function qg(){return Yg}function Gg(){Yg=Ur()}function ip(e){co=Ur(),e.actualStartTime<0&&(e.actualStartTime=Ur())}function Qg(e){co=-1}function kc(e,t){if(co>=0){var n=Ur()-co;e.actualDuration+=n,t&&(e.selfBaseDuration=n),co=-1}}function Ca(e){if(Uc>=0){var t=Ur()-Uc;Uc=-1;for(var n=e.return;n!==null;){switch(n.tag){case M:var a=n.stateNode;a.effectDuration+=t;return;case ve:var r=n.stateNode;r.effectDuration+=t;return}n=n.return}}}function lp(e){if(Ac>=0){var t=Ur()-Ac;Ac=-1;for(var n=e.return;n!==null;){switch(n.tag){case M:var a=n.stateNode;a!==null&&(a.passiveEffectDuration+=t);return;case ve:var r=n.stateNode;r!==null&&(r.passiveEffectDuration+=t);return}n=n.return}}}function Ea(){Uc=Ur()}function up(){Ac=Ur()}function op(e){for(var t=e.child;t;)e.actualDuration+=t.actualDuration,t=t.sibling}function Jn(e,t){if(e&&e.defaultProps){var n=Se({},t),a=e.defaultProps;for(var r in a)n[r]===void 0&&(n[r]=a[r]);return n}return t}var sp={},cp,fp,dp,vp,pp,Wg,zc,hp,mp,yp,fo;{cp=new Set,fp=new Set,dp=new Set,vp=new Set,hp=new Set,pp=new Set,mp=new Set,yp=new Set,fo=new Set;var Ig=new Set;zc=function(e,t){if(!(e===null||typeof e=="function")){var n=t+"_"+e;Ig.has(n)||(Ig.add(n),f("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e))}},Wg=function(e,t){if(t===void 0){var n=Oe(e)||"Component";pp.has(n)||(pp.add(n),f("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))}},Object.defineProperty(sp,"_processChildContext",{enumerable:!1,value:function(){throw new Error("_processChildContext is not available in React 16+. This likely means you have multiple copies of React and are attempting to nest a React 15 tree inside a React 16 tree using unstable_renderSubtreeIntoContainer, which isn't supported. Try to make sure you have only one copy of React (and ideally, switch to ReactDOM.createPortal).")}}),Object.freeze(sp)}function gp(e,t,n,a){var r=e.memoizedState,i=n(a,r);{if(e.mode&at){Tt(!0);try{i=n(a,r)}finally{Tt(!1)}}Wg(t,i)}var l=i==null?r:Se({},r,i);if(e.memoizedState=l,e.lanes===N){var u=e.updateQueue;u.baseState=l}}var bp={isMounted:gE,enqueueSetState:function(e,t,n){var a=Wi(e),r=en(),i=zr(a),l=Wa(r,i);l.payload=t,n!=null&&(zc(n,"setState"),l.callback=n);var u=Or(a,l,i);u!==null&&(gt(u,a,i,r),fc(u,a,i)),Xf(a,i)},enqueueReplaceState:function(e,t,n){var a=Wi(e),r=en(),i=zr(a),l=Wa(r,i);l.tag=dg,l.payload=t,n!=null&&(zc(n,"replaceState"),l.callback=n);var u=Or(a,l,i);u!==null&&(gt(u,a,i,r),fc(u,a,i)),Xf(a,i)},enqueueForceUpdate:function(e,t){var n=Wi(e),a=en(),r=zr(n),i=Wa(a,r);i.tag=oc,t!=null&&(zc(t,"forceUpdate"),i.callback=t);var l=Or(n,i,r);l!==null&&(gt(l,n,r,a),fc(l,n,r)),IE(n,r)}};function Xg(e,t,n,a,r,i,l){var u=e.stateNode;if(typeof u.shouldComponentUpdate=="function"){var o=u.shouldComponentUpdate(a,i,l);{if(e.mode&at){Tt(!0);try{o=u.shouldComponentUpdate(a,i,l)}finally{Tt(!1)}}o===void 0&&f("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",Oe(t)||"Component")}return o}return t.prototype&&t.prototype.isPureReactComponent?!Au(n,a)||!Au(r,i):!0}function O0(e,t,n){var a=e.stateNode;{var r=Oe(t)||"Component",i=a.render;i||(t.prototype&&typeof t.prototype.render=="function"?f("%s(...): No `render` method found on the returned component instance: did you accidentally return an object from the constructor?",r):f("%s(...): No `render` method found on the returned component instance: you may have forgotten to define `render`.",r)),a.getInitialState&&!a.getInitialState.isReactClassApproved&&!a.state&&f("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",r),a.getDefaultProps&&!a.getDefaultProps.isReactClassApproved&&f("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",r),a.propTypes&&f("propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.",r),a.contextType&&f("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",r),t.childContextTypes&&!fo.has(t)&&(e.mode&at)===re&&(fo.add(t),f(`%s uses the legacy childContextTypes API which is no longer supported and will be removed in the next major release. Use React.createContext() instead

.Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)),t.contextTypes&&!fo.has(t)&&(e.mode&at)===re&&(fo.add(t),f(`%s uses the legacy contextTypes API which is no longer supported and will be removed in the next major release. Use React.createContext() with static contextType instead.

Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)),a.contextTypes&&f("contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.",r),t.contextType&&t.contextTypes&&!mp.has(t)&&(mp.add(t),f("%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.",r)),typeof a.componentShouldUpdate=="function"&&f("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",r),t.prototype&&t.prototype.isPureReactComponent&&typeof a.shouldComponentUpdate<"u"&&f("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",Oe(t)||"A pure component"),typeof a.componentDidUnmount=="function"&&f("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",r),typeof a.componentDidReceiveProps=="function"&&f("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",r),typeof a.componentWillRecieveProps=="function"&&f("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",r),typeof a.UNSAFE_componentWillRecieveProps=="function"&&f("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",r);var l=a.props!==n;a.props!==void 0&&l&&f("%s(...): When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",r,r),a.defaultProps&&f("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",r,r),typeof a.getSnapshotBeforeUpdate=="function"&&typeof a.componentDidUpdate!="function"&&!dp.has(t)&&(dp.add(t),f("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",Oe(t))),typeof a.getDerivedStateFromProps=="function"&&f("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof a.getDerivedStateFromError=="function"&&f("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof t.getSnapshotBeforeUpdate=="function"&&f("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",r);var u=a.state;u&&(typeof u!="object"||Me(u))&&f("%s.state: must be set to an object or null",r),typeof a.getChildContext=="function"&&typeof t.childContextTypes!="object"&&f("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",r)}}function Kg(e,t){t.updater=bp,e.stateNode=t,vE(t,e),t._reactInternalInstance=sp}function Jg(e,t,n){var a=!1,r=xn,i=xn,l=t.contextType;if("contextType"in t){var u=l===null||l!==void 0&&l.$$typeof===Bl&&l._context===void 0;if(!u&&!yp.has(t)){yp.add(t);var o="";l===void 0?o=" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":typeof l!="object"?o=" However, it is set to a "+typeof l+".":l.$$typeof===Ni?o=" Did you accidentally pass the Context.Provider instead?":l._context!==void 0?o=" Did you accidentally pass the Context.Consumer instead?":o=" However, it is set to an object with keys {"+Object.keys(l).join(", ")+"}.",f("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",Oe(t)||"Component",o)}}if(typeof l=="object"&&l!==null)i=st(l);else{r=dl(e,t,!0);var d=t.contextTypes;a=d!=null,i=a?vl(e,r):xn}var v=new t(n,i);if(e.mode&at){Tt(!0);try{v=new t(n,i)}finally{Tt(!1)}}var b=e.memoizedState=v.state!==null&&v.state!==void 0?v.state:null;Kg(e,v);{if(typeof t.getDerivedStateFromProps=="function"&&b===null){var g=Oe(t)||"Component";fp.has(g)||(fp.add(g),f("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",g,v.state===null?"null":"undefined",g))}if(typeof t.getDerivedStateFromProps=="function"||typeof v.getSnapshotBeforeUpdate=="function"){var R=null,D=null,_=null;if(typeof v.componentWillMount=="function"&&v.componentWillMount.__suppressDeprecationWarning!==!0?R="componentWillMount":typeof v.UNSAFE_componentWillMount=="function"&&(R="UNSAFE_componentWillMount"),typeof v.componentWillReceiveProps=="function"&&v.componentWillReceiveProps.__suppressDeprecationWarning!==!0?D="componentWillReceiveProps":typeof v.UNSAFE_componentWillReceiveProps=="function"&&(D="UNSAFE_componentWillReceiveProps"),typeof v.componentWillUpdate=="function"&&v.componentWillUpdate.__suppressDeprecationWarning!==!0?_="componentWillUpdate":typeof v.UNSAFE_componentWillUpdate=="function"&&(_="UNSAFE_componentWillUpdate"),R!==null||D!==null||_!==null){var Y=Oe(t)||"Component",te=typeof t.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";vp.has(Y)||(vp.add(Y),f(`Unsafe legacy lifecycles will not be called for components using new component APIs.

%s uses %s but also contains the following legacy lifecycles:%s%s%s

The above lifecycles should be removed. Learn more about this warning here:
https://reactjs.org/link/unsafe-component-lifecycles`,Y,te,R!==null?`
  `+R:"",D!==null?`
  `+D:"",_!==null?`
  `+_:""))}}}return a&&By(e,r,i),v}function _0(e,t){var n=t.state;typeof t.componentWillMount=="function"&&t.componentWillMount(),typeof t.UNSAFE_componentWillMount=="function"&&t.UNSAFE_componentWillMount(),n!==t.state&&(f("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",pe(e)||"Component"),bp.enqueueReplaceState(t,t.state,null))}function Zg(e,t,n,a){var r=t.state;if(typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==r){{var i=pe(e)||"Component";cp.has(i)||(cp.add(i),f("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",i))}bp.enqueueReplaceState(t,t.state,null)}}function Sp(e,t,n,a){O0(e,t,n);var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},_v(e);var i=t.contextType;if(typeof i=="object"&&i!==null)r.context=st(i);else{var l=dl(e,t,!0);r.context=vl(e,l)}{if(r.state===n){var u=Oe(t)||"Component";hp.has(u)||(hp.add(u),f("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",u))}e.mode&at&&In.recordLegacyContextWarning(e,r),In.recordUnsafeLifecycleWarnings(e,r)}r.state=e.memoizedState;var o=t.getDerivedStateFromProps;if(typeof o=="function"&&(gp(e,t,o,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps!="function"&&typeof r.getSnapshotBeforeUpdate!="function"&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(_0(e,r),dc(e,n,r,a),r.state=e.memoizedState),typeof r.componentDidMount=="function"){var d=De;d|=ai,(e.mode&pa)!==re&&(d|=Fa),e.flags|=d}}function L0(e,t,n,a){var r=e.stateNode,i=e.memoizedProps;r.props=i;var l=r.context,u=t.contextType,o=xn;if(typeof u=="object"&&u!==null)o=st(u);else{var d=dl(e,t,!0);o=vl(e,d)}var v=t.getDerivedStateFromProps,b=typeof v=="function"||typeof r.getSnapshotBeforeUpdate=="function";!b&&(typeof r.UNSAFE_componentWillReceiveProps=="function"||typeof r.componentWillReceiveProps=="function")&&(i!==n||l!==o)&&Zg(e,r,n,o),pg();var g=e.memoizedState,R=r.state=g;if(dc(e,n,r,a),R=e.memoizedState,i===n&&g===R&&!Qs()&&!vc()){if(typeof r.componentDidMount=="function"){var D=De;D|=ai,(e.mode&pa)!==re&&(D|=Fa),e.flags|=D}return!1}typeof v=="function"&&(gp(e,t,v,n),R=e.memoizedState);var _=vc()||Xg(e,t,i,n,g,R,o);if(_){if(!b&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"){var Y=De;Y|=ai,(e.mode&pa)!==re&&(Y|=Fa),e.flags|=Y}}else{if(typeof r.componentDidMount=="function"){var te=De;te|=ai,(e.mode&pa)!==re&&(te|=Fa),e.flags|=te}e.memoizedProps=n,e.memoizedState=R}return r.props=n,r.state=R,r.context=o,_}function M0(e,t,n,a,r){var i=t.stateNode;vg(e,t);var l=t.memoizedProps,u=t.type===t.elementType?l:Jn(t.type,l);i.props=u;var o=t.pendingProps,d=i.context,v=n.contextType,b=xn;if(typeof v=="object"&&v!==null)b=st(v);else{var g=dl(t,n,!0);b=vl(t,g)}var R=n.getDerivedStateFromProps,D=typeof R=="function"||typeof i.getSnapshotBeforeUpdate=="function";!D&&(typeof i.UNSAFE_componentWillReceiveProps=="function"||typeof i.componentWillReceiveProps=="function")&&(l!==o||d!==b)&&Zg(t,i,a,b),pg();var _=t.memoizedState,Y=i.state=_;if(dc(t,a,i,r),Y=t.memoizedState,l===o&&_===Y&&!Qs()&&!vc()&&!$r)return typeof i.componentDidUpdate=="function"&&(l!==e.memoizedProps||_!==e.memoizedState)&&(t.flags|=De),typeof i.getSnapshotBeforeUpdate=="function"&&(l!==e.memoizedProps||_!==e.memoizedState)&&(t.flags|=ti),!1;typeof R=="function"&&(gp(t,n,R,a),Y=t.memoizedState);var te=vc()||Xg(t,n,u,a,_,Y,b)||$r;return te?(!D&&(typeof i.UNSAFE_componentWillUpdate=="function"||typeof i.componentWillUpdate=="function")&&(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,Y,b),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,Y,b)),typeof i.componentDidUpdate=="function"&&(t.flags|=De),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=ti)):(typeof i.componentDidUpdate=="function"&&(l!==e.memoizedProps||_!==e.memoizedState)&&(t.flags|=De),typeof i.getSnapshotBeforeUpdate=="function"&&(l!==e.memoizedProps||_!==e.memoizedState)&&(t.flags|=ti),t.memoizedProps=a,t.memoizedState=Y),i.props=a,i.state=Y,i.context=b,te}function Ci(e,t){return{value:e,source:t,stack:Jl(t),digest:null}}function Cp(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function U0(e,t){return!0}function Ep(e,t){try{var n=U0(e,t);if(n===!1)return;var a=t.value,r=t.source,i=t.stack,l=i!==null?i:"";if(a!=null&&a._suppressLogging){if(e.tag===w)return;console.error(a)}var u=r?pe(r):null,o=u?"The above error occurred in the <"+u+"> component:":"The above error occurred in one of your React components:",d;if(e.tag===M)d=`Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.`;else{var v=pe(e)||"Anonymous";d="React will try to recreate this component tree from scratch "+("using the error boundary you provided, "+v+".")}var b=o+`
`+l+`

`+(""+d);console.error(b)}catch(g){setTimeout(function(){throw g})}}var A0=typeof WeakMap=="function"?WeakMap:Map;function eb(e,t,n){var a=Wa(Ge,n);a.tag=wv,a.payload={element:null};var r=t.value;return a.callback=function(){xO(r),Ep(e,t)},a}function Rp(e,t,n){var a=Wa(Ge,n);a.tag=wv;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;a.payload=function(){return r(i)},a.callback=function(){fS(e),Ep(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(a.callback=function(){fS(e),Ep(e,t),typeof r!="function"&&RO(this);var o=t.value,d=t.stack;this.componentDidCatch(o,{componentStack:d!==null?d:""}),typeof r!="function"&&(Cn(e.lanes,ce)||f("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",pe(e)||"Unknown"))}),a}function tb(e,t,n){var a=e.pingCache,r;if(a===null?(a=e.pingCache=new A0,r=new Set,a.set(t,r)):(r=a.get(t),r===void 0&&(r=new Set,a.set(t,r))),!r.has(n)){r.add(n);var i=DO.bind(null,e,t,n);qn&&_o(e,n),t.then(i,i)}}function N0(e,t,n,a){var r=e.updateQueue;if(r===null){var i=new Set;i.add(n),e.updateQueue=i}else r.add(n)}function k0(e,t){var n=e.tag;if((e.mode&Re)===re&&(n===U||n===Z||n===Ce)){var a=e.alternate;a?(e.updateQueue=a.updateQueue,e.memoizedState=a.memoizedState,e.lanes=a.lanes):(e.updateQueue=null,e.memoizedState=null)}}function nb(e){var t=e;do{if(t.tag===ie&&h0(t))return t;t=t.return}while(t!==null);return null}function ab(e,t,n,a,r){if((e.mode&Re)===re){if(e===t)e.flags|=Kt;else{if(e.flags|=Ue,n.flags|=Yf,n.flags&=~(pE|cu),n.tag===w){var i=n.alternate;if(i===null)n.tag=vn;else{var l=Wa(Ge,ce);l.tag=oc,Or(n,l,ce)}}n.lanes=he(n.lanes,ce)}return e}return e.flags|=Kt,e.lanes=r,e}function z0(e,t,n,a,r){if(n.flags|=cu,qn&&_o(e,r),a!==null&&typeof a=="object"&&typeof a.then=="function"){var i=a;k0(n),Lt()&&n.mode&Re&&Wy();var l=nb(t);if(l!==null){l.flags&=~za,ab(l,t,n,e,r),l.mode&Re&&tb(e,i,r),N0(l,e,i);return}else{if(!rR(r)){tb(e,i,r),nh();return}var u=new Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");a=u}}else if(Lt()&&n.mode&Re){Wy();var o=nb(t);if(o!==null){(o.flags&Kt)===le&&(o.flags|=za),ab(o,t,n,e,r),hv(Ci(a,n));return}}a=Ci(a,n),hO(a);var d=t;do{switch(d.tag){case M:{var v=a;d.flags|=Kt;var b=gu(r);d.lanes=he(d.lanes,b);var g=eb(d,v,b);Lv(d,g);return}case w:var R=a,D=d.type,_=d.stateNode;if((d.flags&Ue)===le&&(typeof D.getDerivedStateFromError=="function"||_!==null&&typeof _.componentDidCatch=="function"&&!nS(_))){d.flags|=Kt;var Y=gu(r);d.lanes=he(d.lanes,Y);var te=Rp(d,R,Y);Lv(d,te);return}break}d=d.return}while(d!==null)}function H0(){return null}var vo=m.ReactCurrentOwner,Zn=!1,Tp,po,xp,Dp,wp,Ei,Op,Hc,ho;Tp={},po={},xp={},Dp={},wp={},Ei=!1,Op={},Hc={},ho={};function Jt(e,t,n,a){e===null?t.child=lg(t,null,n,a):t.child=yl(t,e.child,n,a)}function F0(e,t,n,a){t.child=yl(t,e.child,null,a),t.child=yl(t,null,n,a)}function rb(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&Qn(i,a,"prop",Oe(n))}var l=n.render,u=t.ref,o,d;bl(t,r),du(t);{if(vo.current=t,bn(!0),o=xl(e,t,l,a,u,r),d=Dl(),t.mode&at){Tt(!0);try{o=xl(e,t,l,a,u,r),d=Dl()}finally{Tt(!1)}}bn(!1)}return Ji(),e!==null&&!Zn?(Sg(e,t,r),Ia(e,t,r)):(Lt()&&d&&sv(t),t.flags|=Ii,Jt(e,t,o,r),t.child)}function ib(e,t,n,a,r){if(e===null){var i=n.type;if(YO(i)&&n.compare===null&&n.defaultProps===void 0){var l=i;return l=Nl(i),t.tag=Ce,t.type=l,Mp(t,i),lb(e,t,l,a,r)}{var u=i.propTypes;if(u&&Qn(u,a,"prop",Oe(i)),n.defaultProps!==void 0){var o=Oe(i)||"Unknown";ho[o]||(f("%s: Support for defaultProps will be removed from memo components in a future major release. Use JavaScript default parameters instead.",o),ho[o]=!0)}}var d=vh(n.type,null,a,t,t.mode,r);return d.ref=t.ref,d.return=t,t.child=d,d}{var v=n.type,b=v.propTypes;b&&Qn(b,a,"prop",Oe(v))}var g=e.child,R=Hp(e,r);if(!R){var D=g.memoizedProps,_=n.compare;if(_=_!==null?_:Au,_(D,a)&&e.ref===t.ref)return Ia(e,t,r)}t.flags|=Ii;var Y=wi(g,a);return Y.ref=t.ref,Y.return=t,t.child=Y,Y}function lb(e,t,n,a,r){if(t.type!==t.elementType){var i=t.elementType;if(i.$$typeof===Ot){var l=i,u=l._payload,o=l._init;try{i=o(u)}catch{i=null}var d=i&&i.propTypes;d&&Qn(d,a,"prop",Oe(i))}}if(e!==null){var v=e.memoizedProps;if(Au(v,a)&&e.ref===t.ref&&t.type===e.type)if(Zn=!1,t.pendingProps=a=v,Hp(e,r))(e.flags&Yf)!==le&&(Zn=!0);else return t.lanes=e.lanes,Ia(e,t,r)}return _p(e,t,n,a,r)}function ub(e,t,n){var a=t.pendingProps,r=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"||Da)if((t.mode&Re)===re){var l={baseLanes:N,cachePool:null,transitions:null};t.memoizedState=l,Xc(t,n)}else if(Cn(n,Sn)){var b={baseLanes:N,cachePool:null,transitions:null};t.memoizedState=b;var g=i!==null?i.baseLanes:n;Xc(t,g)}else{var u=null,o;if(i!==null){var d=i.baseLanes;o=he(d,n)}else o=n;t.lanes=t.childLanes=Sn;var v={baseLanes:o,cachePool:u,transitions:null};return t.memoizedState=v,t.updateQueue=null,Xc(t,o),null}else{var R;i!==null?(R=he(i.baseLanes,n),t.memoizedState=null):R=n,Xc(t,R)}return Jt(e,t,r,n),t.child}function j0(e,t,n){var a=t.pendingProps;return Jt(e,t,a,n),t.child}function V0(e,t,n){var a=t.pendingProps.children;return Jt(e,t,a,n),t.child}function B0(e,t,n){{t.flags|=De;{var a=t.stateNode;a.effectDuration=0,a.passiveEffectDuration=0}}var r=t.pendingProps,i=r.children;return Jt(e,t,i,n),t.child}function ob(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=mr,t.flags|=Pf)}function _p(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&Qn(i,a,"prop",Oe(n))}var l;{var u=dl(t,n,!0);l=vl(t,u)}var o,d;bl(t,r),du(t);{if(vo.current=t,bn(!0),o=xl(e,t,n,a,l,r),d=Dl(),t.mode&at){Tt(!0);try{o=xl(e,t,n,a,l,r),d=Dl()}finally{Tt(!1)}}bn(!1)}return Ji(),e!==null&&!Zn?(Sg(e,t,r),Ia(e,t,r)):(Lt()&&d&&sv(t),t.flags|=Ii,Jt(e,t,o,r),t.child)}function sb(e,t,n,a,r){{switch(r_(t)){case!1:{var i=t.stateNode,l=t.type,u=new l(t.memoizedProps,i.context),o=u.state;i.updater.enqueueSetState(i,o,null);break}case!0:{t.flags|=Ue,t.flags|=Kt;var d=new Error("Simulated error coming from DevTools"),v=gu(r);t.lanes=he(t.lanes,v);var b=Rp(t,Ci(d,t),v);Lv(t,b);break}}if(t.type!==t.elementType){var g=n.propTypes;g&&Qn(g,a,"prop",Oe(n))}}var R;ya(n)?(R=!0,Is(t)):R=!1,bl(t,r);var D=t.stateNode,_;D===null?(jc(e,t),Jg(t,n,a),Sp(t,n,a,r),_=!0):e===null?_=L0(t,n,a,r):_=M0(e,t,n,a,r);var Y=Lp(e,t,n,_,R,r);{var te=t.stateNode;_&&te.props!==a&&(Ei||f("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",pe(t)||"a component"),Ei=!0)}return Y}function Lp(e,t,n,a,r,i){ob(e,t);var l=(t.flags&Ue)!==le;if(!a&&!l)return r&&Py(t,n,!1),Ia(e,t,i);var u=t.stateNode;vo.current=t;var o;if(l&&typeof n.getDerivedStateFromError!="function")o=null,Qg();else{du(t);{if(bn(!0),o=u.render(),t.mode&at){Tt(!0);try{u.render()}finally{Tt(!1)}}bn(!1)}Ji()}return t.flags|=Ii,e!==null&&l?F0(e,t,o,i):Jt(e,t,o,i),t.memoizedState=u.state,r&&Py(t,n,!0),t.child}function cb(e){var t=e.stateNode;t.pendingContext?$y(e,t.pendingContext,t.pendingContext!==t.context):t.context&&$y(e,t.context,!1),Mv(e,t.containerInfo)}function $0(e,t,n){if(cb(t),e===null)throw new Error("Should have a current fiber. This is a bug in React.");var a=t.pendingProps,r=t.memoizedState,i=r.element;vg(e,t),dc(t,a,null,n);var l=t.memoizedState;t.stateNode;var u=l.element;if(r.isDehydrated){var o={element:u,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},d=t.updateQueue;if(d.baseState=o,t.memoizedState=o,t.flags&za){var v=Ci(new Error("There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering."),t);return fb(e,t,u,n,v)}else if(u!==i){var b=Ci(new Error("This root received an early update, before anything was able hydrate. Switched the entire root to client rendering."),t);return fb(e,t,u,n,b)}else{qD(t);var g=lg(t,null,u,n);t.child=g;for(var R=g;R;)R.flags=R.flags&~ot|Ha,R=R.sibling}}else{if(ml(),u===i)return Ia(e,t,n);Jt(e,t,u,n)}return t.child}function fb(e,t,n,a,r){return ml(),hv(r),t.flags|=za,Jt(e,t,n,a),t.child}function Y0(e,t,n){yg(t),e===null&&pv(t);var a=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,l=r.children,u=Id(a,r);return u?l=null:i!==null&&Id(a,i)&&(t.flags|=su),ob(e,t),Jt(e,t,l,n),t.child}function P0(e,t){return e===null&&pv(t),null}function q0(e,t,n,a){jc(e,t);var r=t.pendingProps,i=n,l=i._payload,u=i._init,o=u(l);t.type=o;var d=t.tag=PO(o),v=Jn(o,r),b;switch(d){case U:return Mp(t,o),t.type=o=Nl(o),b=_p(null,t,o,v,a),b;case w:return t.type=o=uh(o),b=sb(null,t,o,v,a),b;case Z:return t.type=o=oh(o),b=rb(null,t,o,v,a),b;case Be:{if(t.type!==t.elementType){var g=o.propTypes;g&&Qn(g,v,"prop",Oe(o))}return b=ib(null,t,o,Jn(o.type,v),a),b}}var R="";throw o!==null&&typeof o=="object"&&o.$$typeof===Ot&&(R=" Did you wrap a component in React.lazy() more than once?"),new Error("Element type is invalid. Received a promise that resolves to: "+o+". "+("Lazy element type must resolve to a class or function."+R))}function G0(e,t,n,a,r){jc(e,t),t.tag=w;var i;return ya(n)?(i=!0,Is(t)):i=!1,bl(t,r),Jg(t,n,a),Sp(t,n,a,r),Lp(null,t,n,!0,i,r)}function Q0(e,t,n,a){jc(e,t);var r=t.pendingProps,i;{var l=dl(t,n,!1);i=vl(t,l)}bl(t,a);var u,o;du(t);{if(n.prototype&&typeof n.prototype.render=="function"){var d=Oe(n)||"Unknown";Tp[d]||(f("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",d,d),Tp[d]=!0)}t.mode&at&&In.recordLegacyContextWarning(t,null),bn(!0),vo.current=t,u=xl(null,t,n,r,i,a),o=Dl(),bn(!1)}if(Ji(),t.flags|=Ii,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0){var v=Oe(n)||"Unknown";po[v]||(f("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",v,v,v),po[v]=!0)}if(typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0){{var b=Oe(n)||"Unknown";po[b]||(f("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",b,b,b),po[b]=!0)}t.tag=w,t.memoizedState=null,t.updateQueue=null;var g=!1;return ya(n)?(g=!0,Is(t)):g=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,_v(t),Kg(t,u),Sp(t,n,r,a),Lp(null,t,n,!0,g,a)}else{if(t.tag=U,t.mode&at){Tt(!0);try{u=xl(null,t,n,r,i,a),o=Dl()}finally{Tt(!1)}}return Lt()&&o&&sv(t),Jt(null,t,u,a),Mp(t,n),t.child}}function Mp(e,t){{if(t&&t.childContextTypes&&f("%s(...): childContextTypes cannot be defined on a function component.",t.displayName||t.name||"Component"),e.ref!==null){var n="",a=dr();a&&(n+=`

Check the render method of \``+a+"`.");var r=a||"",i=e._debugSource;i&&(r=i.fileName+":"+i.lineNumber),wp[r]||(wp[r]=!0,f("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s",n))}if(t.defaultProps!==void 0){var l=Oe(t)||"Unknown";ho[l]||(f("%s: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead.",l),ho[l]=!0)}if(typeof t.getDerivedStateFromProps=="function"){var u=Oe(t)||"Unknown";Dp[u]||(f("%s: Function components do not support getDerivedStateFromProps.",u),Dp[u]=!0)}if(typeof t.contextType=="object"&&t.contextType!==null){var o=Oe(t)||"Unknown";xp[o]||(f("%s: Function components do not support contextType.",o),xp[o]=!0)}}}var Up={dehydrated:null,treeContext:null,retryLane:xt};function Ap(e){return{baseLanes:e,cachePool:H0(),transitions:null}}function W0(e,t){var n=null;return{baseLanes:he(e.baseLanes,t),cachePool:n,transitions:e.transitions}}function I0(e,t,n,a){if(t!==null){var r=t.memoizedState;if(r===null)return!1}return Nv(e,ao)}function X0(e,t){return gs(e.childLanes,t)}function db(e,t,n){var a=t.pendingProps;i_(t)&&(t.flags|=Ue);var r=Xn.current,i=!1,l=(t.flags&Ue)!==le;if(l||I0(r,e)?(i=!0,t.flags&=~Ue):(e===null||e.memoizedState!==null)&&(r=p0(r,bg)),r=Cl(r),Lr(t,r),e===null){pv(t);var u=t.memoizedState;if(u!==null){var o=u.dehydrated;if(o!==null)return tw(t,o)}var d=a.children,v=a.fallback;if(i){var b=K0(t,d,v,n),g=t.child;return g.memoizedState=Ap(n),t.memoizedState=Up,b}else return Np(t,d)}else{var R=e.memoizedState;if(R!==null){var D=R.dehydrated;if(D!==null)return nw(e,t,l,a,D,R,n)}if(i){var _=a.fallback,Y=a.children,te=Z0(e,t,Y,_,n),ee=t.child,xe=e.child.memoizedState;return ee.memoizedState=xe===null?Ap(n):W0(xe,n),ee.childLanes=X0(e,n),t.memoizedState=Up,te}else{var ge=a.children,C=J0(e,t,ge,n);return t.memoizedState=null,C}}}function Np(e,t,n){var a=e.mode,r={mode:"visible",children:t},i=kp(r,a);return i.return=e,e.child=i,i}function K0(e,t,n,a){var r=e.mode,i=e.child,l={mode:"hidden",children:t},u,o;return(r&Re)===re&&i!==null?(u=i,u.childLanes=N,u.pendingProps=l,e.mode&ke&&(u.actualDuration=0,u.actualStartTime=-1,u.selfBaseDuration=0,u.treeBaseDuration=0),o=Fr(n,r,a,null)):(u=kp(l,r),o=Fr(n,r,a,null)),u.return=e,o.return=e,u.sibling=o,e.child=u,o}function kp(e,t,n){return vS(e,t,N,null)}function vb(e,t){return wi(e,t)}function J0(e,t,n,a){var r=e.child,i=r.sibling,l=vb(r,{mode:"visible",children:n});if((t.mode&Re)===re&&(l.lanes=a),l.return=t,l.sibling=null,i!==null){var u=t.deletions;u===null?(t.deletions=[i],t.flags|=ei):u.push(i)}return t.child=l,l}function Z0(e,t,n,a,r){var i=t.mode,l=e.child,u=l.sibling,o={mode:"hidden",children:n},d;if((i&Re)===re&&t.child!==l){var v=t.child;d=v,d.childLanes=N,d.pendingProps=o,t.mode&ke&&(d.actualDuration=0,d.actualStartTime=-1,d.selfBaseDuration=l.selfBaseDuration,d.treeBaseDuration=l.treeBaseDuration),t.deletions=null}else d=vb(l,o),d.subtreeFlags=l.subtreeFlags&ja;var b;return u!==null?b=wi(u,a):(b=Fr(a,i,r,null),b.flags|=ot),b.return=t,d.return=t,d.sibling=b,t.child=d,b}function Fc(e,t,n,a){a!==null&&hv(a),yl(t,e.child,null,n);var r=t.pendingProps,i=r.children,l=Np(t,i);return l.flags|=ot,t.memoizedState=null,l}function ew(e,t,n,a,r){var i=t.mode,l={mode:"visible",children:n},u=kp(l,i),o=Fr(a,i,r,null);return o.flags|=ot,u.return=t,o.return=t,u.sibling=o,t.child=u,(t.mode&Re)!==re&&yl(t,e.child,null,r),o}function tw(e,t,n){return(e.mode&Re)===re?(f("Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container) to ReactDOMClient.hydrateRoot(container, <App />).render(element) or remove the Suspense components from the server rendered components."),e.lanes=ce):Zd(t)?e.lanes=li:e.lanes=Sn,null}function nw(e,t,n,a,r,i,l){if(n)if(t.flags&za){t.flags&=~za;var C=Cp(new Error("There was an error while hydrating this Suspense boundary. Switched to client rendering."));return Fc(e,t,l,C)}else{if(t.memoizedState!==null)return t.child=e.child,t.flags|=Ue,null;var L=a.children,E=a.fallback,z=ew(e,t,L,E,l),W=t.child;return W.memoizedState=Ap(l),t.memoizedState=Up,z}else{if(YD(),(t.mode&Re)===re)return Fc(e,t,l,null);if(Zd(r)){var u,o,d;{var v=lD(r);u=v.digest,o=v.message,d=v.stack}var b;o?b=new Error(o):b=new Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.");var g=Cp(b,u,d);return Fc(e,t,l,g)}var R=Cn(l,e.childLanes);if(Zn||R){var D=Ic();if(D!==null){var _=dR(D,l);if(_!==xt&&_!==i.retryLane){i.retryLane=_;var Y=Ge;sn(e,_),gt(D,e,_,Y)}}nh();var te=Cp(new Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return Fc(e,t,l,te)}else if(zy(r)){t.flags|=Ue,t.child=e.child;var ee=wO.bind(null,e);return uD(r,ee),null}else{GD(t,r,i.treeContext);var xe=a.children,ge=Np(t,xe);return ge.flags|=Ha,ge}}}function pb(e,t,n){e.lanes=he(e.lanes,t);var a=e.alternate;a!==null&&(a.lanes=he(a.lanes,t)),xv(e.return,t,n)}function aw(e,t,n){for(var a=t;a!==null;){if(a.tag===ie){var r=a.memoizedState;r!==null&&pb(a,n,e)}else if(a.tag===qe)pb(a,n,e);else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;a=a.return}a.sibling.return=a.return,a=a.sibling}}function rw(e){for(var t=e,n=null;t!==null;){var a=t.alternate;a!==null&&mc(a)===null&&(n=t),t=t.sibling}return n}function iw(e){if(e!==void 0&&e!=="forwards"&&e!=="backwards"&&e!=="together"&&!Op[e])if(Op[e]=!0,typeof e=="string")switch(e.toLowerCase()){case"together":case"forwards":case"backwards":{f('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',e,e.toLowerCase());break}case"forward":case"backward":{f('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',e,e.toLowerCase());break}default:f('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e);break}else f('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}function lw(e,t){e!==void 0&&!Hc[e]&&(e!=="collapsed"&&e!=="hidden"?(Hc[e]=!0,f('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',e)):t!=="forwards"&&t!=="backwards"&&(Hc[e]=!0,f('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',e)))}function hb(e,t){{var n=Me(e),a=!n&&typeof La(e)=="function";if(n||a){var r=n?"array":"iterable";return f("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",r,t,r),!1}}return!0}function uw(e,t){if((t==="forwards"||t==="backwards")&&e!==void 0&&e!==null&&e!==!1)if(Me(e)){for(var n=0;n<e.length;n++)if(!hb(e[n],n))return}else{var a=La(e);if(typeof a=="function"){var r=a.call(e);if(r)for(var i=r.next(),l=0;!i.done;i=r.next()){if(!hb(i.value,l))return;l++}}else f('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',t)}}function zp(e,t,n,a,r){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=n,i.tailMode=r)}function mb(e,t,n){var a=t.pendingProps,r=a.revealOrder,i=a.tail,l=a.children;iw(r),lw(i,r),uw(l,r),Jt(e,t,l,n);var u=Xn.current,o=Nv(u,ao);if(o)u=kv(u,ao),t.flags|=Ue;else{var d=e!==null&&(e.flags&Ue)!==le;d&&aw(t,t.child,n),u=Cl(u)}if(Lr(t,u),(t.mode&Re)===re)t.memoizedState=null;else switch(r){case"forwards":{var v=rw(t.child),b;v===null?(b=t.child,t.child=null):(b=v.sibling,v.sibling=null),zp(t,!1,b,v,i);break}case"backwards":{var g=null,R=t.child;for(t.child=null;R!==null;){var D=R.alternate;if(D!==null&&mc(D)===null){t.child=R;break}var _=R.sibling;R.sibling=g,g=R,R=_}zp(t,!0,g,null,i);break}case"together":{zp(t,!1,null,null,void 0);break}default:t.memoizedState=null}return t.child}function ow(e,t,n){Mv(t,t.stateNode.containerInfo);var a=t.pendingProps;return e===null?t.child=yl(t,null,a,n):Jt(e,t,a,n),t.child}var yb=!1;function sw(e,t,n){var a=t.type,r=a._context,i=t.pendingProps,l=t.memoizedProps,u=i.value;{"value"in i||yb||(yb=!0,f("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?"));var o=t.type.propTypes;o&&Qn(o,i,"prop","Context.Provider")}if(sg(t,r,u),l!==null){var d=l.value;if(Tn(d,u)){if(l.children===i.children&&!Qs())return Ia(e,t,n)}else i0(t,r,n)}var v=i.children;return Jt(e,t,v,n),t.child}var gb=!1;function cw(e,t,n){var a=t.type;a._context===void 0?a!==a.Consumer&&(gb||(gb=!0,f("Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?"))):a=a._context;var r=t.pendingProps,i=r.children;typeof i!="function"&&f("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),bl(t,n);var l=st(a);du(t);var u;return vo.current=t,bn(!0),u=i(l),bn(!1),Ji(),t.flags|=Ii,Jt(e,t,u,n),t.child}function mo(){Zn=!0}function jc(e,t){(t.mode&Re)===re&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=ot)}function Ia(e,t,n){return e!==null&&(t.dependencies=e.dependencies),Qg(),Oo(t.lanes),Cn(n,t.childLanes)?(a0(e,t),t.child):null}function fw(e,t,n){{var a=t.return;if(a===null)throw new Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,t===a.child)a.child=n;else{var r=a.child;if(r===null)throw new Error("Expected parent to have a child.");for(;r.sibling!==t;)if(r=r.sibling,r===null)throw new Error("Expected to find the previous sibling.");r.sibling=n}var i=a.deletions;return i===null?(a.deletions=[e],a.flags|=ei):i.push(e),n.flags|=ot,n}}function Hp(e,t){var n=e.lanes;return!!Cn(n,t)}function dw(e,t,n){switch(t.tag){case M:cb(t),t.stateNode,ml();break;case B:yg(t);break;case w:{var a=t.type;ya(a)&&Is(t);break}case F:Mv(t,t.stateNode.containerInfo);break;case I:{var r=t.memoizedProps.value,i=t.type._context;sg(t,i,r);break}case ve:{var l=Cn(n,t.childLanes);l&&(t.flags|=De);{var u=t.stateNode;u.effectDuration=0,u.passiveEffectDuration=0}}break;case ie:{var o=t.memoizedState;if(o!==null){if(o.dehydrated!==null)return Lr(t,Cl(Xn.current)),t.flags|=Ue,null;var d=t.child,v=d.childLanes;if(Cn(n,v))return db(e,t,n);Lr(t,Cl(Xn.current));var b=Ia(e,t,n);return b!==null?b.sibling:null}else Lr(t,Cl(Xn.current));break}case qe:{var g=(e.flags&Ue)!==le,R=Cn(n,t.childLanes);if(g){if(R)return mb(e,t,n);t.flags|=Ue}var D=t.memoizedState;if(D!==null&&(D.rendering=null,D.tail=null,D.lastEffect=null),Lr(t,Xn.current),R)break;return null}case Fe:case Qe:return t.lanes=N,ub(e,t,n)}return Ia(e,t,n)}function bb(e,t,n){if(t._debugNeedsRemount&&e!==null)return fw(e,t,vh(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.lanes));if(e!==null){var a=e.memoizedProps,r=t.pendingProps;if(a!==r||Qs()||t.type!==e.type)Zn=!0;else{var i=Hp(e,n);if(!i&&(t.flags&Ue)===le)return Zn=!1,dw(e,t,n);(e.flags&Yf)!==le?Zn=!0:Zn=!1}}else if(Zn=!1,Lt()&&HD(t)){var l=t.index,u=FD();Qy(t,u,l)}switch(t.lanes=N,t.tag){case V:return Q0(e,t,t.type,n);case Ft:{var o=t.elementType;return q0(e,t,o,n)}case U:{var d=t.type,v=t.pendingProps,b=t.elementType===d?v:Jn(d,v);return _p(e,t,d,b,n)}case w:{var g=t.type,R=t.pendingProps,D=t.elementType===g?R:Jn(g,R);return sb(e,t,g,D,n)}case M:return $0(e,t,n);case B:return Y0(e,t,n);case J:return P0(e,t);case ie:return db(e,t,n);case F:return ow(e,t,n);case Z:{var _=t.type,Y=t.pendingProps,te=t.elementType===_?Y:Jn(_,Y);return rb(e,t,_,te,n)}case ye:return j0(e,t,n);case ne:return V0(e,t,n);case ve:return B0(e,t,n);case I:return sw(e,t,n);case we:return cw(e,t,n);case Be:{var ee=t.type,xe=t.pendingProps,ge=Jn(ee,xe);if(t.type!==t.elementType){var C=ee.propTypes;C&&Qn(C,ge,"prop",Oe(ee))}return ge=Jn(ee.type,ge),ib(e,t,ee,ge,n)}case Ce:return lb(e,t,t.type,t.pendingProps,n);case vn:{var L=t.type,E=t.pendingProps,z=t.elementType===L?E:Jn(L,E);return G0(e,t,L,z,n)}case qe:return mb(e,t,n);case ia:break;case Fe:return ub(e,t,n)}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function wl(e){e.flags|=De}function Sb(e){e.flags|=mr,e.flags|=Pf}var Cb,Fp,Eb,Rb;Cb=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===B||r.tag===J)Nx(e,r.stateNode);else if(r.tag!==F){if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Fp=function(e,t){},Eb=function(e,t,n,a,r){var i=e.memoizedProps;if(i!==a){var l=t.stateNode,u=Uv(),o=zx(l,n,i,a,r,u);t.updateQueue=o,o&&wl(t)}},Rb=function(e,t,n,a){n!==a&&wl(t)};function yo(e,t){if(!Lt())switch(e.tailMode){case"hidden":{for(var n=e.tail,a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e.tail=null:a.sibling=null;break}case"collapsed":{for(var r=e.tail,i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?!t&&e.tail!==null?e.tail.sibling=null:e.tail=null:i.sibling=null;break}}}function Ut(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=N,a=le;if(t){if((e.mode&ke)!==re){for(var o=e.selfBaseDuration,d=e.child;d!==null;)n=he(n,he(d.lanes,d.childLanes)),a|=d.subtreeFlags&ja,a|=d.flags&ja,o+=d.treeBaseDuration,d=d.sibling;e.treeBaseDuration=o}else for(var v=e.child;v!==null;)n=he(n,he(v.lanes,v.childLanes)),a|=v.subtreeFlags&ja,a|=v.flags&ja,v.return=e,v=v.sibling;e.subtreeFlags|=a}else{if((e.mode&ke)!==re){for(var r=e.actualDuration,i=e.selfBaseDuration,l=e.child;l!==null;)n=he(n,he(l.lanes,l.childLanes)),a|=l.subtreeFlags,a|=l.flags,r+=l.actualDuration,i+=l.treeBaseDuration,l=l.sibling;e.actualDuration=r,e.treeBaseDuration=i}else for(var u=e.child;u!==null;)n=he(n,he(u.lanes,u.childLanes)),a|=u.subtreeFlags,a|=u.flags,u.return=e,u=u.sibling;e.subtreeFlags|=a}return e.childLanes=n,t}function vw(e,t,n){if(KD()&&(t.mode&Re)!==re&&(t.flags&Ue)===le)return eg(t),ml(),t.flags|=za|cu|Kt,!1;var a=ec(t);if(n!==null&&n.dehydrated!==null)if(e===null){if(!a)throw new Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");if(ID(t),Ut(t),(t.mode&ke)!==re){var r=n!==null;if(r){var i=t.child;i!==null&&(t.treeBaseDuration-=i.treeBaseDuration)}}return!1}else{if(ml(),(t.flags&Ue)===le&&(t.memoizedState=null),t.flags|=De,Ut(t),(t.mode&ke)!==re){var l=n!==null;if(l){var u=t.child;u!==null&&(t.treeBaseDuration-=u.treeBaseDuration)}}return!1}else return tg(),!0}function Tb(e,t,n){var a=t.pendingProps;switch(cv(t),t.tag){case V:case Ft:case Ce:case U:case Z:case ye:case ne:case ve:case we:case Be:return Ut(t),null;case w:{var r=t.type;return ya(r)&&Ws(t),Ut(t),null}case M:{var i=t.stateNode;if(Sl(t),lv(t),Hv(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),e===null||e.child===null){var l=ec(t);if(l)wl(t);else if(e!==null){var u=e.memoizedState;(!u.isDehydrated||(t.flags&za)!==le)&&(t.flags|=ti,tg())}}return Fp(e,t),Ut(t),null}case B:{Av(t);var o=mg(),d=t.type;if(e!==null&&t.stateNode!=null)Eb(e,t,d,a,o),e.ref!==t.ref&&Sb(t);else{if(!a){if(t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return Ut(t),null}var v=Uv(),b=ec(t);if(b)QD(t,o,v)&&wl(t);else{var g=Ax(d,a,o,v,t);Cb(g,t,!1,!1),t.stateNode=g,kx(g,d,a,o)&&wl(t)}t.ref!==null&&Sb(t)}return Ut(t),null}case J:{var R=a;if(e&&t.stateNode!=null){var D=e.memoizedProps;Rb(e,t,D,R)}else{if(typeof R!="string"&&t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var _=mg(),Y=Uv(),te=ec(t);te?WD(t)&&wl(t):t.stateNode=Hx(R,_,Y,t)}return Ut(t),null}case ie:{El(t);var ee=t.memoizedState;if(e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){var xe=vw(e,t,ee);if(!xe)return t.flags&Kt?t:null}if((t.flags&Ue)!==le)return t.lanes=n,(t.mode&ke)!==re&&op(t),t;var ge=ee!==null,C=e!==null&&e.memoizedState!==null;if(ge!==C&&ge){var L=t.child;if(L.flags|=ni,(t.mode&Re)!==re){var E=e===null&&(t.memoizedProps.unstable_avoidThisFallback!==!0||!nr);E||Nv(Xn.current,bg)?pO():nh()}}var z=t.updateQueue;if(z!==null&&(t.flags|=De),Ut(t),(t.mode&ke)!==re&&ge){var W=t.child;W!==null&&(t.treeBaseDuration-=W.treeBaseDuration)}return null}case F:return Sl(t),Fp(e,t),e===null&&LD(t.stateNode.containerInfo),Ut(t),null;case I:var P=t.type._context;return Tv(P,t),Ut(t),null;case vn:{var oe=t.type;return ya(oe)&&Ws(t),Ut(t),null}case qe:{El(t);var de=t.memoizedState;if(de===null)return Ut(t),null;var He=(t.flags&Ue)!==le,_e=de.rendering;if(_e===null)if(He)yo(de,!1);else{var lt=mO()&&(e===null||(e.flags&Ue)===le);if(!lt)for(var Le=t.child;Le!==null;){var rt=mc(Le);if(rt!==null){He=!0,t.flags|=Ue,yo(de,!1);var qt=rt.updateQueue;return qt!==null&&(t.updateQueue=qt,t.flags|=De),t.subtreeFlags=le,r0(t,n),Lr(t,kv(Xn.current,ao)),t.child}Le=Le.sibling}de.tail!==null&&Rt()>qb()&&(t.flags|=Ue,He=!0,yo(de,!1),t.lanes=Rm)}else{if(!He){var Ht=mc(_e);if(Ht!==null){t.flags|=Ue,He=!0;var wn=Ht.updateQueue;if(wn!==null&&(t.updateQueue=wn,t.flags|=De),yo(de,!0),de.tail===null&&de.tailMode==="hidden"&&!_e.alternate&&!Lt())return Ut(t),null}else Rt()*2-de.renderingStartTime>qb()&&n!==Sn&&(t.flags|=Ue,He=!0,yo(de,!1),t.lanes=Rm)}if(de.isBackwards)_e.sibling=t.child,t.child=_e;else{var tn=de.last;tn!==null?tn.sibling=_e:t.child=_e,de.last=_e}}if(de.tail!==null){var nn=de.tail;de.rendering=nn,de.tail=nn.sibling,de.renderingStartTime=Rt(),nn.sibling=null;var Gt=Xn.current;return He?Gt=kv(Gt,ao):Gt=Cl(Gt),Lr(t,Gt),nn}return Ut(t),null}case ia:break;case Fe:case Qe:{th(t);var er=t.memoizedState,kl=er!==null;if(e!==null){var Ao=e.memoizedState,xa=Ao!==null;xa!==kl&&!Da&&(t.flags|=ni)}return!kl||(t.mode&Re)===re?Ut(t):Cn(Ta,Sn)&&(Ut(t),t.subtreeFlags&(ot|De)&&(t.flags|=ni)),null}case Qt:return null;case bt:return null}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function pw(e,t,n){switch(cv(t),t.tag){case w:{var a=t.type;ya(a)&&Ws(t);var r=t.flags;return r&Kt?(t.flags=r&~Kt|Ue,(t.mode&ke)!==re&&op(t),t):null}case M:{t.stateNode,Sl(t),lv(t),Hv();var i=t.flags;return(i&Kt)!==le&&(i&Ue)===le?(t.flags=i&~Kt|Ue,t):null}case B:return Av(t),null;case ie:{El(t);var l=t.memoizedState;if(l!==null&&l.dehydrated!==null){if(t.alternate===null)throw new Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");ml()}var u=t.flags;return u&Kt?(t.flags=u&~Kt|Ue,(t.mode&ke)!==re&&op(t),t):null}case qe:return El(t),null;case F:return Sl(t),null;case I:var o=t.type._context;return Tv(o,t),null;case Fe:case Qe:return th(t),null;case Qt:return null;default:return null}}function xb(e,t,n){switch(cv(t),t.tag){case w:{var a=t.type.childContextTypes;a!=null&&Ws(t);break}case M:{t.stateNode,Sl(t),lv(t),Hv();break}case B:{Av(t);break}case F:Sl(t);break;case ie:El(t);break;case qe:El(t);break;case I:var r=t.type._context;Tv(r,t);break;case Fe:case Qe:th(t);break}}var Db=null;Db=new Set;var Vc=!1,At=!1,hw=typeof WeakSet=="function"?WeakSet:Set,X=null,Ol=null,_l=null;function mw(e){Vf(null,function(){throw e}),Bf()}var yw=function(e,t){if(t.props=e.memoizedProps,t.state=e.memoizedState,e.mode&ke)try{Ea(),t.componentWillUnmount()}finally{Ca(e)}else t.componentWillUnmount()};function wb(e,t){try{Ar(dt,e)}catch(n){Pe(e,t,n)}}function jp(e,t,n){try{yw(e,n)}catch(a){Pe(e,t,a)}}function gw(e,t,n){try{n.componentDidMount()}catch(a){Pe(e,t,a)}}function Ob(e,t){try{Lb(e)}catch(n){Pe(e,t,n)}}function Ll(e,t){var n=e.ref;if(n!==null)if(typeof n=="function"){var a;try{if(Yr&&Pr&&e.mode&ke)try{Ea(),a=n(null)}finally{Ca(e)}else a=n(null)}catch(r){Pe(e,t,r)}typeof a=="function"&&f("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",pe(e))}else n.current=null}function Bc(e,t,n){try{n()}catch(a){Pe(e,t,a)}}var _b=!1;function bw(e,t){Mx(e.containerInfo),X=t,Sw();var n=_b;return _b=!1,n}function Sw(){for(;X!==null;){var e=X,t=e.child;(e.subtreeFlags&Gf)!==le&&t!==null?(t.return=e,X=t):Cw()}}function Cw(){for(;X!==null;){var e=X;Je(e);try{Ew(e)}catch(n){Pe(e,e.return,n)}Et();var t=e.sibling;if(t!==null){t.return=e.return,X=t;return}X=e.return}}function Ew(e){var t=e.alternate,n=e.flags;if((n&ti)!==le){switch(Je(e),e.tag){case U:case Z:case Ce:break;case w:{if(t!==null){var a=t.memoizedProps,r=t.memoizedState,i=e.stateNode;e.type===e.elementType&&!Ei&&(i.props!==e.memoizedProps&&f("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",pe(e)||"instance"),i.state!==e.memoizedState&&f("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",pe(e)||"instance"));var l=i.getSnapshotBeforeUpdate(e.elementType===e.type?a:Jn(e.type,a),r);{var u=Db;l===void 0&&!u.has(e.type)&&(u.add(e.type),f("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",pe(e)))}i.__reactInternalSnapshotBeforeUpdate=l}break}case M:{{var o=e.stateNode;nD(o.containerInfo)}break}case B:case J:case F:case vn:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}Et()}}function ea(e,t,n){var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var i=r.next,l=i;do{if((l.tag&e)===e){var u=l.destroy;l.destroy=void 0,u!==void 0&&((e&Mt)!==cn?HE(t):(e&dt)!==cn&&gm(t),(e&ga)!==cn&&Lo(!0),Bc(t,n,u),(e&ga)!==cn&&Lo(!1),(e&Mt)!==cn?FE():(e&dt)!==cn&&bm())}l=l.next}while(l!==i)}}function Ar(e,t){var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var r=a.next,i=r;do{if((i.tag&e)===e){(e&Mt)!==cn?kE(t):(e&dt)!==cn&&jE(t);var l=i.create;(e&ga)!==cn&&Lo(!0),i.destroy=l(),(e&ga)!==cn&&Lo(!1),(e&Mt)!==cn?zE():(e&dt)!==cn&&VE();{var u=i.destroy;if(u!==void 0&&typeof u!="function"){var o=void 0;(i.tag&dt)!==le?o="useLayoutEffect":(i.tag&ga)!==le?o="useInsertionEffect":o="useEffect";var d=void 0;u===null?d=" You returned null. If your effect does not require clean up, return undefined (or nothing).":typeof u.then=="function"?d=`

It looks like you wrote `+o+`(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:

`+o+`(() => {
  async function fetchData() {
    // You can await here
    const response = await MyAPI.getData(someId);
    // ...
  }
  fetchData();
}, [someId]); // Or [] if effect doesn't need props or state

Learn more about data fetching with Hooks: https://reactjs.org/link/hooks-data-fetching`:d=" You returned: "+u,f("%s must not return anything besides a function, which is used for clean-up.%s",o,d)}}}i=i.next}while(i!==r)}}function Rw(e,t){if((t.flags&De)!==le)switch(t.tag){case ve:{var n=t.stateNode.passiveEffectDuration,a=t.memoizedProps,r=a.id,i=a.onPostCommit,l=qg(),u=t.alternate===null?"mount":"update";Pg()&&(u="nested-update"),typeof i=="function"&&i(r,u,n,l);var o=t.return;e:for(;o!==null;){switch(o.tag){case M:var d=o.stateNode;d.passiveEffectDuration+=n;break e;case ve:var v=o.stateNode;v.passiveEffectDuration+=n;break e}o=o.return}break}}}function Tw(e,t,n,a){if((n.flags&fu)!==le)switch(n.tag){case U:case Z:case Ce:{if(!At)if(n.mode&ke)try{Ea(),Ar(dt|ft,n)}finally{Ca(n)}else Ar(dt|ft,n);break}case w:{var r=n.stateNode;if(n.flags&De&&!At)if(t===null)if(n.type===n.elementType&&!Ei&&(r.props!==n.memoizedProps&&f("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",pe(n)||"instance"),r.state!==n.memoizedState&&f("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",pe(n)||"instance")),n.mode&ke)try{Ea(),r.componentDidMount()}finally{Ca(n)}else r.componentDidMount();else{var i=n.elementType===n.type?t.memoizedProps:Jn(n.type,t.memoizedProps),l=t.memoizedState;if(n.type===n.elementType&&!Ei&&(r.props!==n.memoizedProps&&f("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",pe(n)||"instance"),r.state!==n.memoizedState&&f("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",pe(n)||"instance")),n.mode&ke)try{Ea(),r.componentDidUpdate(i,l,r.__reactInternalSnapshotBeforeUpdate)}finally{Ca(n)}else r.componentDidUpdate(i,l,r.__reactInternalSnapshotBeforeUpdate)}var u=n.updateQueue;u!==null&&(n.type===n.elementType&&!Ei&&(r.props!==n.memoizedProps&&f("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",pe(n)||"instance"),r.state!==n.memoizedState&&f("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",pe(n)||"instance")),hg(n,u,r));break}case M:{var o=n.updateQueue;if(o!==null){var d=null;if(n.child!==null)switch(n.child.tag){case B:d=n.child.stateNode;break;case w:d=n.child.stateNode;break}hg(n,o,d)}break}case B:{var v=n.stateNode;if(t===null&&n.flags&De){var b=n.type,g=n.memoizedProps;$x(v,b,g)}break}case J:break;case F:break;case ve:{{var R=n.memoizedProps,D=R.onCommit,_=R.onRender,Y=n.stateNode.effectDuration,te=qg(),ee=t===null?"mount":"update";Pg()&&(ee="nested-update"),typeof _=="function"&&_(n.memoizedProps.id,ee,n.actualDuration,n.treeBaseDuration,n.actualStartTime,te);{typeof D=="function"&&D(n.memoizedProps.id,ee,Y,te),CO(n);var xe=n.return;e:for(;xe!==null;){switch(xe.tag){case M:var ge=xe.stateNode;ge.effectDuration+=Y;break e;case ve:var C=xe.stateNode;C.effectDuration+=Y;break e}xe=xe.return}}}break}case ie:{Uw(e,n);break}case qe:case vn:case ia:case Fe:case Qe:case bt:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}At||n.flags&mr&&Lb(n)}function xw(e){switch(e.tag){case U:case Z:case Ce:{if(e.mode&ke)try{Ea(),wb(e,e.return)}finally{Ca(e)}else wb(e,e.return);break}case w:{var t=e.stateNode;typeof t.componentDidMount=="function"&&gw(e,e.return,t),Ob(e,e.return);break}case B:{Ob(e,e.return);break}}}function Dw(e,t){for(var n=null,a=e;;){if(a.tag===B){if(n===null){n=a;try{var r=a.stateNode;t?Jx(r):eD(a.stateNode,a.memoizedProps)}catch(l){Pe(e,e.return,l)}}}else if(a.tag===J){if(n===null)try{var i=a.stateNode;t?Zx(i):tD(i,a.memoizedProps)}catch(l){Pe(e,e.return,l)}}else if(!((a.tag===Fe||a.tag===Qe)&&a.memoizedState!==null&&a!==e)){if(a.child!==null){a.child.return=a,a=a.child;continue}}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;n===a&&(n=null),a=a.return}n===a&&(n=null),a.sibling.return=a.return,a=a.sibling}}function Lb(e){var t=e.ref;if(t!==null){var n=e.stateNode,a;switch(e.tag){case B:a=n;break;default:a=n}if(typeof t=="function"){var r;if(e.mode&ke)try{Ea(),r=t(a)}finally{Ca(e)}else r=t(a);typeof r=="function"&&f("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",pe(e))}else t.hasOwnProperty("current")||f("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",pe(e)),t.current=a}}function ww(e){var t=e.alternate;t!==null&&(t.return=null),e.return=null}function Mb(e){var t=e.alternate;t!==null&&(e.alternate=null,Mb(t));{if(e.child=null,e.deletions=null,e.sibling=null,e.tag===B){var n=e.stateNode;n!==null&&AD(n)}e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}}function Ow(e){for(var t=e.return;t!==null;){if(Ub(t))return t;t=t.return}throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}function Ub(e){return e.tag===B||e.tag===M||e.tag===F}function Ab(e){var t=e;e:for(;;){for(;t.sibling===null;){if(t.return===null||Ub(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==B&&t.tag!==J&&t.tag!==et;){if(t.flags&ot||t.child===null||t.tag===F)continue e;t.child.return=t,t=t.child}if(!(t.flags&ot))return t.stateNode}}function _w(e){var t=Ow(e);switch(t.tag){case B:{var n=t.stateNode;t.flags&su&&(ky(n),t.flags&=~su);var a=Ab(e);Bp(e,a,n);break}case M:case F:{var r=t.stateNode.containerInfo,i=Ab(e);Vp(e,i,r);break}default:throw new Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}function Vp(e,t,n){var a=e.tag,r=a===B||a===J;if(r){var i=e.stateNode;t?Wx(n,i,t):Gx(n,i)}else if(a!==F){var l=e.child;if(l!==null){Vp(l,t,n);for(var u=l.sibling;u!==null;)Vp(u,t,n),u=u.sibling}}}function Bp(e,t,n){var a=e.tag,r=a===B||a===J;if(r){var i=e.stateNode;t?Qx(n,i,t):qx(n,i)}else if(a!==F){var l=e.child;if(l!==null){Bp(l,t,n);for(var u=l.sibling;u!==null;)Bp(u,t,n),u=u.sibling}}}var Nt=null,ta=!1;function Lw(e,t,n){{var a=t;e:for(;a!==null;){switch(a.tag){case B:{Nt=a.stateNode,ta=!1;break e}case M:{Nt=a.stateNode.containerInfo,ta=!0;break e}case F:{Nt=a.stateNode.containerInfo,ta=!0;break e}}a=a.return}if(Nt===null)throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");Nb(e,t,n),Nt=null,ta=!1}ww(n)}function Nr(e,t,n){for(var a=n.child;a!==null;)Nb(e,t,a),a=a.sibling}function Nb(e,t,n){switch(ME(n),n.tag){case B:At||Ll(n,t);case J:{{var a=Nt,r=ta;Nt=null,Nr(e,t,n),Nt=a,ta=r,Nt!==null&&(ta?Xx(Nt,n.stateNode):Ix(Nt,n.stateNode))}return}case et:{Nt!==null&&(ta?Kx(Nt,n.stateNode):Jd(Nt,n.stateNode));return}case F:{{var i=Nt,l=ta;Nt=n.stateNode.containerInfo,ta=!0,Nr(e,t,n),Nt=i,ta=l}return}case U:case Z:case Be:case Ce:{if(!At){var u=n.updateQueue;if(u!==null){var o=u.lastEffect;if(o!==null){var d=o.next,v=d;do{var b=v,g=b.destroy,R=b.tag;g!==void 0&&((R&ga)!==cn?Bc(n,t,g):(R&dt)!==cn&&(gm(n),n.mode&ke?(Ea(),Bc(n,t,g),Ca(n)):Bc(n,t,g),bm())),v=v.next}while(v!==d)}}}Nr(e,t,n);return}case w:{if(!At){Ll(n,t);var D=n.stateNode;typeof D.componentWillUnmount=="function"&&jp(n,t,D)}Nr(e,t,n);return}case ia:{Nr(e,t,n);return}case Fe:{if(n.mode&Re){var _=At;At=_||n.memoizedState!==null,Nr(e,t,n),At=_}else Nr(e,t,n);break}default:{Nr(e,t,n);return}}}function Mw(e){e.memoizedState}function Uw(e,t){var n=t.memoizedState;if(n===null){var a=t.alternate;if(a!==null){var r=a.memoizedState;if(r!==null){var i=r.dehydrated;i!==null&&mD(i)}}}}function kb(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new hw),t.forEach(function(a){var r=OO.bind(null,e,a);if(!n.has(a)){if(n.add(a),qn)if(Ol!==null&&_l!==null)_o(_l,Ol);else throw Error("Expected finished root and lanes to be set. This is a bug in React.");a.then(r,r)}})}}function Aw(e,t,n){Ol=n,_l=e,Je(t),zb(t,e),Je(t),Ol=null,_l=null}function na(e,t,n){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r];try{Lw(e,t,i)}catch(o){Pe(i,t,o)}}var l=Ko();if(t.subtreeFlags&Qf)for(var u=t.child;u!==null;)Je(u),zb(u,e),u=u.sibling;Je(l)}function zb(e,t,n){var a=e.alternate,r=e.flags;switch(e.tag){case U:case Z:case Be:case Ce:{if(na(t,e),Ra(e),r&De){try{ea(ga|ft,e,e.return),Ar(ga|ft,e)}catch(oe){Pe(e,e.return,oe)}if(e.mode&ke){try{Ea(),ea(dt|ft,e,e.return)}catch(oe){Pe(e,e.return,oe)}Ca(e)}else try{ea(dt|ft,e,e.return)}catch(oe){Pe(e,e.return,oe)}}return}case w:{na(t,e),Ra(e),r&mr&&a!==null&&Ll(a,a.return);return}case B:{na(t,e),Ra(e),r&mr&&a!==null&&Ll(a,a.return);{if(e.flags&su){var i=e.stateNode;try{ky(i)}catch(oe){Pe(e,e.return,oe)}}if(r&De){var l=e.stateNode;if(l!=null){var u=e.memoizedProps,o=a!==null?a.memoizedProps:u,d=e.type,v=e.updateQueue;if(e.updateQueue=null,v!==null)try{Yx(l,v,d,o,u,e)}catch(oe){Pe(e,e.return,oe)}}}}return}case J:{if(na(t,e),Ra(e),r&De){if(e.stateNode===null)throw new Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");var b=e.stateNode,g=e.memoizedProps,R=a!==null?a.memoizedProps:g;try{Px(b,R,g)}catch(oe){Pe(e,e.return,oe)}}return}case M:{if(na(t,e),Ra(e),r&De&&a!==null){var D=a.memoizedState;if(D.isDehydrated)try{hD(t.containerInfo)}catch(oe){Pe(e,e.return,oe)}}return}case F:{na(t,e),Ra(e);return}case ie:{na(t,e),Ra(e);var _=e.child;if(_.flags&ni){var Y=_.stateNode,te=_.memoizedState,ee=te!==null;if(Y.isHidden=ee,ee){var xe=_.alternate!==null&&_.alternate.memoizedState!==null;xe||vO()}}if(r&De){try{Mw(e)}catch(oe){Pe(e,e.return,oe)}kb(e)}return}case Fe:{var ge=a!==null&&a.memoizedState!==null;if(e.mode&Re){var C=At;At=C||ge,na(t,e),At=C}else na(t,e);if(Ra(e),r&ni){var L=e.stateNode,E=e.memoizedState,z=E!==null,W=e;if(L.isHidden=z,z&&!ge&&(W.mode&Re)!==re){X=W;for(var P=W.child;P!==null;)X=P,kw(P),P=P.sibling}Dw(W,z)}return}case qe:{na(t,e),Ra(e),r&De&&kb(e);return}case ia:return;default:{na(t,e),Ra(e);return}}}function Ra(e){var t=e.flags;if(t&ot){try{_w(e)}catch(n){Pe(e,e.return,n)}e.flags&=~ot}t&Ha&&(e.flags&=~Ha)}function Nw(e,t,n){Ol=n,_l=t,X=e,Hb(e,t,n),Ol=null,_l=null}function Hb(e,t,n){for(var a=(e.mode&Re)!==re;X!==null;){var r=X,i=r.child;if(r.tag===Fe&&a){var l=r.memoizedState!==null,u=l||Vc;if(u){$p(e,t,n);continue}else{var o=r.alternate,d=o!==null&&o.memoizedState!==null,v=d||At,b=Vc,g=At;Vc=u,At=v,At&&!g&&(X=r,zw(r));for(var R=i;R!==null;)X=R,Hb(R,t,n),R=R.sibling;X=r,Vc=b,At=g,$p(e,t,n);continue}}(r.subtreeFlags&fu)!==le&&i!==null?(i.return=r,X=i):$p(e,t,n)}}function $p(e,t,n){for(;X!==null;){var a=X;if((a.flags&fu)!==le){var r=a.alternate;Je(a);try{Tw(t,r,a,n)}catch(l){Pe(a,a.return,l)}Et()}if(a===e){X=null;return}var i=a.sibling;if(i!==null){i.return=a.return,X=i;return}X=a.return}}function kw(e){for(;X!==null;){var t=X,n=t.child;switch(t.tag){case U:case Z:case Be:case Ce:{if(t.mode&ke)try{Ea(),ea(dt,t,t.return)}finally{Ca(t)}else ea(dt,t,t.return);break}case w:{Ll(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&jp(t,t.return,a);break}case B:{Ll(t,t.return);break}case Fe:{var r=t.memoizedState!==null;if(r){Fb(e);continue}break}}n!==null?(n.return=t,X=n):Fb(e)}}function Fb(e){for(;X!==null;){var t=X;if(t===e){X=null;return}var n=t.sibling;if(n!==null){n.return=t.return,X=n;return}X=t.return}}function zw(e){for(;X!==null;){var t=X,n=t.child;if(t.tag===Fe){var a=t.memoizedState!==null;if(a){jb(e);continue}}n!==null?(n.return=t,X=n):jb(e)}}function jb(e){for(;X!==null;){var t=X;Je(t);try{xw(t)}catch(a){Pe(t,t.return,a)}if(Et(),t===e){X=null;return}var n=t.sibling;if(n!==null){n.return=t.return,X=n;return}X=t.return}}function Hw(e,t,n,a){X=t,Fw(t,e,n,a)}function Fw(e,t,n,a){for(;X!==null;){var r=X,i=r.child;(r.subtreeFlags&Xi)!==le&&i!==null?(i.return=r,X=i):jw(e,t,n,a)}}function jw(e,t,n,a){for(;X!==null;){var r=X;if((r.flags&Pn)!==le){Je(r);try{Vw(t,r,n,a)}catch(l){Pe(r,r.return,l)}Et()}if(r===e){X=null;return}var i=r.sibling;if(i!==null){i.return=r.return,X=i;return}X=r.return}}function Vw(e,t,n,a){switch(t.tag){case U:case Z:case Ce:{if(t.mode&ke){up();try{Ar(Mt|ft,t)}finally{lp(t)}}else Ar(Mt|ft,t);break}}}function Bw(e){X=e,$w()}function $w(){for(;X!==null;){var e=X,t=e.child;if((X.flags&ei)!==le){var n=e.deletions;if(n!==null){for(var a=0;a<n.length;a++){var r=n[a];X=r,qw(r,e)}{var i=e.alternate;if(i!==null){var l=i.child;if(l!==null){i.child=null;do{var u=l.sibling;l.sibling=null,l=u}while(l!==null)}}}X=e}}(e.subtreeFlags&Xi)!==le&&t!==null?(t.return=e,X=t):Yw()}}function Yw(){for(;X!==null;){var e=X;(e.flags&Pn)!==le&&(Je(e),Pw(e),Et());var t=e.sibling;if(t!==null){t.return=e.return,X=t;return}X=e.return}}function Pw(e){switch(e.tag){case U:case Z:case Ce:{e.mode&ke?(up(),ea(Mt|ft,e,e.return),lp(e)):ea(Mt|ft,e,e.return);break}}}function qw(e,t){for(;X!==null;){var n=X;Je(n),Qw(n,t),Et();var a=n.child;a!==null?(a.return=n,X=a):Gw(e)}}function Gw(e){for(;X!==null;){var t=X,n=t.sibling,a=t.return;if(Mb(t),t===e){X=null;return}if(n!==null){n.return=a,X=n;return}X=a}}function Qw(e,t){switch(e.tag){case U:case Z:case Ce:{e.mode&ke?(up(),ea(Mt,e,t),lp(e)):ea(Mt,e,t);break}}}function Ww(e){switch(e.tag){case U:case Z:case Ce:{try{Ar(dt|ft,e)}catch(n){Pe(e,e.return,n)}break}case w:{var t=e.stateNode;try{t.componentDidMount()}catch(n){Pe(e,e.return,n)}break}}}function Iw(e){switch(e.tag){case U:case Z:case Ce:{try{Ar(Mt|ft,e)}catch(t){Pe(e,e.return,t)}break}}}function Xw(e){switch(e.tag){case U:case Z:case Ce:{try{ea(dt|ft,e,e.return)}catch(n){Pe(e,e.return,n)}break}case w:{var t=e.stateNode;typeof t.componentWillUnmount=="function"&&jp(e,e.return,t);break}}}function Kw(e){switch(e.tag){case U:case Z:case Ce:try{ea(Mt|ft,e,e.return)}catch(t){Pe(e,e.return,t)}}}if(typeof Symbol=="function"&&Symbol.for){var go=Symbol.for;go("selector.component"),go("selector.has_pseudo_class"),go("selector.role"),go("selector.test_id"),go("selector.text")}var Jw=[];function Zw(){Jw.forEach(function(e){return e()})}var eO=m.ReactCurrentActQueue;function tO(e){{var t=typeof IS_REACT_ACT_ENVIRONMENT<"u"?IS_REACT_ACT_ENVIRONMENT:void 0,n=typeof jest<"u";return n&&t!==!1}}function Vb(){{var e=typeof IS_REACT_ACT_ENVIRONMENT<"u"?IS_REACT_ACT_ENVIRONMENT:void 0;return!e&&eO.current!==null&&f("The current testing environment is not configured to support act(...)"),e}}var nO=Math.ceil,Yp=m.ReactCurrentDispatcher,Pp=m.ReactCurrentOwner,kt=m.ReactCurrentBatchConfig,aa=m.ReactCurrentActQueue,ht=0,Bb=1,zt=2,Hn=4,Xa=0,bo=1,Ri=2,$c=3,So=4,$b=5,qp=6,Te=ht,Zt=null,Ze=null,mt=N,Ta=N,Gp=Tr(N),yt=Xa,Co=null,Yc=N,Eo=N,Pc=N,Ro=null,fn=null,Qp=0,Yb=500,Pb=1/0,aO=500,Ka=null;function To(){Pb=Rt()+aO}function qb(){return Pb}var qc=!1,Wp=null,Ml=null,Ti=!1,kr=null,xo=N,Ip=[],Xp=null,rO=50,Do=0,Kp=null,Jp=!1,Gc=!1,iO=50,Ul=0,Qc=null,wo=Ge,Wc=N,Gb=!1;function Ic(){return Zt}function en(){return(Te&(zt|Hn))!==ht?Rt():(wo!==Ge||(wo=Rt()),wo)}function zr(e){var t=e.mode;if((t&Re)===re)return ce;if((Te&zt)!==ht&&mt!==N)return gu(mt);var n=e0()!==ZD;if(n){if(kt.transition!==null){var a=kt.transition;a._updatedFibers||(a._updatedFibers=new Set),a._updatedFibers.add(e)}return Wc===xt&&(Wc=wm()),Wc}var r=Gn();if(r!==xt)return r;var i=Fx();return i}function lO(e){var t=e.mode;return(t&Re)===re?ce:oR()}function gt(e,t,n,a){LO(),Gb&&f("useInsertionEffect must not schedule updates."),Jp&&(Gc=!0),bu(e,n,a),(Te&zt)!==N&&e===Zt?AO(t):(qn&&Lm(e,t,n),NO(t),e===Zt&&((Te&zt)===ht&&(Eo=he(Eo,n)),yt===So&&Hr(e,mt)),dn(e,a),n===ce&&Te===ht&&(t.mode&Re)===re&&!aa.isBatchingLegacy&&(To(),Gy()))}function uO(e,t,n){var a=e.current;a.lanes=t,bu(e,t,n),dn(e,n)}function oO(e){return(Te&zt)!==ht}function dn(e,t){var n=e.callbackNode;nR(e,t);var a=ms(e,e===Zt?mt:N);if(a===N){n!==null&&oS(n),e.callbackNode=null,e.callbackPriority=xt;return}var r=oi(a),i=e.callbackPriority;if(i===r&&!(aa.current!==null&&n!==ih)){n==null&&i!==ce&&f("Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.");return}n!=null&&oS(n);var l;if(r===ce)e.tag===xr?(aa.isBatchingLegacy!==null&&(aa.didScheduleLegacyUpdate=!0),zD(Ib.bind(null,e))):qy(Ib.bind(null,e)),aa.current!==null?aa.current.push(Dr):Vx(function(){(Te&(zt|Hn))===ht&&Dr()}),l=null;else{var u;switch(Am(a)){case En:u=ds;break;case Ba:u=Wf;break;case $a:u=ii;break;case bs:u=If;break;default:u=ii;break}l=lh(u,Qb.bind(null,e))}e.callbackPriority=r,e.callbackNode=l}function Qb(e,t){if(D0(),wo=Ge,Wc=N,(Te&(zt|Hn))!==ht)throw new Error("Should not already be working.");var n=e.callbackNode,a=Za();if(a&&e.callbackNode!==n)return null;var r=ms(e,e===Zt?mt:N);if(r===N)return null;var i=!ys(e,r)&&!uR(e,r)&&!t,l=i?gO(e,r):Kc(e,r);if(l!==Xa){if(l===Ri){var u=yd(e);u!==N&&(r=u,l=Zp(e,u))}if(l===bo){var o=Co;throw xi(e,N),Hr(e,r),dn(e,Rt()),o}if(l===qp)Hr(e,r);else{var d=!ys(e,r),v=e.current.alternate;if(d&&!cO(v)){if(l=Kc(e,r),l===Ri){var b=yd(e);b!==N&&(r=b,l=Zp(e,b))}if(l===bo){var g=Co;throw xi(e,N),Hr(e,r),dn(e,Rt()),g}}e.finishedWork=v,e.finishedLanes=r,sO(e,l,r)}}return dn(e,Rt()),e.callbackNode===n?Qb.bind(null,e):null}function Zp(e,t){var n=Ro;if(Ss(e)){var a=xi(e,t);a.flags|=za,_D(e.containerInfo)}var r=Kc(e,t);if(r!==Ri){var i=fn;fn=n,i!==null&&Wb(i)}return r}function Wb(e){fn===null?fn=e:fn.push.apply(fn,e)}function sO(e,t,n){switch(t){case Xa:case bo:throw new Error("Root did not complete. This is a bug in React.");case Ri:{Di(e,fn,Ka);break}case $c:{if(Hr(e,n),xm(n)&&!sS()){var a=Qp+Yb-Rt();if(a>10){var r=ms(e,N);if(r!==N)break;var i=e.suspendedLanes;if(!nl(i,n)){en(),_m(e,i);break}e.timeoutHandle=Xd(Di.bind(null,e,fn,Ka),a);break}}Di(e,fn,Ka);break}case So:{if(Hr(e,n),lR(n))break;if(!sS()){var l=eR(e,n),u=l,o=Rt()-u,d=_O(o)-o;if(d>10){e.timeoutHandle=Xd(Di.bind(null,e,fn,Ka),d);break}}Di(e,fn,Ka);break}case $b:{Di(e,fn,Ka);break}default:throw new Error("Unknown root exit status.")}}function cO(e){for(var t=e;;){if(t.flags&cs){var n=t.updateQueue;if(n!==null){var a=n.stores;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r],l=i.getSnapshot,u=i.value;try{if(!Tn(l(),u))return!1}catch{return!1}}}}var o=t.child;if(t.subtreeFlags&cs&&o!==null){o.return=t,t=o;continue}if(t===e)return!0;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}return!0}function Hr(e,t){t=gs(t,Pc),t=gs(t,Eo),cR(e,t)}function Ib(e){if(w0(),(Te&(zt|Hn))!==ht)throw new Error("Should not already be working.");Za();var t=ms(e,N);if(!Cn(t,ce))return dn(e,Rt()),null;var n=Kc(e,t);if(e.tag!==xr&&n===Ri){var a=yd(e);a!==N&&(t=a,n=Zp(e,a))}if(n===bo){var r=Co;throw xi(e,N),Hr(e,t),dn(e,Rt()),r}if(n===qp)throw new Error("Root did not complete. This is a bug in React.");var i=e.current.alternate;return e.finishedWork=i,e.finishedLanes=t,Di(e,fn,Ka),dn(e,Rt()),null}function fO(e,t){t!==N&&(Cd(e,he(t,ce)),dn(e,Rt()),(Te&(zt|Hn))===ht&&(To(),Dr()))}function eh(e,t){var n=Te;Te|=Bb;try{return e(t)}finally{Te=n,Te===ht&&!aa.isBatchingLegacy&&(To(),Gy())}}function dO(e,t,n,a,r){var i=Gn(),l=kt.transition;try{return kt.transition=null,Dt(En),e(t,n,a,r)}finally{Dt(i),kt.transition=l,Te===ht&&To()}}function Ja(e){kr!==null&&kr.tag===xr&&(Te&(zt|Hn))===ht&&Za();var t=Te;Te|=Bb;var n=kt.transition,a=Gn();try{return kt.transition=null,Dt(En),e?e():void 0}finally{Dt(a),kt.transition=n,Te=t,(Te&(zt|Hn))===ht&&Dr()}}function Xb(){return(Te&(zt|Hn))!==ht}function Xc(e,t){Yt(Gp,Ta,e),Ta=he(Ta,t)}function th(e){Ta=Gp.current,$t(Gp,e)}function xi(e,t){e.finishedWork=null,e.finishedLanes=N;var n=e.timeoutHandle;if(n!==Kd&&(e.timeoutHandle=Kd,jx(n)),Ze!==null)for(var a=Ze.return;a!==null;){var r=a.alternate;xb(r,a),a=a.return}Zt=e;var i=wi(e.current,null);return Ze=i,mt=Ta=t,yt=Xa,Co=null,Yc=N,Eo=N,Pc=N,Ro=null,fn=null,u0(),In.discardPendingWarnings(),i}function Kb(e,t){do{var n=Ze;try{if(lc(),Cg(),Et(),Pp.current=null,n===null||n.return===null){yt=bo,Co=t,Ze=null;return}if(Yr&&n.mode&ke&&kc(n,!0),ar)if(Ji(),t!==null&&typeof t=="object"&&typeof t.then=="function"){var a=t;$E(n,a,mt)}else BE(n,t,mt);z0(e,n.return,n,t,mt),tS(n)}catch(r){t=r,Ze===n&&n!==null?(n=n.return,Ze=n):n=Ze;continue}return}while(!0)}function Jb(){var e=Yp.current;return Yp.current=Lc,e===null?Lc:e}function Zb(e){Yp.current=e}function vO(){Qp=Rt()}function Oo(e){Yc=he(e,Yc)}function pO(){yt===Xa&&(yt=$c)}function nh(){(yt===Xa||yt===$c||yt===Ri)&&(yt=So),Zt!==null&&(gd(Yc)||gd(Eo))&&Hr(Zt,mt)}function hO(e){yt!==So&&(yt=Ri),Ro===null?Ro=[e]:Ro.push(e)}function mO(){return yt===Xa}function Kc(e,t){var n=Te;Te|=zt;var a=Jb();if(Zt!==e||mt!==t){if(qn){var r=e.memoizedUpdaters;r.size>0&&(_o(e,mt),r.clear()),Mm(e,t)}Ka=Um(),xi(e,t)}Sm(t);do try{yO();break}catch(i){Kb(e,i)}while(!0);if(lc(),Te=n,Zb(a),Ze!==null)throw new Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");return Cm(),Zt=null,mt=N,yt}function yO(){for(;Ze!==null;)eS(Ze)}function gO(e,t){var n=Te;Te|=zt;var a=Jb();if(Zt!==e||mt!==t){if(qn){var r=e.memoizedUpdaters;r.size>0&&(_o(e,mt),r.clear()),Mm(e,t)}Ka=Um(),To(),xi(e,t)}Sm(t);do try{bO();break}catch(i){Kb(e,i)}while(!0);return lc(),Zb(a),Te=n,Ze!==null?(QE(),Xa):(Cm(),Zt=null,mt=N,yt)}function bO(){for(;Ze!==null&&!CE();)eS(Ze)}function eS(e){var t=e.alternate;Je(e);var n;(e.mode&ke)!==re?(ip(e),n=ah(t,e,Ta),kc(e,!0)):n=ah(t,e,Ta),Et(),e.memoizedProps=e.pendingProps,n===null?tS(e):Ze=n,Pp.current=null}function tS(e){var t=e;do{var n=t.alternate,a=t.return;if((t.flags&cu)===le){Je(t);var r=void 0;if((t.mode&ke)===re?r=Tb(n,t,Ta):(ip(t),r=Tb(n,t,Ta),kc(t,!1)),Et(),r!==null){Ze=r;return}}else{var i=pw(n,t);if(i!==null){i.flags&=hE,Ze=i;return}if((t.mode&ke)!==re){kc(t,!1);for(var l=t.actualDuration,u=t.child;u!==null;)l+=u.actualDuration,u=u.sibling;t.actualDuration=l}if(a!==null)a.flags|=cu,a.subtreeFlags=le,a.deletions=null;else{yt=qp,Ze=null;return}}var o=t.sibling;if(o!==null){Ze=o;return}t=a,Ze=t}while(t!==null);yt===Xa&&(yt=$b)}function Di(e,t,n){var a=Gn(),r=kt.transition;try{kt.transition=null,Dt(En),SO(e,t,n,a)}finally{kt.transition=r,Dt(a)}return null}function SO(e,t,n,a){do Za();while(kr!==null);if(MO(),(Te&(zt|Hn))!==ht)throw new Error("Should not already be working.");var r=e.finishedWork,i=e.finishedLanes;if(NE(i),r===null)return ym(),null;if(i===N&&f("root.finishedLanes should not be empty during a commit. This is a bug in React."),e.finishedWork=null,e.finishedLanes=N,r===e.current)throw new Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");e.callbackNode=null,e.callbackPriority=xt;var l=he(r.lanes,r.childLanes);fR(e,l),e===Zt&&(Zt=null,Ze=null,mt=N),((r.subtreeFlags&Xi)!==le||(r.flags&Xi)!==le)&&(Ti||(Ti=!0,Xp=n,lh(ii,function(){return Za(),null})));var u=(r.subtreeFlags&(Gf|Qf|fu|Xi))!==le,o=(r.flags&(Gf|Qf|fu|Xi))!==le;if(u||o){var d=kt.transition;kt.transition=null;var v=Gn();Dt(En);var b=Te;Te|=Hn,Pp.current=null,bw(e,r),Gg(),Aw(e,r,i),Ux(e.containerInfo),e.current=r,YE(i),Nw(r,e,i),PE(),EE(),Te=b,Dt(v),kt.transition=d}else e.current=r,Gg();var g=Ti;if(Ti?(Ti=!1,kr=e,xo=i):(Ul=0,Qc=null),l=e.pendingLanes,l===N&&(Ml=null),g||iS(e.current,!1),_E(r.stateNode,a),qn&&e.memoizedUpdaters.clear(),Zw(),dn(e,Rt()),t!==null)for(var R=e.onRecoverableError,D=0;D<t.length;D++){var _=t[D],Y=_.stack,te=_.digest;R(_.value,{componentStack:Y,digest:te})}if(qc){qc=!1;var ee=Wp;throw Wp=null,ee}return Cn(xo,ce)&&e.tag!==xr&&Za(),l=e.pendingLanes,Cn(l,ce)?(x0(),e===Kp?Do++:(Do=0,Kp=e)):Do=0,Dr(),ym(),null}function Za(){if(kr!==null){var e=Am(xo),t=hR($a,e),n=kt.transition,a=Gn();try{return kt.transition=null,Dt(t),EO()}finally{Dt(a),kt.transition=n}}return!1}function CO(e){Ip.push(e),Ti||(Ti=!0,lh(ii,function(){return Za(),null}))}function EO(){if(kr===null)return!1;var e=Xp;Xp=null;var t=kr,n=xo;if(kr=null,xo=N,(Te&(zt|Hn))!==ht)throw new Error("Cannot flush passive effects while already rendering.");Jp=!0,Gc=!1,qE(n);var a=Te;Te|=Hn,Bw(t.current),Hw(t,t.current,n,e);{var r=Ip;Ip=[];for(var i=0;i<r.length;i++){var l=r[i];Rw(t,l)}}GE(),iS(t.current,!0),Te=a,Dr(),Gc?t===Qc?Ul++:(Ul=0,Qc=t):Ul=0,Jp=!1,Gc=!1,LE(t);{var u=t.current.stateNode;u.effectDuration=0,u.passiveEffectDuration=0}return!0}function nS(e){return Ml!==null&&Ml.has(e)}function RO(e){Ml===null?Ml=new Set([e]):Ml.add(e)}function TO(e){qc||(qc=!0,Wp=e)}var xO=TO;function aS(e,t,n){var a=Ci(n,t),r=eb(e,a,ce),i=Or(e,r,ce),l=en();i!==null&&(bu(i,ce,l),dn(i,l))}function Pe(e,t,n){if(mw(n),Lo(!1),e.tag===M){aS(e,e,n);return}var a=null;for(a=t;a!==null;){if(a.tag===M){aS(a,e,n);return}else if(a.tag===w){var r=a.type,i=a.stateNode;if(typeof r.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&!nS(i)){var l=Ci(n,e),u=Rp(a,l,ce),o=Or(a,u,ce),d=en();o!==null&&(bu(o,ce,d),dn(o,d));return}}a=a.return}f(`Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.

Error message:

%s`,n)}function DO(e,t,n){var a=e.pingCache;a!==null&&a.delete(t);var r=en();_m(e,n),kO(e),Zt===e&&nl(mt,n)&&(yt===So||yt===$c&&xm(mt)&&Rt()-Qp<Yb?xi(e,N):Pc=he(Pc,n)),dn(e,r)}function rS(e,t){t===xt&&(t=lO(e));var n=en(),a=sn(e,t);a!==null&&(bu(a,t,n),dn(a,n))}function wO(e){var t=e.memoizedState,n=xt;t!==null&&(n=t.retryLane),rS(e,n)}function OO(e,t){var n=xt,a;switch(e.tag){case ie:a=e.stateNode;var r=e.memoizedState;r!==null&&(n=r.retryLane);break;case qe:a=e.stateNode;break;default:throw new Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}a!==null&&a.delete(t),rS(e,n)}function _O(e){return e<120?120:e<480?480:e<1080?1080:e<1920?1920:e<3e3?3e3:e<4320?4320:nO(e/1960)*1960}function LO(){if(Do>rO)throw Do=0,Kp=null,new Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");Ul>iO&&(Ul=0,Qc=null,f("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render."))}function MO(){In.flushLegacyContextWarning(),In.flushPendingUnsafeLifecycleWarnings()}function iS(e,t){Je(e),Jc(e,Fa,Xw),t&&Jc(e,fs,Kw),Jc(e,Fa,Ww),t&&Jc(e,fs,Iw),Et()}function Jc(e,t,n){for(var a=e,r=null;a!==null;){var i=a.subtreeFlags&t;a!==r&&a.child!==null&&i!==le?a=a.child:((a.flags&t)!==le&&n(a),a.sibling!==null?a=a.sibling:a=r=a.return)}}var Zc=null;function lS(e){{if((Te&zt)!==ht||!(e.mode&Re))return;var t=e.tag;if(t!==V&&t!==M&&t!==w&&t!==U&&t!==Z&&t!==Be&&t!==Ce)return;var n=pe(e)||"ReactComponent";if(Zc!==null){if(Zc.has(n))return;Zc.add(n)}else Zc=new Set([n]);var a=Vt;try{Je(e),f("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")}finally{a?Je(e):Et()}}}var ah;{var UO=null;ah=function(e,t,n){var a=pS(UO,t);try{return bb(e,t,n)}catch(i){if(PD()||i!==null&&typeof i=="object"&&typeof i.then=="function")throw i;if(lc(),Cg(),xb(e,t),pS(t,a),t.mode&ke&&ip(t),Vf(null,bb,null,e,t,n),fE()){var r=Bf();typeof r=="object"&&r!==null&&r._suppressLogging&&typeof i=="object"&&i!==null&&!i._suppressLogging&&(i._suppressLogging=!0)}throw i}}}var uS=!1,rh;rh=new Set;function AO(e){if(Kr&&!E0())switch(e.tag){case U:case Z:case Ce:{var t=Ze&&pe(Ze)||"Unknown",n=t;if(!rh.has(n)){rh.add(n);var a=pe(e)||"Unknown";f("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render",a,t,t)}break}case w:{uS||(f("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),uS=!0);break}}}function _o(e,t){if(qn){var n=e.memoizedUpdaters;n.forEach(function(a){Lm(e,a,t)})}}var ih={};function lh(e,t){{var n=aa.current;return n!==null?(n.push(t),ih):mm(e,t)}}function oS(e){if(e!==ih)return SE(e)}function sS(){return aa.current!==null}function NO(e){{if(e.mode&Re){if(!Vb())return}else if(!tO()||Te!==ht||e.tag!==U&&e.tag!==Z&&e.tag!==Ce)return;if(aa.current===null){var t=Vt;try{Je(e),f(`An update to %s inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`,pe(e))}finally{t?Je(e):Et()}}}}function kO(e){e.tag!==xr&&Vb()&&aa.current===null&&f(`A suspended resource finished loading inside a test, but the event was not wrapped in act(...).

When testing, code that resolves suspended data should be wrapped into act(...):

act(() => {
  /* finish loading suspended data */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`)}function Lo(e){Gb=e}var Fn=null,Al=null,zO=function(e){Fn=e};function Nl(e){{if(Fn===null)return e;var t=Fn(e);return t===void 0?e:t.current}}function uh(e){return Nl(e)}function oh(e){{if(Fn===null)return e;var t=Fn(e);if(t===void 0){if(e!=null&&typeof e.render=="function"){var n=Nl(e.render);if(e.render!==n){var a={$$typeof:tt,render:n};return e.displayName!==void 0&&(a.displayName=e.displayName),a}}return e}return t.current}}function cS(e,t){{if(Fn===null)return!1;var n=e.elementType,a=t.type,r=!1,i=typeof a=="object"&&a!==null?a.$$typeof:null;switch(e.tag){case w:{typeof a=="function"&&(r=!0);break}case U:{(typeof a=="function"||i===Ot)&&(r=!0);break}case Z:{(i===tt||i===Ot)&&(r=!0);break}case Be:case Ce:{(i===Wr||i===Ot)&&(r=!0);break}default:return!1}if(r){var l=Fn(n);if(l!==void 0&&l===Fn(a))return!0}return!1}}function fS(e){{if(Fn===null||typeof WeakSet!="function")return;Al===null&&(Al=new WeakSet),Al.add(e)}}var HO=function(e,t){{if(Fn===null)return;var n=t.staleFamilies,a=t.updatedFamilies;Za(),Ja(function(){sh(e.current,a,n)})}},FO=function(e,t){{if(e.context!==xn)return;Za(),Ja(function(){Mo(t,e,null,null)})}};function sh(e,t,n){{var a=e.alternate,r=e.child,i=e.sibling,l=e.tag,u=e.type,o=null;switch(l){case U:case Ce:case w:o=u;break;case Z:o=u.render;break}if(Fn===null)throw new Error("Expected resolveFamily to be set during hot reload.");var d=!1,v=!1;if(o!==null){var b=Fn(o);b!==void 0&&(n.has(b)?v=!0:t.has(b)&&(l===w?v=!0:d=!0))}if(Al!==null&&(Al.has(e)||a!==null&&Al.has(a))&&(v=!0),v&&(e._debugNeedsRemount=!0),v||d){var g=sn(e,ce);g!==null&&gt(g,e,ce,Ge)}r!==null&&!v&&sh(r,t,n),i!==null&&sh(i,t,n)}}var jO=function(e,t){{var n=new Set,a=new Set(t.map(function(r){return r.current}));return ch(e.current,a,n),n}};function ch(e,t,n){{var a=e.child,r=e.sibling,i=e.tag,l=e.type,u=null;switch(i){case U:case Ce:case w:u=l;break;case Z:u=l.render;break}var o=!1;u!==null&&t.has(u)&&(o=!0),o?VO(e,n):a!==null&&ch(a,t,n),r!==null&&ch(r,t,n)}}function VO(e,t){{var n=BO(e,t);if(n)return;for(var a=e;;){switch(a.tag){case B:t.add(a.stateNode);return;case F:t.add(a.stateNode.containerInfo);return;case M:t.add(a.stateNode.containerInfo);return}if(a.return===null)throw new Error("Expected to reach root first.");a=a.return}}}function BO(e,t){for(var n=e,a=!1;;){if(n.tag===B)a=!0,t.add(n.stateNode);else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)return a;for(;n.sibling===null;){if(n.return===null||n.return===e)return a;n=n.return}n.sibling.return=n.return,n=n.sibling}return!1}var fh;{fh=!1;try{var dS=Object.preventExtensions({})}catch{fh=!0}}function $O(e,t,n,a){this.tag=e,this.key=n,this.elementType=null,this.type=null,this.stateNode=null,this.return=null,this.child=null,this.sibling=null,this.index=0,this.ref=null,this.pendingProps=t,this.memoizedProps=null,this.updateQueue=null,this.memoizedState=null,this.dependencies=null,this.mode=a,this.flags=le,this.subtreeFlags=le,this.deletions=null,this.lanes=N,this.childLanes=N,this.alternate=null,this.actualDuration=Number.NaN,this.actualStartTime=Number.NaN,this.selfBaseDuration=Number.NaN,this.treeBaseDuration=Number.NaN,this.actualDuration=0,this.actualStartTime=-1,this.selfBaseDuration=0,this.treeBaseDuration=0,this._debugSource=null,this._debugOwner=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,!fh&&typeof Object.preventExtensions=="function"&&Object.preventExtensions(this)}var Dn=function(e,t,n,a){return new $O(e,t,n,a)};function dh(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function YO(e){return typeof e=="function"&&!dh(e)&&e.defaultProps===void 0}function PO(e){if(typeof e=="function")return dh(e)?w:U;if(e!=null){var t=e.$$typeof;if(t===tt)return Z;if(t===Wr)return Be}return V}function wi(e,t){var n=e.alternate;n===null?(n=Dn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugSource=e._debugSource,n._debugOwner=e._debugOwner,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=le,n.subtreeFlags=le,n.deletions=null,n.actualDuration=0,n.actualStartTime=-1),n.flags=e.flags&ja,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue;var a=e.dependencies;switch(n.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case V:case U:case Ce:n.type=Nl(e.type);break;case w:n.type=uh(e.type);break;case Z:n.type=oh(e.type);break}return n}function qO(e,t){e.flags&=ja|ot;var n=e.alternate;if(n===null)e.childLanes=N,e.lanes=t,e.child=null,e.subtreeFlags=le,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0;else{e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=le,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type;var a=n.dependencies;e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration}return e}function GO(e,t,n){var a;return e===Xs?(a=Re,t===!0&&(a|=at,a|=pa)):a=re,qn&&(a|=ke),Dn(M,null,null,a)}function vh(e,t,n,a,r,i){var l=V,u=e;if(typeof e=="function")dh(e)?(l=w,u=uh(u)):u=Nl(u);else if(typeof e=="string")l=B;else e:switch(e){case ur:return Fr(n.children,r,i,t);case Vl:l=ne,r|=at,(r&Re)!==re&&(r|=pa);break;case Ai:return QO(n,r,i,t);case ki:return WO(n,r,i,t);case zi:return IO(n,r,i,t);case Yo:return vS(n,r,i,t);case mf:case pf:case yf:case gf:case hf:default:{if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ni:l=I;break e;case Bl:l=we;break e;case tt:l=Z,u=oh(u);break e;case Wr:l=Be;break e;case Ot:l=Ft,u=null;break e}var o="";{(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(o+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var d=a?pe(a):null;d&&(o+=`

Check the render method of \``+d+"`.")}throw new Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) "+("but got: "+(e==null?e:typeof e)+"."+o))}}var v=Dn(l,n,t,r);return v.elementType=e,v.type=u,v.lanes=i,v._debugOwner=a,v}function ph(e,t,n){var a=null;a=e._owner;var r=e.type,i=e.key,l=e.props,u=vh(r,i,l,a,t,n);return u._debugSource=e._source,u._debugOwner=e._owner,u}function Fr(e,t,n,a){var r=Dn(ye,e,a,t);return r.lanes=n,r}function QO(e,t,n,a){typeof e.id!="string"&&f('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id);var r=Dn(ve,e,a,t|ke);return r.elementType=Ai,r.lanes=n,r.stateNode={effectDuration:0,passiveEffectDuration:0},r}function WO(e,t,n,a){var r=Dn(ie,e,a,t);return r.elementType=ki,r.lanes=n,r}function IO(e,t,n,a){var r=Dn(qe,e,a,t);return r.elementType=zi,r.lanes=n,r}function vS(e,t,n,a){var r=Dn(Fe,e,a,t);r.elementType=Yo,r.lanes=n;var i={isHidden:!1};return r.stateNode=i,r}function hh(e,t,n){var a=Dn(J,e,null,t);return a.lanes=n,a}function XO(){var e=Dn(B,null,null,re);return e.elementType="DELETED",e}function KO(e){var t=Dn(et,null,null,re);return t.stateNode=e,t}function mh(e,t,n){var a=e.children!==null?e.children:[],r=Dn(F,a,e.key,t);return r.lanes=n,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function pS(e,t){return e===null&&(e=Dn(V,null,null,re)),e.tag=t.tag,e.key=t.key,e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.return=t.return,e.child=t.child,e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.pendingProps=t.pendingProps,e.memoizedProps=t.memoizedProps,e.updateQueue=t.updateQueue,e.memoizedState=t.memoizedState,e.dependencies=t.dependencies,e.mode=t.mode,e.flags=t.flags,e.subtreeFlags=t.subtreeFlags,e.deletions=t.deletions,e.lanes=t.lanes,e.childLanes=t.childLanes,e.alternate=t.alternate,e.actualDuration=t.actualDuration,e.actualStartTime=t.actualStartTime,e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration,e._debugSource=t._debugSource,e._debugOwner=t._debugOwner,e._debugNeedsRemount=t._debugNeedsRemount,e._debugHookTypes=t._debugHookTypes,e}function JO(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.pendingChildren=null,this.current=null,this.pingCache=null,this.finishedWork=null,this.timeoutHandle=Kd,this.context=null,this.pendingContext=null,this.callbackNode=null,this.callbackPriority=xt,this.eventTimes=Sd(N),this.expirationTimes=Sd(Ge),this.pendingLanes=N,this.suspendedLanes=N,this.pingedLanes=N,this.expiredLanes=N,this.mutableReadLanes=N,this.finishedLanes=N,this.entangledLanes=N,this.entanglements=Sd(N),this.identifierPrefix=a,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null,this.effectDuration=0,this.passiveEffectDuration=0;{this.memoizedUpdaters=new Set;for(var i=this.pendingUpdatersLaneMap=[],l=0;l<Kf;l++)i.push(new Set)}switch(t){case Xs:this._debugRootType=n?"hydrateRoot()":"createRoot()";break;case xr:this._debugRootType=n?"hydrate()":"render()";break}}function hS(e,t,n,a,r,i,l,u,o,d){var v=new JO(e,t,n,u,o),b=GO(t,i);v.current=b,b.stateNode=v;{var g={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null};b.memoizedState=g}return _v(b),v}var yh="18.3.1";function ZO(e,t,n){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;return Gr(a),{$$typeof:_a,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var gh,bh;gh=!1,bh={};function mS(e){if(!e)return xn;var t=Wi(e),n=kD(t);if(t.tag===w){var a=t.type;if(ya(a))return Yy(t,a,n)}return n}function e_(e,t){{var n=Wi(e);if(n===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var a=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+a)}var r=vm(n);if(r===null)return null;if(r.mode&at){var i=pe(n)||"Component";if(!bh[i]){bh[i]=!0;var l=Vt;try{Je(r),n.mode&at?f("%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i):f("%s is deprecated in StrictMode. %s was passed an instance of %s which renders StrictMode children. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i)}finally{l?Je(l):Et()}}}return r.stateNode}}function yS(e,t,n,a,r,i,l,u){var o=!1,d=null;return hS(e,t,o,d,n,a,r,i,l)}function gS(e,t,n,a,r,i,l,u,o,d){var v=!0,b=hS(n,a,v,e,r,i,l,u,o);b.context=mS(null);var g=b.current,R=en(),D=zr(g),_=Wa(R,D);return _.callback=t??null,Or(g,_,D),uO(b,D,R),b}function Mo(e,t,n,a){OE(t,e);var r=t.current,i=en(),l=zr(r);WE(l);var u=mS(n);t.context===null?t.context=u:t.pendingContext=u,Kr&&Vt!==null&&!gh&&(gh=!0,f(`Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.

Check the render method of %s.`,pe(Vt)||"Unknown"));var o=Wa(i,l);o.payload={element:e},a=a===void 0?null:a,a!==null&&(typeof a!="function"&&f("render(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",a),o.callback=a);var d=Or(r,o,l);return d!==null&&(gt(d,r,l,i),fc(d,r,l)),l}function ef(e){var t=e.current;if(!t.child)return null;switch(t.child.tag){case B:return t.child.stateNode;default:return t.child.stateNode}}function t_(e){switch(e.tag){case M:{var t=e.stateNode;if(Ss(t)){var n=aR(t);fO(t,n)}break}case ie:{Ja(function(){var r=sn(e,ce);if(r!==null){var i=en();gt(r,e,ce,i)}});var a=ce;Sh(e,a);break}}}function bS(e,t){var n=e.memoizedState;n!==null&&n.dehydrated!==null&&(n.retryLane=sR(n.retryLane,t))}function Sh(e,t){bS(e,t);var n=e.alternate;n&&bS(n,t)}function n_(e){if(e.tag===ie){var t=hu,n=sn(e,t);if(n!==null){var a=en();gt(n,e,t,a)}Sh(e,t)}}function a_(e){if(e.tag===ie){var t=zr(e),n=sn(e,t);if(n!==null){var a=en();gt(n,e,t,a)}Sh(e,t)}}function SS(e){var t=bE(e);return t===null?null:t.stateNode}var CS=function(e){return null};function r_(e){return CS(e)}var ES=function(e){return!1};function i_(e){return ES(e)}var RS=null,TS=null,xS=null,DS=null,wS=null,OS=null,_S=null,LS=null,MS=null;{var US=function(e,t,n){var a=t[n],r=Me(e)?e.slice():Se({},e);return n+1===t.length?(Me(r)?r.splice(a,1):delete r[a],r):(r[a]=US(e[a],t,n+1),r)},AS=function(e,t){return US(e,t,0)},NS=function(e,t,n,a){var r=t[a],i=Me(e)?e.slice():Se({},e);if(a+1===t.length){var l=n[a];i[l]=i[r],Me(i)?i.splice(r,1):delete i[r]}else i[r]=NS(e[r],t,n,a+1);return i},kS=function(e,t,n){if(t.length!==n.length){T("copyWithRename() expects paths of the same length");return}else for(var a=0;a<n.length-1;a++)if(t[a]!==n[a]){T("copyWithRename() expects paths to be the same except for the deepest key");return}return NS(e,t,n,0)},zS=function(e,t,n,a){if(n>=t.length)return a;var r=t[n],i=Me(e)?e.slice():Se({},e);return i[r]=zS(e[r],t,n+1,a),i},HS=function(e,t,n){return zS(e,t,0,n)},Ch=function(e,t){for(var n=e.memoizedState;n!==null&&t>0;)n=n.next,t--;return n};RS=function(e,t,n,a){var r=Ch(e,t);if(r!==null){var i=HS(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Se({},e.memoizedProps);var l=sn(e,ce);l!==null&&gt(l,e,ce,Ge)}},TS=function(e,t,n){var a=Ch(e,t);if(a!==null){var r=AS(a.memoizedState,n);a.memoizedState=r,a.baseState=r,e.memoizedProps=Se({},e.memoizedProps);var i=sn(e,ce);i!==null&&gt(i,e,ce,Ge)}},xS=function(e,t,n,a){var r=Ch(e,t);if(r!==null){var i=kS(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Se({},e.memoizedProps);var l=sn(e,ce);l!==null&&gt(l,e,ce,Ge)}},DS=function(e,t,n){e.pendingProps=HS(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=sn(e,ce);a!==null&&gt(a,e,ce,Ge)},wS=function(e,t){e.pendingProps=AS(e.memoizedProps,t),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var n=sn(e,ce);n!==null&&gt(n,e,ce,Ge)},OS=function(e,t,n){e.pendingProps=kS(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=sn(e,ce);a!==null&&gt(a,e,ce,Ge)},_S=function(e){var t=sn(e,ce);t!==null&&gt(t,e,ce,Ge)},LS=function(e){CS=e},MS=function(e){ES=e}}function l_(e){var t=vm(e);return t===null?null:t.stateNode}function u_(e){return null}function o_(){return Vt}function s_(e){var t=e.findFiberByHostInstance,n=m.ReactCurrentDispatcher;return wE({bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:RS,overrideHookStateDeletePath:TS,overrideHookStateRenamePath:xS,overrideProps:DS,overridePropsDeletePath:wS,overridePropsRenamePath:OS,setErrorHandler:LS,setSuspenseHandler:MS,scheduleUpdate:_S,currentDispatcherRef:n,findHostInstanceByFiber:l_,findFiberByHostInstance:t||u_,findHostInstancesForRefresh:jO,scheduleRefresh:HO,scheduleRoot:FO,setRefreshHandler:zO,getCurrentFiber:o_,reconcilerVersion:yh})}var FS=typeof reportError=="function"?reportError:function(e){console.error(e)};function Eh(e){this._internalRoot=e}tf.prototype.render=Eh.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw new Error("Cannot update an unmounted root.");{typeof arguments[1]=="function"?f("render(...): does not support the second callback argument. To execute a side effect after rendering, declare it in a component body with useEffect()."):nf(arguments[1])?f("You passed a container to the second argument of root.render(...). You don't need to pass it again since you already passed it to create the root."):typeof arguments[1]<"u"&&f("You passed a second argument to root.render(...) but it only accepts one argument.");var n=t.containerInfo;if(n.nodeType!==ut){var a=SS(t.current);a&&a.parentNode!==n&&f("render(...): It looks like the React-rendered content of the root container was removed without using React. This is not supported and will cause errors. Instead, call root.unmount() to empty a root's container.")}}Mo(e,t,null,null)},tf.prototype.unmount=Eh.prototype.unmount=function(){typeof arguments[0]=="function"&&f("unmount(...): does not support a callback argument. To execute a side effect after rendering, declare it in a component body with useEffect().");var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Xb()&&f("Attempted to synchronously unmount a root while React was already rendering. React cannot finish unmounting the root until the current render has completed, which may lead to a race condition."),Ja(function(){Mo(null,e,null,null)}),Fy(t)}};function c_(e,t){if(!nf(e))throw new Error("createRoot(...): Target container is not a DOM element.");jS(e);var n=!1,a=!1,r="",i=FS;t!=null&&(t.hydrate?T("hydrate through createRoot is deprecated. Use ReactDOMClient.hydrateRoot(container, <App />) instead."):typeof t=="object"&&t!==null&&t.$$typeof===lr&&f(`You passed a JSX element to createRoot. You probably meant to call root.render instead. Example usage:

  let root = createRoot(domContainer);
  root.render(<App />);`),t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.transitionCallbacks!==void 0&&t.transitionCallbacks);var l=yS(e,Xs,null,n,a,r,i);Ys(l.current,e);var u=e.nodeType===ut?e.parentNode:e;return zu(u),new Eh(l)}function tf(e){this._internalRoot=e}function f_(e){e&&DR(e)}tf.prototype.unstable_scheduleHydration=f_;function d_(e,t,n){if(!nf(e))throw new Error("hydrateRoot(...): Target container is not a DOM element.");jS(e),t===void 0&&f("Must provide initial children as second argument to hydrateRoot. Example usage: hydrateRoot(domContainer, <App />)");var a=n??null,r=n!=null&&n.hydratedSources||null,i=!1,l=!1,u="",o=FS;n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError));var d=gS(t,null,e,Xs,a,i,l,u,o);if(Ys(d.current,e),zu(e),r)for(var v=0;v<r.length;v++){var b=r[v];m0(d,b)}return new tf(d)}function nf(e){return!!(e&&(e.nodeType===un||e.nodeType===ka||e.nodeType===_f||!Wt))}function Uo(e){return!!(e&&(e.nodeType===un||e.nodeType===ka||e.nodeType===_f||e.nodeType===ut&&e.nodeValue===" react-mount-point-unstable "))}function jS(e){e.nodeType===un&&e.tagName&&e.tagName.toUpperCase()==="BODY"&&f("createRoot(): Creating roots directly with document.body is discouraged, since its children are often manipulated by third-party scripts and browser extensions. This may lead to subtle reconciliation issues. Try using a container element created for your app."),Qu(e)&&(e._reactRootContainer?f("You are calling ReactDOMClient.createRoot() on a container that was previously passed to ReactDOM.render(). This is not supported."):f("You are calling ReactDOMClient.createRoot() on a container that has already been passed to createRoot() before. Instead, call root.render() on the existing root instead if you want to update it."))}var v_=m.ReactCurrentOwner,VS;VS=function(e){if(e._reactRootContainer&&e.nodeType!==ut){var t=SS(e._reactRootContainer.current);t&&t.parentNode!==e&&f("render(...): It looks like the React-rendered content of this container was removed without using React. This is not supported and will cause errors. Instead, call ReactDOM.unmountComponentAtNode to empty a container.")}var n=!!e._reactRootContainer,a=Rh(e),r=!!(a&&Rr(a));r&&!n&&f("render(...): Replacing React-rendered children with a new root component. If you intended to update the children of this node, you should instead have the existing children update their state and render the new components instead of calling ReactDOM.render."),e.nodeType===un&&e.tagName&&e.tagName.toUpperCase()==="BODY"&&f("render(): Rendering components directly into document.body is discouraged, since its children are often manipulated by third-party scripts and browser extensions. This may lead to subtle reconciliation issues. Try rendering into a container element created for your app.")};function Rh(e){return e?e.nodeType===ka?e.documentElement:e.firstChild:null}function BS(){}function p_(e,t,n,a,r){if(r){if(typeof a=="function"){var i=a;a=function(){var g=ef(l);i.call(g)}}var l=gS(t,a,e,xr,null,!1,!1,"",BS);e._reactRootContainer=l,Ys(l.current,e);var u=e.nodeType===ut?e.parentNode:e;return zu(u),Ja(),l}else{for(var o;o=e.lastChild;)e.removeChild(o);if(typeof a=="function"){var d=a;a=function(){var g=ef(v);d.call(g)}}var v=yS(e,xr,null,!1,!1,"",BS);e._reactRootContainer=v,Ys(v.current,e);var b=e.nodeType===ut?e.parentNode:e;return zu(b),Ja(function(){Mo(t,v,n,a)}),v}}function h_(e,t){e!==null&&typeof e!="function"&&f("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e)}function af(e,t,n,a,r){VS(n),h_(r===void 0?null:r,"render");var i=n._reactRootContainer,l;if(!i)l=p_(n,t,e,r,a);else{if(l=i,typeof r=="function"){var u=r;r=function(){var o=ef(l);u.call(o)}}Mo(t,l,e,r)}return ef(l)}var $S=!1;function m_(e){{$S||($S=!0,f("findDOMNode is deprecated and will be removed in the next major release. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node"));var t=v_.current;if(t!==null&&t.stateNode!==null){var n=t.stateNode._warnedAboutRefsInRender;n||f("%s is accessing findDOMNode inside its render(). render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",Oe(t.type)||"A component"),t.stateNode._warnedAboutRefsInRender=!0}}return e==null?null:e.nodeType===un?e:e_(e,"findDOMNode")}function y_(e,t,n){if(f("ReactDOM.hydrate is no longer supported in React 18. Use hydrateRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(t))throw new Error("Target container is not a DOM element.");{var a=Qu(t)&&t._reactRootContainer===void 0;a&&f("You are calling ReactDOM.hydrate() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call hydrateRoot(container, element)?")}return af(null,e,t,!0,n)}function g_(e,t,n){if(f("ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(t))throw new Error("Target container is not a DOM element.");{var a=Qu(t)&&t._reactRootContainer===void 0;a&&f("You are calling ReactDOM.render() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call root.render(element)?")}return af(null,e,t,!1,n)}function b_(e,t,n,a){if(f("ReactDOM.unstable_renderSubtreeIntoContainer() is no longer supported in React 18. Consider using a portal instead. Until you switch to the createRoot API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot"),!Uo(n))throw new Error("Target container is not a DOM element.");if(e==null||!dE(e))throw new Error("parentComponent must be a valid React Component");return af(e,t,n,!1,a)}var YS=!1;function S_(e){if(YS||(YS=!0,f("unmountComponentAtNode is deprecated and will be removed in the next major release. Switch to the createRoot API. Learn more: https://reactjs.org/link/switch-to-createroot")),!Uo(e))throw new Error("unmountComponentAtNode(...): Target container is not a DOM element.");{var t=Qu(e)&&e._reactRootContainer===void 0;t&&f("You are calling ReactDOM.unmountComponentAtNode() on a container that was previously passed to ReactDOMClient.createRoot(). This is not supported. Did you mean to call root.unmount()?")}if(e._reactRootContainer){{var n=Rh(e),a=n&&!Rr(n);a&&f("unmountComponentAtNode(): The node you're attempting to unmount was rendered by another copy of React.")}return Ja(function(){af(null,null,e,!1,function(){e._reactRootContainer=null,Fy(e)})}),!0}else{{var r=Rh(e),i=!!(r&&Rr(r)),l=e.nodeType===un&&Uo(e.parentNode)&&!!e.parentNode._reactRootContainer;i&&f("unmountComponentAtNode(): The node you're attempting to unmount was rendered by React and is not a top-level container. %s",l?"You may have accidentally passed in a React root node instead of its container.":"Instead, have the parent component update its state and rerender in order to remove this component.")}return!1}}mR(t_),gR(n_),bR(a_),SR(Gn),CR(vR),(typeof Map!="function"||Map.prototype==null||typeof Map.prototype.forEach!="function"||typeof Set!="function"||Set.prototype==null||typeof Set.prototype.clear!="function"||typeof Set.prototype.forEach!="function")&&f("React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),tE(Ex),rE(eh,dO,Ja);function C_(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!nf(t))throw new Error("Target container is not a DOM element.");return ZO(e,t,null,n)}function E_(e,t,n,a){return b_(e,t,n,a)}var Th={usingClientEntryPoint:!1,Events:[Rr,fl,Ps,tm,nm,eh]};function R_(e,t){return Th.usingClientEntryPoint||f('You are importing createRoot from "react-dom" which is not supported. You should instead import it from "react-dom/client".'),c_(e,t)}function T_(e,t,n){return Th.usingClientEntryPoint||f('You are importing hydrateRoot from "react-dom" which is not supported. You should instead import it from "react-dom/client".'),d_(e,t,n)}function x_(e){return Xb()&&f("flushSync was called from inside a lifecycle method. React cannot flush when React is already rendering. Consider moving this call to a scheduler task or micro task."),Ja(e)}var D_=s_({findFiberByHostInstance:di,bundleType:1,version:yh,rendererPackageName:"react-dom"});if(!D_&&St&&window.top===window.self&&(navigator.userAgent.indexOf("Chrome")>-1&&navigator.userAgent.indexOf("Edge")===-1||navigator.userAgent.indexOf("Firefox")>-1)){var PS=window.location.protocol;/^(https?|file):$/.test(PS)&&console.info("%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools"+(PS==="file:"?`
You might need to use a local HTTP server (instead of file://): https://reactjs.org/link/react-devtools-faq`:""),"font-weight:bold")}On.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Th,On.createPortal=C_,On.createRoot=R_,On.findDOMNode=m_,On.flushSync=x_,On.hydrate=y_,On.hydrateRoot=T_,On.render=g_,On.unmountComponentAtNode=S_,On.unstable_batchedUpdates=eh,On.unstable_renderSubtreeIntoContainer=E_,On.version=yh,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})();eC.exports=On;var N_=eC.exports;const nM=JS(N_);/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function No(){return No=Object.assign?Object.assign.bind():function(c){for(var p=1;p<arguments.length;p++){var m=arguments[p];for(var y in m)Object.prototype.hasOwnProperty.call(m,y)&&(c[y]=m[y])}return c},No.apply(this,arguments)}var jr;(function(c){c.Pop="POP",c.Push="PUSH",c.Replace="REPLACE"})(jr||(jr={}));const qS="popstate";function k_(c){c===void 0&&(c={});function p(y,S){let{pathname:T,search:f,hash:k}=y.location;return wh("",{pathname:T,search:f,hash:k},S.state&&S.state.usr||null,S.state&&S.state.key||"default")}function m(y,S){return typeof S=="string"?S:ko(S)}return H_(p,m,null,c)}function Xe(c,p){if(c===!1||c===null||typeof c>"u")throw new Error(p)}function jn(c,p){if(!c){typeof console<"u"&&console.warn(p);try{throw new Error(p)}catch{}}}function z_(){return Math.random().toString(36).substr(2,8)}function GS(c,p){return{usr:c.state,key:c.key,idx:p}}function wh(c,p,m,y){return m===void 0&&(m=null),No({pathname:typeof c=="string"?c:c.pathname,search:"",hash:""},typeof p=="string"?Hl(p):p,{state:m,key:p&&p.key||y||z_()})}function ko(c){let{pathname:p="/",search:m="",hash:y=""}=c;return m&&m!=="?"&&(p+=m.charAt(0)==="?"?m:"?"+m),y&&y!=="#"&&(p+=y.charAt(0)==="#"?y:"#"+y),p}function Hl(c){let p={};if(c){let m=c.indexOf("#");m>=0&&(p.hash=c.substr(m),c=c.substr(0,m));let y=c.indexOf("?");y>=0&&(p.search=c.substr(y),c=c.substr(0,y)),c&&(p.pathname=c)}return p}function H_(c,p,m,y){y===void 0&&(y={});let{window:S=document.defaultView,v5Compat:T=!1}=y,f=S.history,k=jr.Pop,U=null,w=V();w==null&&(w=0,f.replaceState(No({},f.state,{idx:w}),""));function V(){return(f.state||{idx:null}).idx}function M(){k=jr.Pop;let ne=V(),we=ne==null?null:ne-w;w=ne,U&&U({action:k,location:ye.location,delta:we})}function F(ne,we){k=jr.Push;let I=wh(ye.location,ne,we);w=V()+1;let Z=GS(I,w),ve=ye.createHref(I);try{f.pushState(Z,"",ve)}catch(ie){if(ie instanceof DOMException&&ie.name==="DataCloneError")throw ie;S.location.assign(ve)}T&&U&&U({action:k,location:ye.location,delta:1})}function B(ne,we){k=jr.Replace;let I=wh(ye.location,ne,we);w=V();let Z=GS(I,w),ve=ye.createHref(I);f.replaceState(Z,"",ve),T&&U&&U({action:k,location:ye.location,delta:0})}function J(ne){let we=S.location.origin!=="null"?S.location.origin:S.location.href,I=typeof ne=="string"?ne:ko(ne);return I=I.replace(/ $/,"%20"),Xe(we,"No window.location.(origin|href) available to create URL for href: "+I),new URL(I,we)}let ye={get action(){return k},get location(){return c(S,f)},listen(ne){if(U)throw new Error("A history only accepts one active listener");return S.addEventListener(qS,M),U=ne,()=>{S.removeEventListener(qS,M),U=null}},createHref(ne){return p(S,ne)},createURL:J,encodeLocation(ne){let we=J(ne);return{pathname:we.pathname,search:we.search,hash:we.hash}},push:F,replace:B,go(ne){return f.go(ne)}};return ye}var QS;(function(c){c.data="data",c.deferred="deferred",c.redirect="redirect",c.error="error"})(QS||(QS={}));function F_(c,p,m){return m===void 0&&(m="/"),j_(c,p,m,!1)}function j_(c,p,m,y){let S=typeof p=="string"?Hl(p):p,T=Vr(S.pathname||"/",m);if(T==null)return null;let f=aC(c);V_(f);let k=null;for(let U=0;k==null&&U<f.length;++U){let w=K_(T);k=I_(f[U],w,y)}return k}function aC(c,p,m,y){p===void 0&&(p=[]),m===void 0&&(m=[]),y===void 0&&(y="");let S=(T,f,k)=>{let U={relativePath:k===void 0?T.path||"":k,caseSensitive:T.caseSensitive===!0,childrenIndex:f,route:T};U.relativePath.startsWith("/")&&(Xe(U.relativePath.startsWith(y),'Absolute route path "'+U.relativePath+'" nested under path '+('"'+y+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),U.relativePath=U.relativePath.slice(y.length));let w=tr([y,U.relativePath]),V=m.concat(U);T.children&&T.children.length>0&&(Xe(T.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+w+'".')),aC(T.children,p,V,w)),!(T.path==null&&!T.index)&&p.push({path:w,score:Q_(w,T.index),routesMeta:V})};return c.forEach((T,f)=>{var k;if(T.path===""||!((k=T.path)!=null&&k.includes("?")))S(T,f);else for(let U of rC(T.path))S(T,f,U)}),p}function rC(c){let p=c.split("/");if(p.length===0)return[];let[m,...y]=p,S=m.endsWith("?"),T=m.replace(/\?$/,"");if(y.length===0)return S?[T,""]:[T];let f=rC(y.join("/")),k=[];return k.push(...f.map(U=>U===""?T:[T,U].join("/"))),S&&k.push(...f),k.map(U=>c.startsWith("/")&&U===""?"/":U)}function V_(c){c.sort((p,m)=>p.score!==m.score?m.score-p.score:W_(p.routesMeta.map(y=>y.childrenIndex),m.routesMeta.map(y=>y.childrenIndex)))}const B_=/^:[\w-]+$/,$_=3,Y_=2,P_=1,q_=10,G_=-2,WS=c=>c==="*";function Q_(c,p){let m=c.split("/"),y=m.length;return m.some(WS)&&(y+=G_),p&&(y+=Y_),m.filter(S=>!WS(S)).reduce((S,T)=>S+(B_.test(T)?$_:T===""?P_:q_),y)}function W_(c,p){return c.length===p.length&&c.slice(0,-1).every((y,S)=>y===p[S])?c[c.length-1]-p[p.length-1]:0}function I_(c,p,m){let{routesMeta:y}=c,S={},T="/",f=[];for(let k=0;k<y.length;++k){let U=y[k],w=k===y.length-1,V=T==="/"?p:p.slice(T.length)||"/",M=sf({path:U.relativePath,caseSensitive:U.caseSensitive,end:w},V),F=U.route;if(!M&&w&&m&&!y[y.length-1].route.index&&(M=sf({path:U.relativePath,caseSensitive:U.caseSensitive,end:!1},V)),!M)return null;Object.assign(S,M.params),f.push({params:S,pathname:tr([T,M.pathname]),pathnameBase:tL(tr([T,M.pathnameBase])),route:F}),M.pathnameBase!=="/"&&(T=tr([T,M.pathnameBase]))}return f}function sf(c,p){typeof c=="string"&&(c={path:c,caseSensitive:!1,end:!0});let[m,y]=X_(c.path,c.caseSensitive,c.end),S=p.match(m);if(!S)return null;let T=S[0],f=T.replace(/(.)\/+$/,"$1"),k=S.slice(1);return{params:y.reduce((w,V,M)=>{let{paramName:F,isOptional:B}=V;if(F==="*"){let ye=k[M]||"";f=T.slice(0,T.length-ye.length).replace(/(.)\/+$/,"$1")}const J=k[M];return B&&!J?w[F]=void 0:w[F]=(J||"").replace(/%2F/g,"/"),w},{}),pathname:T,pathnameBase:f,pattern:c}}function X_(c,p,m){p===void 0&&(p=!1),m===void 0&&(m=!0),jn(c==="*"||!c.endsWith("*")||c.endsWith("/*"),'Route path "'+c+'" will be treated as if it were '+('"'+c.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+c.replace(/\*$/,"/*")+'".'));let y=[],S="^"+c.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,k,U)=>(y.push({paramName:k,isOptional:U!=null}),U?"/?([^\\/]+)?":"/([^\\/]+)"));return c.endsWith("*")?(y.push({paramName:"*"}),S+=c==="*"||c==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):m?S+="\\/*$":c!==""&&c!=="/"&&(S+="(?:(?=\\/|$))"),[new RegExp(S,p?void 0:"i"),y]}function K_(c){try{return c.split("/").map(p=>decodeURIComponent(p).replace(/\//g,"%2F")).join("/")}catch(p){return jn(!1,'The URL path "'+c+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+p+").")),c}}function Vr(c,p){if(p==="/")return c;if(!c.toLowerCase().startsWith(p.toLowerCase()))return null;let m=p.endsWith("/")?p.length-1:p.length,y=c.charAt(m);return y&&y!=="/"?null:c.slice(m)||"/"}function J_(c,p){p===void 0&&(p="/");let{pathname:m,search:y="",hash:S=""}=typeof c=="string"?Hl(c):c;return{pathname:m?m.startsWith("/")?m:Z_(m,p):p,search:nL(y),hash:aL(S)}}function Z_(c,p){let m=p.replace(/\/+$/,"").split("/");return c.split("/").forEach(S=>{S===".."?m.length>1&&m.pop():S!=="."&&m.push(S)}),m.length>1?m.join("/"):"/"}function xh(c,p,m,y){return"Cannot include a '"+c+"' character in a manually specified "+("`to."+p+"` field ["+JSON.stringify(y)+"].  Please separate it out to the ")+("`to."+m+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function eL(c){return c.filter((p,m)=>m===0||p.route.path&&p.route.path.length>0)}function Lh(c,p){let m=eL(c);return p?m.map((y,S)=>S===m.length-1?y.pathname:y.pathnameBase):m.map(y=>y.pathnameBase)}function Mh(c,p,m,y){y===void 0&&(y=!1);let S;typeof c=="string"?S=Hl(c):(S=No({},c),Xe(!S.pathname||!S.pathname.includes("?"),xh("?","pathname","search",S)),Xe(!S.pathname||!S.pathname.includes("#"),xh("#","pathname","hash",S)),Xe(!S.search||!S.search.includes("#"),xh("#","search","hash",S)));let T=c===""||S.pathname==="",f=T?"/":S.pathname,k;if(f==null)k=m;else{let M=p.length-1;if(!y&&f.startsWith("..")){let F=f.split("/");for(;F[0]==="..";)F.shift(),M-=1;S.pathname=F.join("/")}k=M>=0?p[M]:"/"}let U=J_(S,k),w=f&&f!=="/"&&f.endsWith("/"),V=(T||f===".")&&m.endsWith("/");return!U.pathname.endsWith("/")&&(w||V)&&(U.pathname+="/"),U}const tr=c=>c.join("/").replace(/\/\/+/g,"/"),tL=c=>c.replace(/\/+$/,"").replace(/^\/*/,"/"),nL=c=>!c||c==="?"?"":c.startsWith("?")?c:"?"+c,aL=c=>!c||c==="#"?"":c.startsWith("#")?c:"#"+c;function rL(c){return c!=null&&typeof c.status=="number"&&typeof c.statusText=="string"&&typeof c.internal=="boolean"&&"data"in c}const iC=["post","put","patch","delete"];new Set(iC);const iL=["get",...iC];new Set(iL);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function zo(){return zo=Object.assign?Object.assign.bind():function(c){for(var p=1;p<arguments.length;p++){var m=arguments[p];for(var y in m)Object.prototype.hasOwnProperty.call(m,y)&&(c[y]=m[y])}return c},zo.apply(this,arguments)}const Fo=H.createContext(null);Fo.displayName="DataRouter";const Uh=H.createContext(null);Uh.displayName="DataRouterState";const lL=H.createContext(null);lL.displayName="Await";const Vn=H.createContext(null);Vn.displayName="Navigation";const jo=H.createContext(null);jo.displayName="Location";const ra=H.createContext({outlet:null,matches:[],isDataRoute:!1});ra.displayName="Route";const Ah=H.createContext(null);Ah.displayName="RouteError";function uL(c,p){let{relative:m}=p===void 0?{}:p;Fl()||Xe(!1,"useHref() may be used only in the context of a <Router> component.");let{basename:y,navigator:S}=H.useContext(Vn),{hash:T,pathname:f,search:k}=Vo(c,{relative:m}),U=f;return y!=="/"&&(U=f==="/"?y:tr([y,f])),S.createHref({pathname:U,search:k,hash:T})}function Fl(){return H.useContext(jo)!=null}function Br(){return Fl()||Xe(!1,"useLocation() may be used only in the context of a <Router> component."),H.useContext(jo).location}const lC="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function uC(c){H.useContext(Vn).static||H.useLayoutEffect(c)}function Nh(){let{isDataRoute:c}=H.useContext(ra);return c?CL():oL()}function oL(){Fl()||Xe(!1,"useNavigate() may be used only in the context of a <Router> component.");let c=H.useContext(Fo),{basename:p,future:m,navigator:y}=H.useContext(Vn),{matches:S}=H.useContext(ra),{pathname:T}=Br(),f=JSON.stringify(Lh(S,m.v7_relativeSplatPath)),k=H.useRef(!1);return uC(()=>{k.current=!0}),H.useCallback(function(w,V){if(V===void 0&&(V={}),jn(k.current,lC),!k.current)return;if(typeof w=="number"){y.go(w);return}let M=Mh(w,JSON.parse(f),T,V.relative==="path");c==null&&p!=="/"&&(M.pathname=M.pathname==="/"?p:tr([p,M.pathname])),(V.replace?y.replace:y.push)(M,V.state,V)},[p,y,f,T,c])}function aM(){let{matches:c}=H.useContext(ra),p=c[c.length-1];return p?p.params:{}}function Vo(c,p){let{relative:m}=p===void 0?{}:p,{future:y}=H.useContext(Vn),{matches:S}=H.useContext(ra),{pathname:T}=Br(),f=JSON.stringify(Lh(S,y.v7_relativeSplatPath));return H.useMemo(()=>Mh(c,JSON.parse(f),T,m==="path"),[c,f,T,m])}function sL(c,p){return cL(c,p)}function cL(c,p,m,y){Fl()||Xe(!1,"useRoutes() may be used only in the context of a <Router> component.");let{navigator:S}=H.useContext(Vn),{matches:T}=H.useContext(ra),f=T[T.length-1],k=f?f.params:{},U=f?f.pathname:"/",w=f?f.pathnameBase:"/",V=f&&f.route;{let I=V&&V.path||"";sC(U,!V||I.endsWith("*"),"You rendered descendant <Routes> (or called `useRoutes()`) at "+('"'+U+'" (under <Route path="'+I+'">) but the ')+`parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

`+('Please change the parent <Route path="'+I+'"> to <Route ')+('path="'+(I==="/"?"*":I+"/*")+'">.'))}let M=Br(),F;if(p){var B;let I=typeof p=="string"?Hl(p):p;w==="/"||(B=I.pathname)!=null&&B.startsWith(w)||Xe(!1,"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was "+('matched by all parent routes. The current pathname base is "'+w+'" ')+('but pathname "'+I.pathname+'" was given in the `location` prop.')),F=I}else F=M;let J=F.pathname||"/",ye=J;if(w!=="/"){let I=w.replace(/^\//,"").split("/");ye="/"+J.replace(/^\//,"").split("/").slice(I.length).join("/")}let ne=F_(c,{pathname:ye});jn(V||ne!=null,'No routes matched location "'+F.pathname+F.search+F.hash+'" '),jn(ne==null||ne[ne.length-1].route.element!==void 0||ne[ne.length-1].route.Component!==void 0||ne[ne.length-1].route.lazy!==void 0,'Matched leaf route at location "'+F.pathname+F.search+F.hash+'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.');let we=hL(ne&&ne.map(I=>Object.assign({},I,{params:Object.assign({},k,I.params),pathname:tr([w,S.encodeLocation?S.encodeLocation(I.pathname).pathname:I.pathname]),pathnameBase:I.pathnameBase==="/"?w:tr([w,S.encodeLocation?S.encodeLocation(I.pathnameBase).pathname:I.pathnameBase])})),T,m,y);return p&&we?H.createElement(jo.Provider,{value:{location:zo({pathname:"/",search:"",hash:"",state:null,key:"default"},F),navigationType:jr.Pop}},we):we}function fL(){let c=SL(),p=rL(c)?c.status+" "+c.statusText:c instanceof Error?c.message:JSON.stringify(c),m=c instanceof Error?c.stack:null,y="rgba(200,200,200, 0.5)",S={padding:"0.5rem",backgroundColor:y},T={padding:"2px 4px",backgroundColor:y},f=null;return console.error("Error handled by React Router default ErrorBoundary:",c),f=H.createElement(H.Fragment,null,H.createElement("p",null,"💿 Hey developer 👋"),H.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",H.createElement("code",{style:T},"ErrorBoundary")," or"," ",H.createElement("code",{style:T},"errorElement")," prop on your route.")),H.createElement(H.Fragment,null,H.createElement("h2",null,"Unexpected Application Error!"),H.createElement("h3",{style:{fontStyle:"italic"}},p),m?H.createElement("pre",{style:S},m):null,f)}const dL=H.createElement(fL,null);class vL extends H.Component{constructor(p){super(p),this.state={location:p.location,revalidation:p.revalidation,error:p.error}}static getDerivedStateFromError(p){return{error:p}}static getDerivedStateFromProps(p,m){return m.location!==p.location||m.revalidation!=="idle"&&p.revalidation==="idle"?{error:p.error,location:p.location,revalidation:p.revalidation}:{error:p.error!==void 0?p.error:m.error,location:m.location,revalidation:p.revalidation||m.revalidation}}componentDidCatch(p,m){console.error("React Router caught the following error during render",p,m)}render(){return this.state.error!==void 0?H.createElement(ra.Provider,{value:this.props.routeContext},H.createElement(Ah.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function pL(c){let{routeContext:p,match:m,children:y}=c,S=H.useContext(Fo);return S&&S.static&&S.staticContext&&(m.route.errorElement||m.route.ErrorBoundary)&&(S.staticContext._deepestRenderedBoundaryId=m.route.id),H.createElement(ra.Provider,{value:p},y)}function hL(c,p,m,y){var S;if(p===void 0&&(p=[]),m===void 0&&(m=null),y===void 0&&(y=null),c==null){var T;if(!m)return null;if(m.errors)c=m.matches;else if((T=y)!=null&&T.v7_partialHydration&&p.length===0&&!m.initialized&&m.matches.length>0)c=m.matches;else return null}let f=c,k=(S=m)==null?void 0:S.errors;if(k!=null){let V=f.findIndex(M=>M.route.id&&(k==null?void 0:k[M.route.id])!==void 0);V>=0||Xe(!1,"Could not find a matching route for errors on route IDs: "+Object.keys(k).join(",")),f=f.slice(0,Math.min(f.length,V+1))}let U=!1,w=-1;if(m&&y&&y.v7_partialHydration)for(let V=0;V<f.length;V++){let M=f[V];if((M.route.HydrateFallback||M.route.hydrateFallbackElement)&&(w=V),M.route.id){let{loaderData:F,errors:B}=m,J=M.route.loader&&F[M.route.id]===void 0&&(!B||B[M.route.id]===void 0);if(M.route.lazy||J){U=!0,w>=0?f=f.slice(0,w+1):f=[f[0]];break}}}return f.reduceRight((V,M,F)=>{let B,J=!1,ye=null,ne=null;m&&(B=k&&M.route.id?k[M.route.id]:void 0,ye=M.route.errorElement||dL,U&&(w<0&&F===0?(sC("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),J=!0,ne=null):w===F&&(J=!0,ne=M.route.hydrateFallbackElement||null)));let we=p.concat(f.slice(0,F+1)),I=()=>{let Z;return B?Z=ye:J?Z=ne:M.route.Component?Z=H.createElement(M.route.Component,null):M.route.element?Z=M.route.element:Z=V,H.createElement(pL,{match:M,routeContext:{outlet:V,matches:we,isDataRoute:m!=null},children:Z})};return m&&(M.route.ErrorBoundary||M.route.errorElement||F===0)?H.createElement(vL,{location:m.location,revalidation:m.revalidation,component:ye,error:B,children:I(),routeContext:{outlet:null,matches:we,isDataRoute:!0}}):I()},null)}var oC=function(c){return c.UseBlocker="useBlocker",c.UseRevalidator="useRevalidator",c.UseNavigateStable="useNavigate",c}(oC||{}),Ho=function(c){return c.UseBlocker="useBlocker",c.UseLoaderData="useLoaderData",c.UseActionData="useActionData",c.UseRouteError="useRouteError",c.UseNavigation="useNavigation",c.UseRouteLoaderData="useRouteLoaderData",c.UseMatches="useMatches",c.UseRevalidator="useRevalidator",c.UseNavigateStable="useNavigate",c.UseRouteId="useRouteId",c}(Ho||{});function kh(c){return c+" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router."}function mL(c){let p=H.useContext(Fo);return p||Xe(!1,kh(c)),p}function yL(c){let p=H.useContext(Uh);return p||Xe(!1,kh(c)),p}function gL(c){let p=H.useContext(ra);return p||Xe(!1,kh(c)),p}function zh(c){let p=gL(c),m=p.matches[p.matches.length-1];return m.route.id||Xe(!1,c+' can only be used on routes that contain a unique "id"'),m.route.id}function bL(){return zh(Ho.UseRouteId)}function SL(){var c;let p=H.useContext(Ah),m=yL(Ho.UseRouteError),y=zh(Ho.UseRouteError);return p!==void 0?p:(c=m.errors)==null?void 0:c[y]}function CL(){let{router:c}=mL(oC.UseNavigateStable),p=zh(Ho.UseNavigateStable),m=H.useRef(!1);return uC(()=>{m.current=!0}),H.useCallback(function(S,T){T===void 0&&(T={}),jn(m.current,lC),m.current&&(typeof S=="number"?c.navigate(S):c.navigate(S,zo({fromRouteId:p},T)))},[c,p])}const IS={};function sC(c,p,m){!p&&!IS[c]&&(IS[c]=!0,jn(!1,m))}function rM(c){let{to:p,replace:m,state:y,relative:S}=c;Fl()||Xe(!1,"<Navigate> may be used only in the context of a <Router> component.");let{future:T,static:f}=H.useContext(Vn);jn(!f,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:k}=H.useContext(ra),{pathname:U}=Br(),w=Nh(),V=Mh(p,Lh(k,T.v7_relativeSplatPath),U,S==="path"),M=JSON.stringify(V);return H.useEffect(()=>w(JSON.parse(M),{replace:m,state:y,relative:S}),[w,M,S,m,y]),null}function EL(c){Xe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function RL(c){let{basename:p="/",children:m=null,location:y,navigationType:S=jr.Pop,navigator:T,static:f=!1,future:k}=c;Fl()&&Xe(!1,"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let U=p.replace(/^\/*/,"/"),w=H.useMemo(()=>({basename:U,navigator:T,static:f,future:zo({v7_relativeSplatPath:!1},k)}),[U,k,T,f]);typeof y=="string"&&(y=Hl(y));let{pathname:V="/",search:M="",hash:F="",state:B=null,key:J="default"}=y,ye=H.useMemo(()=>{let ne=Vr(V,U);return ne==null?null:{location:{pathname:ne,search:M,hash:F,state:B,key:J},navigationType:S}},[U,V,M,F,B,J,S]);return jn(ye!=null,'<Router basename="'+U+'"> is not able to match the URL '+('"'+V+M+F+'" because it does not start with the ')+"basename, so the <Router> won't render anything."),ye==null?null:H.createElement(Vn.Provider,{value:w},H.createElement(jo.Provider,{children:m,value:ye}))}function iM(c){let{children:p,location:m}=c;return sL(Oh(p),m)}new Promise(()=>{});function Oh(c,p){p===void 0&&(p=[]);let m=[];return H.Children.forEach(c,(y,S)=>{if(!H.isValidElement(y))return;let T=[...p,S];if(y.type===H.Fragment){m.push.apply(m,Oh(y.props.children,T));return}y.type!==EL&&Xe(!1,"["+(typeof y.type=="string"?y.type:y.type.name)+"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>"),!y.props.index||!y.props.children||Xe(!1,"An index route cannot have child routes.");let f={id:y.props.id||T.join("-"),caseSensitive:y.props.caseSensitive,element:y.props.element,Component:y.props.Component,index:y.props.index,path:y.props.path,loader:y.props.loader,action:y.props.action,errorElement:y.props.errorElement,ErrorBoundary:y.props.ErrorBoundary,hasErrorBoundary:y.props.ErrorBoundary!=null||y.props.errorElement!=null,shouldRevalidate:y.props.shouldRevalidate,handle:y.props.handle,lazy:y.props.lazy};y.props.children&&(f.children=Oh(y.props.children,T)),m.push(f)}),m}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function zl(){return zl=Object.assign?Object.assign.bind():function(c){for(var p=1;p<arguments.length;p++){var m=arguments[p];for(var y in m)Object.prototype.hasOwnProperty.call(m,y)&&(c[y]=m[y])}return c},zl.apply(this,arguments)}function Hh(c,p){if(c==null)return{};var m={},y=Object.keys(c),S,T;for(T=0;T<y.length;T++)S=y[T],!(p.indexOf(S)>=0)&&(m[S]=c[S]);return m}const lf="get",uf="application/x-www-form-urlencoded";function ff(c){return c!=null&&typeof c.tagName=="string"}function TL(c){return ff(c)&&c.tagName.toLowerCase()==="button"}function xL(c){return ff(c)&&c.tagName.toLowerCase()==="form"}function DL(c){return ff(c)&&c.tagName.toLowerCase()==="input"}function wL(c){return!!(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)}function OL(c,p){return c.button===0&&(!p||p==="_self")&&!wL(c)}function _h(c){return c===void 0&&(c=""),new URLSearchParams(typeof c=="string"||Array.isArray(c)||c instanceof URLSearchParams?c:Object.keys(c).reduce((p,m)=>{let y=c[m];return p.concat(Array.isArray(y)?y.map(S=>[m,S]):[[m,y]])},[]))}function _L(c,p){let m=_h(c);return p&&p.forEach((y,S)=>{m.has(S)||p.getAll(S).forEach(T=>{m.append(S,T)})}),m}let rf=null;function LL(){if(rf===null)try{new FormData(document.createElement("form"),0),rf=!1}catch{rf=!0}return rf}const ML=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Dh(c){return c!=null&&!ML.has(c)?(jn(!1,'"'+c+'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` '+('and will default to "'+uf+'"')),null):c}function UL(c,p){let m,y,S,T,f;if(xL(c)){let k=c.getAttribute("action");y=k?Vr(k,p):null,m=c.getAttribute("method")||lf,S=Dh(c.getAttribute("enctype"))||uf,T=new FormData(c)}else if(TL(c)||DL(c)&&(c.type==="submit"||c.type==="image")){let k=c.form;if(k==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let U=c.getAttribute("formaction")||k.getAttribute("action");if(y=U?Vr(U,p):null,m=c.getAttribute("formmethod")||k.getAttribute("method")||lf,S=Dh(c.getAttribute("formenctype"))||Dh(k.getAttribute("enctype"))||uf,T=new FormData(k,c),!LL()){let{name:w,type:V,value:M}=c;if(V==="image"){let F=w?w+".":"";T.append(F+"x","0"),T.append(F+"y","0")}else w&&T.append(w,M)}}else{if(ff(c))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');m=lf,y=null,S=uf,f=c}return T&&S==="text/plain"&&(f=T,T=void 0),{action:y,method:m.toLowerCase(),encType:S,formData:T,body:f}}const AL=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],NL=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],kL=["fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"],zL="6";try{window.__reactRouterVersion=zL}catch{}const cC=H.createContext({isTransitioning:!1});cC.displayName="ViewTransition";const HL=H.createContext(new Map);HL.displayName="Fetchers";const FL="startTransition",XS=U_[FL];function lM(c){let{basename:p,children:m,future:y,window:S}=c,T=H.useRef();T.current==null&&(T.current=k_({window:S,v5Compat:!0}));let f=T.current,[k,U]=H.useState({action:f.action,location:f.location}),{v7_startTransition:w}=y||{},V=H.useCallback(M=>{w&&XS?XS(()=>U(M)):U(M)},[U,w]);return H.useLayoutEffect(()=>f.listen(V),[f,V]),H.createElement(RL,{basename:p,children:m,location:k.location,navigationType:k.action,navigator:f,future:y})}const jL=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",VL=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,fC=H.forwardRef(function(p,m){let{onClick:y,relative:S,reloadDocument:T,replace:f,state:k,target:U,to:w,preventScrollReset:V,viewTransition:M}=p,F=Hh(p,AL),{basename:B}=H.useContext(Vn),J,ye=!1;if(typeof w=="string"&&VL.test(w)&&(J=w,jL))try{let Z=new URL(window.location.href),ve=w.startsWith("//")?new URL(Z.protocol+w):new URL(w),ie=Vr(ve.pathname,B);ve.origin===Z.origin&&ie!=null?w=ie+ve.search+ve.hash:ye=!0}catch{jn(!1,'<Link to="'+w+'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.')}let ne=uL(w,{relative:S}),we=PL(w,{replace:f,state:k,target:U,preventScrollReset:V,relative:S,viewTransition:M});function I(Z){y&&y(Z),Z.defaultPrevented||we(Z)}return H.createElement("a",zl({},F,{href:J||ne,onClick:ye||T?y:I,ref:m,target:U}))});fC.displayName="Link";const BL=H.forwardRef(function(p,m){let{"aria-current":y="page",caseSensitive:S=!1,className:T="",end:f=!1,style:k,to:U,viewTransition:w,children:V}=p,M=Hh(p,NL),F=Vo(U,{relative:M.relative}),B=Br(),J=H.useContext(Uh),{navigator:ye,basename:ne}=H.useContext(Vn),we=J!=null&&XL(F)&&w===!0,I=ye.encodeLocation?ye.encodeLocation(F).pathname:F.pathname,Z=B.pathname,ve=J&&J.navigation&&J.navigation.location?J.navigation.location.pathname:null;S||(Z=Z.toLowerCase(),ve=ve?ve.toLowerCase():null,I=I.toLowerCase()),ve&&ne&&(ve=Vr(ve,ne)||ve);const ie=I!=="/"&&I.endsWith("/")?I.length-1:I.length;let Be=Z===I||!f&&Z.startsWith(I)&&Z.charAt(ie)==="/",Ce=ve!=null&&(ve===I||!f&&ve.startsWith(I)&&ve.charAt(I.length)==="/"),Ft={isActive:Be,isPending:Ce,isTransitioning:we},vn=Be?y:void 0,et;typeof T=="function"?et=T(Ft):et=[T,Be?"active":null,Ce?"pending":null,we?"transitioning":null].filter(Boolean).join(" ");let qe=typeof k=="function"?k(Ft):k;return H.createElement(fC,zl({},M,{"aria-current":vn,className:et,ref:m,style:qe,to:U,viewTransition:w}),typeof V=="function"?V(Ft):V)});BL.displayName="NavLink";const $L=H.forwardRef((c,p)=>{let{fetcherKey:m,navigate:y,reloadDocument:S,replace:T,state:f,method:k=lf,action:U,onSubmit:w,relative:V,preventScrollReset:M,viewTransition:F}=c,B=Hh(c,kL),J=WL(),ye=IL(U,{relative:V}),ne=k.toLowerCase()==="get"?"get":"post",we=I=>{if(w&&w(I),I.defaultPrevented)return;I.preventDefault();let Z=I.nativeEvent.submitter,ve=(Z==null?void 0:Z.getAttribute("formmethod"))||k;J(Z||I.currentTarget,{fetcherKey:m,method:ve,navigate:y,replace:T,state:f,relative:V,preventScrollReset:M,viewTransition:F})};return H.createElement("form",zl({ref:p,method:ne,action:ye,onSubmit:S?w:we},B))});$L.displayName="Form";var cf;(function(c){c.UseScrollRestoration="useScrollRestoration",c.UseSubmit="useSubmit",c.UseSubmitFetcher="useSubmitFetcher",c.UseFetcher="useFetcher",c.useViewTransitionState="useViewTransitionState"})(cf||(cf={}));var KS;(function(c){c.UseFetcher="useFetcher",c.UseFetchers="useFetchers",c.UseScrollRestoration="useScrollRestoration"})(KS||(KS={}));function YL(c){return c+" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router."}function dC(c){let p=H.useContext(Fo);return p||Xe(!1,YL(c)),p}function PL(c,p){let{target:m,replace:y,state:S,preventScrollReset:T,relative:f,viewTransition:k}=p===void 0?{}:p,U=Nh(),w=Br(),V=Vo(c,{relative:f});return H.useCallback(M=>{if(OL(M,m)){M.preventDefault();let F=y!==void 0?y:ko(w)===ko(V);U(c,{replace:F,state:S,preventScrollReset:T,relative:f,viewTransition:k})}},[w,U,V,y,S,m,c,T,f,k])}function uM(c){jn(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let p=H.useRef(_h(c)),m=H.useRef(!1),y=Br(),S=H.useMemo(()=>_L(y.search,m.current?null:p.current),[y.search]),T=Nh(),f=H.useCallback((k,U)=>{const w=_h(typeof k=="function"?k(S):k);m.current=!0,T("?"+w,U)},[T,S]);return[S,f]}function qL(){if(typeof document>"u")throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}let GL=0,QL=()=>"__"+String(++GL)+"__";function WL(){let{router:c}=dC(cf.UseSubmit),{basename:p}=H.useContext(Vn),m=bL();return H.useCallback(function(y,S){S===void 0&&(S={}),qL();let{action:T,method:f,encType:k,formData:U,body:w}=UL(y,p);if(S.navigate===!1){let V=S.fetcherKey||QL();c.fetch(V,m,S.action||T,{preventScrollReset:S.preventScrollReset,formData:U,body:w,formMethod:S.method||f,formEncType:S.encType||k,flushSync:S.flushSync})}else c.navigate(S.action||T,{preventScrollReset:S.preventScrollReset,formData:U,body:w,formMethod:S.method||f,formEncType:S.encType||k,replace:S.replace,state:S.state,fromRouteId:m,flushSync:S.flushSync,viewTransition:S.viewTransition})},[c,p,m])}function IL(c,p){let{relative:m}=p===void 0?{}:p,{basename:y}=H.useContext(Vn),S=H.useContext(ra);S||Xe(!1,"useFormAction must be used inside a RouteContext");let[T]=S.matches.slice(-1),f=zl({},Vo(c||".",{relative:m})),k=Br();if(c==null){f.search=k.search;let U=new URLSearchParams(f.search),w=U.getAll("index");if(w.some(M=>M==="")){U.delete("index"),w.filter(F=>F).forEach(F=>U.append("index",F));let M=U.toString();f.search=M?"?"+M:""}}return(!c||c===".")&&T.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),y!=="/"&&(f.pathname=f.pathname==="/"?y:tr([y,f.pathname])),ko(f)}function XL(c,p){p===void 0&&(p={});let m=H.useContext(cC);m==null&&Xe(!1,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:y}=dC(cf.useViewTransitionState),S=Vo(c,{relative:p.relative});if(!m.isTransitioning)return!1;let T=Vr(m.currentLocation.pathname,y)||m.currentLocation.pathname,f=Vr(m.nextLocation.pathname,y)||m.nextLocation.pathname;return sf(S.pathname,f)!=null||sf(S.pathname,T)!=null}export{lM as B,fC as L,rM as N,M_ as R,N_ as a,nM as b,U_ as c,tM as d,Nh as e,iM as f,JS as g,EL as h,uM as i,aM as j,H as r,Br as u};

import{j as e,L as r,B as l,C as u,d as f,e as N,a as x,f as E,h as V,t as w}from"./index-D3FXQGgv.js";import{j as y,i as L,e as k,r as o,L as S}from"./react-vendor-DucYQ77b.js";import{B as n}from"./badge-BHFrTODj.js";import{A as B,a as A}from"./avatar-wZOQCpPA.js";import{ad as F,ag as I,_ as R,Z as U,a3 as $,an as z,ao as H,af as T}from"./utils-CUkY6B8V.js";import"./radix-ui-DdAvwPj2.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const Q=()=>{const{hash:m}=y(),[p]=L(),C=k(),[s,b]=o.useState(null),[j,c]=o.useState(!0),g=async()=>{if(m)try{c(!0);const t=await V.getCommit(m);b(t)}catch(t){console.error("Failed to load commit:",t),w({title:"加载失败",description:"无法加载提交详情",variant:"destructive"})}finally{c(!1)}};o.useEffect(()=>{g()},[m]);const d=()=>{const t=p.get("from");t?C(decodeURIComponent(t)):window.history.back()},v=t=>new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),h=t=>{switch(t){case"added":return e.jsxDEV(T,{className:"w-4 h-4 text-green-600"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:69,columnNumber:16},void 0);case"removed":return e.jsxDEV(H,{className:"w-4 h-4 text-red-600"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:71,columnNumber:16},void 0);case"modified":return e.jsxDEV(z,{className:"w-4 h-4 text-yellow-600"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:73,columnNumber:16},void 0);default:return e.jsxDEV($,{className:"w-4 h-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:75,columnNumber:16},void 0)}},D=t=>{switch(t){case"added":return e.jsxDEV(n,{variant:"added",children:"新增"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:82,columnNumber:16},void 0);case"removed":return e.jsxDEV(n,{variant:"removed",children:"删除"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:84,columnNumber:16},void 0);case"modified":return e.jsxDEV(n,{variant:"modified",children:"修改"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:86,columnNumber:16},void 0);default:return e.jsxDEV(n,{variant:"default",children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:88,columnNumber:16},void 0)}},P=t=>{var a;switch((a=t.split(".").pop())==null?void 0:a.toLowerCase()){case"ts":case"tsx":return"text-blue-600";case"js":case"jsx":return"text-yellow-600";case"json":return"text-green-600";case"md":return"text-purple-600";default:return"text-muted-foreground"}};return j?e.jsxDEV(r,{children:e.jsxDEV("div",{className:"flex items-center justify-center h-64",children:e.jsxDEV("div",{className:"w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:114,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:113,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:112,columnNumber:7},void 0):s?e.jsxDEV(r,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV(l,{variant:"outline",size:"sm",onClick:d,className:"flex items-center gap-2",children:[e.jsxDEV(F,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:145,columnNumber:13},void 0),"返回"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:139,columnNumber:11},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-foreground",children:"提交详情"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:150,columnNumber:13},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-sm text-muted-foreground mt-1",children:[e.jsxDEV(I,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:152,columnNumber:15},void 0),e.jsxDEV("code",{className:"text-xs",children:s.hash},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:153,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:151,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:149,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:138,columnNumber:9},void 0),e.jsxDEV(u,{children:[e.jsxDEV(f,{children:e.jsxDEV("div",{className:"flex items-start gap-4",children:[e.jsxDEV(B,{className:"w-12 h-12",children:e.jsxDEV(A,{children:s.author.name.split(" ").map(t=>t[0]).join("")},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:163,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:162,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV(N,{className:"text-lg mb-2",children:s.message},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:169,columnNumber:17},void 0),e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center gap-4 text-sm text-muted-foreground",children:[e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV(R,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:175,columnNumber:21},void 0),e.jsxDEV("span",{className:"font-medium",children:s.author.name},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:176,columnNumber:21},void 0),s.author.username&&e.jsxDEV("span",{children:["(",s.author.username,")"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:177,columnNumber:48},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:174,columnNumber:19},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV(U,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:181,columnNumber:21},void 0),v(s.date)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:180,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:173,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:168,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:161,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:160,columnNumber:11},void 0),e.jsxDEV(x,{children:e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:s.stats.filesChanged},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:192,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"文件变更"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:193,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:191,columnNumber:15},void 0),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("p",{className:"text-2xl font-bold text-green-600",children:["+",s.stats.additions]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:196,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"新增行数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:197,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:195,columnNumber:15},void 0),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("p",{className:"text-2xl font-bold text-red-600",children:["-",s.stats.deletions]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:200,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"删除行数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:201,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:199,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:190,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:189,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:159,columnNumber:9},void 0),e.jsxDEV(u,{children:[e.jsxDEV(f,{children:[e.jsxDEV(N,{className:"text-lg",children:"文件变更列表"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:210,columnNumber:13},void 0),e.jsxDEV(E,{children:["此提交共影响 ",s.files.length," 个文件"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:211,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:209,columnNumber:11},void 0),e.jsxDEV(x,{children:e.jsxDEV("div",{className:"space-y-3",children:s.files.map((t,i)=>e.jsxDEV(S,{to:`/commit/${m}/file/${encodeURIComponent(t.filename)}?from=${encodeURIComponent(window.location.pathname+window.location.search)}`,className:"flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors group cursor-pointer",children:e.jsxDEV("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[h(t.status),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("p",{className:`font-mono text-sm font-medium truncate ${P(t.filename)}`,children:t.filename},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:228,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex items-center gap-3 text-xs text-muted-foreground mt-1",children:[e.jsxDEV("span",{children:["+",t.additions," -",t.deletions]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:232,columnNumber:25},void 0),e.jsxDEV("span",{children:[t.changes," 变更"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:233,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:231,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:227,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:D(t.status)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:237,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:224,columnNumber:19},void 0)},i,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:219,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:217,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:216,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:208,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:136,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:135,columnNumber:5},void 0):e.jsxDEV(r,{children:e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("h2",{className:"text-2xl font-bold text-foreground mb-2",children:"提交不存在"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:124,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-muted-foreground mb-4",children:["无法找到提交 ",m]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:125,columnNumber:11},void 0),e.jsxDEV(l,{onClick:d,children:"返回"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:126,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:123,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Commit.tsx",lineNumber:122,columnNumber:7},void 0)};export{Q as default};

var St=e=>{throw TypeError(e)};var vt=(e,t,s)=>t.has(e)||St("Cannot "+s);var i=(e,t,s)=>(vt(e,t,"read from private field"),s?s.call(e):t.get(e)),l=(e,t,s)=>t.has(e)?St("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),o=(e,t,s,r)=>(vt(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),P=(e,t,s)=>(vt(e,t,"access private method"),s);var lt=(e,t,s,r)=>({set _(n){o(e,t,n,s)},get _(){return i(e,t,r)}});import{r as It}from"./react-vendor-DucYQ77b.js";import{j as zt}from"./radix-ui-DdAvwPj2.js";var pt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},mt=typeof window>"u"||"Deno"in globalThis;function R(){}function Bt(e,t){return typeof e=="function"?e(t):e}function _t(e){return typeof e=="number"&&e>=0&&e!==1/0}function Jt(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ft(e,t){return typeof e=="function"?e(t):e}function Vt(e,t){return typeof e=="function"?e(t):e}function Ot(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:u,queryKey:h,stale:a}=e;if(h){if(r){if(t.queryHash!==Pt(h,t.options))return!1}else if(!ut(t.queryKey,h))return!1}if(s!=="all"){const f=t.isActive();if(s==="active"&&!f||s==="inactive"&&f)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||n&&n!==t.state.fetchStatus||u&&!u(t))}function Ct(e,t){const{exact:s,status:r,predicate:n,mutationKey:u}=e;if(u){if(!t.options.mutationKey)return!1;if(s){if(at(t.options.mutationKey)!==at(u))return!1}else if(!ut(t.options.mutationKey,u))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function Pt(e,t){return((t==null?void 0:t.queryKeyHashFn)||at)(e)}function at(e){return JSON.stringify(e,(t,s)=>wt(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function ut(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(s=>!ut(e[s],t[s])):!1}function gt(e,t){if(e===t)return e;const s=qt(e)&&qt(t);if(s||wt(e)&&wt(t)){const r=s?e:Object.keys(e),n=r.length,u=s?t:Object.keys(t),h=u.length,a=s?[]:{};let f=0;for(let w=0;w<h;w++){const m=s?w:u[w];(!s&&r.includes(m)||s)&&e[m]===void 0&&t[m]===void 0?(a[m]=void 0,f++):(a[m]=gt(e[m],t[m]),a[m]===e[m]&&e[m]!==void 0&&f++)}return n===h&&f===n?e:a}return t}function qt(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function wt(e){if(!At(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!At(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function At(e){return Object.prototype.toString.call(e)==="[object Object]"}function Wt(e){return new Promise(t=>{setTimeout(t,e)})}function Xt(e,t,s){if(typeof s.structuralSharing=="function")return s.structuralSharing(e,t);if(s.structuralSharing!==!1){try{return gt(e,t)}catch(r){console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${s.queryHash}]: ${r}`)}return gt(e,t)}return t}function Yt(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function Zt(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var ft=Symbol();function xt(e,t){return e.queryFn===ft&&console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${e.queryHash}'`),!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===ft?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var _,L,X,Et,te=(Et=class extends pt{constructor(){super();l(this,_);l(this,L);l(this,X);o(this,X,t=>{if(!mt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,L)||this.setEventListener(i(this,X))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,L))==null||t.call(this),o(this,L,void 0))}setEventListener(t){var s;o(this,X,t),(s=i(this,L))==null||s.call(this),o(this,L,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,_)!==t&&(o(this,_,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof i(this,_)=="boolean"?i(this,_):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},_=new WeakMap,L=new WeakMap,X=new WeakMap,Et),Ht=new te,Y,G,Z,Mt,ee=(Mt=class extends pt{constructor(){super();l(this,Y,!0);l(this,G);l(this,Z);o(this,Z,t=>{if(!mt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,G)||this.setEventListener(i(this,Z))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,G))==null||t.call(this),o(this,G,void 0))}setEventListener(t){var s;o(this,Z,t),(s=i(this,G))==null||s.call(this),o(this,G,t(this.setOnline.bind(this)))}setOnline(t){i(this,Y)!==t&&(o(this,Y,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,Y)}},Y=new WeakMap,G=new WeakMap,Z=new WeakMap,Mt),yt=new ee;function se(){let e,t;const s=new Promise((n,u)=>{e=n,t=u});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},s.reject=n=>{r({status:"rejected",reason:n}),t(n)},s}function re(e){return Math.min(1e3*2**e,3e4)}function Lt(e){return(e??"online")==="online"?yt.isOnline():!0}var Gt=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function bt(e){return e instanceof Gt}function Nt(e){let t=!1,s=0,r=!1,n;const u=se(),h=c=>{var p;r||(y(new Gt(c)),(p=e.abort)==null||p.call(e))},a=()=>{t=!0},f=()=>{t=!1},w=()=>Ht.isFocused()&&(e.networkMode==="always"||yt.isOnline())&&e.canRun(),m=()=>Lt(e.networkMode)&&e.canRun(),d=c=>{var p;r||(r=!0,(p=e.onSuccess)==null||p.call(e,c),n==null||n(),u.resolve(c))},y=c=>{var p;r||(r=!0,(p=e.onError)==null||p.call(e,c),n==null||n(),u.reject(c))},q=()=>new Promise(c=>{var p;n=A=>{(r||w())&&c(A)},(p=e.onPause)==null||p.call(e)}).then(()=>{var c;n=void 0,r||(c=e.onContinue)==null||c.call(e)}),F=()=>{if(r)return;let c;const p=s===0?e.initialPromise:void 0;try{c=p??e.fn()}catch(A){c=Promise.reject(A)}Promise.resolve(c).then(d).catch(A=>{var K;if(r)return;const M=e.retry??(mt?0:3),b=e.retryDelay??re,D=typeof b=="function"?b(s,A):b,j=M===!0||typeof M=="number"&&s<M||typeof M=="function"&&M(s,A);if(t||!j){y(A);return}s++,(K=e.onFail)==null||K.call(e,s,A),Wt(D).then(()=>w()?void 0:q()).then(()=>{t?y(A):F()})})};return{promise:u,cancel:h,continue:()=>(n==null||n(),u),cancelRetry:a,continueRetry:f,canStart:m,start:()=>(m()?F():q().then(F),u)}}function ie(){let e=[],t=0,s=a=>{a()},r=a=>{a()},n=a=>setTimeout(a,0);const u=a=>{t?e.push(a):n(()=>{s(a)})},h=()=>{const a=e;e=[],a.length&&n(()=>{r(()=>{a.forEach(f=>{s(f)})})})};return{batch:a=>{let f;t++;try{f=a()}finally{t--,t||h()}return f},batchCalls:a=>(...f)=>{u(()=>{a(...f)})},schedule:u,setNotifyFunction:a=>{s=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{n=a}}}var C=ie(),J,Rt,$t=(Rt=class{constructor(){l(this,J)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),_t(this.gcTime)&&o(this,J,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(mt?1/0:5*60*1e3))}clearGcTimeout(){i(this,J)&&(clearTimeout(i(this,J)),o(this,J,void 0))}},J=new WeakMap,Rt),tt,et,E,S,ot,V,T,x,Tt,ne=(Tt=class extends $t{constructor(t){super();l(this,T);l(this,tt);l(this,et);l(this,E);l(this,S);l(this,ot);l(this,V);o(this,V,!1),o(this,ot,t.defaultOptions),this.setOptions(t.options),this.observers=[],o(this,E,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,o(this,tt,ue(this.options)),this.state=t.state??i(this,tt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=i(this,S))==null?void 0:t.promise}setOptions(t){this.options={...i(this,ot),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,E).remove(this)}setData(t,s){const r=Xt(this.state.data,t,this.options);return P(this,T,x).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){P(this,T,x).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,n;const s=(r=i(this,S))==null?void 0:r.promise;return(n=i(this,S))==null||n.cancel(t),s?s.then(R).catch(R):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,tt))}isActive(){return this.observers.some(t=>Vt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ft||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Jt(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,S))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,S))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,E).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,S)&&(i(this,V)?i(this,S).cancel({revert:!0}):i(this,S).cancelRetry()),this.scheduleGc()),i(this,E).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||P(this,T,x).call(this,{type:"invalidate"})}fetch(t,s){var f,w,m;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(i(this,S))return i(this,S).continueRetry(),i(this,S).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(y=>y.options.queryFn);d&&this.setOptions(d.options)}Array.isArray(this.options.queryKey)||console.error("As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']");const r=new AbortController,n=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(o(this,V,!0),r.signal)})},u=()=>{const d=xt(this.options,s),y={queryKey:this.queryKey,meta:this.meta};return n(y),o(this,V,!1),this.options.persister?this.options.persister(d,y,this):d(y)},h={fetchOptions:s,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:u};n(h),(f=this.options.behavior)==null||f.onFetch(h,this),o(this,et,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((w=h.fetchOptions)==null?void 0:w.meta))&&P(this,T,x).call(this,{type:"fetch",meta:(m=h.fetchOptions)==null?void 0:m.meta});const a=d=>{var y,q,F,c;bt(d)&&d.silent||P(this,T,x).call(this,{type:"error",error:d}),bt(d)||((q=(y=i(this,E).config).onError)==null||q.call(y,d,this),(c=(F=i(this,E).config).onSettled)==null||c.call(F,this.state.data,d,this)),this.scheduleGc()};return o(this,S,Nt({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var y,q,F,c;if(d===void 0){console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`),a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(p){a(p);return}(q=(y=i(this,E).config).onSuccess)==null||q.call(y,d,this),(c=(F=i(this,E).config).onSettled)==null||c.call(F,d,this.state.error,this),this.scheduleGc()},onError:a,onFail:(d,y)=>{P(this,T,x).call(this,{type:"failed",failureCount:d,error:y})},onPause:()=>{P(this,T,x).call(this,{type:"pause"})},onContinue:()=>{P(this,T,x).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),i(this,S).start()}},tt=new WeakMap,et=new WeakMap,E=new WeakMap,S=new WeakMap,ot=new WeakMap,V=new WeakMap,T=new WeakSet,x=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ae(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return bt(n)&&n.revert&&i(this,et)?{...i(this,et),fetchStatus:"idle"}:{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),C.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,E).notify({query:this,type:"updated",action:t})})},Tt);function ae(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Lt(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ue(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var U,jt,oe=(jt=class extends pt{constructor(t={}){super();l(this,U);this.config=t,o(this,U,new Map)}build(t,s,r){const n=s.queryKey,u=s.queryHash??Pt(n,s);let h=this.get(u);return h||(h=new ne({cache:this,queryKey:n,queryHash:u,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(h)),h}add(t){i(this,U).has(t.queryHash)||(i(this,U).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,U).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,U).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){C.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,U).get(t)}getAll(){return[...i(this,U).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Ot(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>Ot(t,r)):s}notify(t){C.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){C.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){C.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},U=new WeakMap,jt),k,O,W,I,H,Kt,he=(Kt=class extends $t{constructor(t){super();l(this,I);l(this,k);l(this,O);l(this,W);this.mutationId=t.mutationId,o(this,O,t.mutationCache),o(this,k,[]),this.state=t.state||ce(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,k).includes(t)||(i(this,k).push(t),this.clearGcTimeout(),i(this,O).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){o(this,k,i(this,k).filter(s=>s!==t)),this.scheduleGc(),i(this,O).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,k).length||(this.state.status==="pending"?this.scheduleGc():i(this,O).remove(this))}continue(){var t;return((t=i(this,W))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var n,u,h,a,f,w,m,d,y,q,F,c,p,A,M,b,D,j,K,ct;o(this,W,Nt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(g,B)=>{P(this,I,H).call(this,{type:"failed",failureCount:g,error:B})},onPause:()=>{P(this,I,H).call(this,{type:"pause"})},onContinue:()=>{P(this,I,H).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,O).canRun(this)}));const s=this.state.status==="pending",r=!i(this,W).canStart();try{if(!s){P(this,I,H).call(this,{type:"pending",variables:t,isPaused:r}),await((u=(n=i(this,O).config).onMutate)==null?void 0:u.call(n,t,this));const B=await((a=(h=this.options).onMutate)==null?void 0:a.call(h,t));B!==this.state.context&&P(this,I,H).call(this,{type:"pending",context:B,variables:t,isPaused:r})}const g=await i(this,W).start();return await((w=(f=i(this,O).config).onSuccess)==null?void 0:w.call(f,g,t,this.state.context,this)),await((d=(m=this.options).onSuccess)==null?void 0:d.call(m,g,t,this.state.context)),await((q=(y=i(this,O).config).onSettled)==null?void 0:q.call(y,g,null,this.state.variables,this.state.context,this)),await((c=(F=this.options).onSettled)==null?void 0:c.call(F,g,null,t,this.state.context)),P(this,I,H).call(this,{type:"success",data:g}),g}catch(g){try{throw await((A=(p=i(this,O).config).onError)==null?void 0:A.call(p,g,t,this.state.context,this)),await((b=(M=this.options).onError)==null?void 0:b.call(M,g,t,this.state.context)),await((j=(D=i(this,O).config).onSettled)==null?void 0:j.call(D,void 0,g,this.state.variables,this.state.context,this)),await((ct=(K=this.options).onSettled)==null?void 0:ct.call(K,void 0,g,t,this.state.context)),g}finally{P(this,I,H).call(this,{type:"error",error:g})}}finally{i(this,O).runNext(this)}}},k=new WeakMap,O=new WeakMap,W=new WeakMap,I=new WeakSet,H=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),C.batch(()=>{i(this,k).forEach(r=>{r.onMutationUpdate(t)}),i(this,O).notify({mutation:this,type:"updated",action:t})})},Kt);function ce(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Q,ht,Ut,le=(Ut=class extends pt{constructor(t={}){super();l(this,Q);l(this,ht);this.config=t,o(this,Q,new Map),o(this,ht,Date.now())}build(t,s,r){const n=new he({mutationCache:this,mutationId:++lt(this,ht)._,options:t.defaultMutationOptions(s),state:r});return this.add(n),n}add(t){const s=dt(t),r=i(this,Q).get(s)??[];r.push(t),i(this,Q).set(s,r),this.notify({type:"added",mutation:t})}remove(t){var r;const s=dt(t);if(i(this,Q).has(s)){const n=(r=i(this,Q).get(s))==null?void 0:r.filter(u=>u!==t);n&&(n.length===0?i(this,Q).delete(s):i(this,Q).set(s,n))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const s=(r=i(this,Q).get(dt(t)))==null?void 0:r.find(n=>n.state.status==="pending");return!s||s===t}runNext(t){var r;const s=(r=i(this,Q).get(dt(t)))==null?void 0:r.find(n=>n!==t&&n.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}clear(){C.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...i(this,Q).values()].flat()}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Ct(s,r))}findAll(t={}){return this.getAll().filter(s=>Ct(t,s))}notify(t){C.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return C.batch(()=>Promise.all(t.map(s=>s.continue().catch(R))))}},Q=new WeakMap,ht=new WeakMap,Ut);function dt(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Dt(e){return{onFetch:(t,s)=>{var m,d,y,q,F;const r=t.options,n=(y=(d=(m=t.fetchOptions)==null?void 0:m.meta)==null?void 0:d.fetchMore)==null?void 0:y.direction,u=((q=t.state.data)==null?void 0:q.pages)||[],h=((F=t.state.data)==null?void 0:F.pageParams)||[];let a={pages:[],pageParams:[]},f=0;const w=async()=>{let c=!1;const p=b=>{Object.defineProperty(b,"signal",{enumerable:!0,get:()=>(t.signal.aborted?c=!0:t.signal.addEventListener("abort",()=>{c=!0}),t.signal)})},A=xt(t.options,t.fetchOptions),M=async(b,D,j)=>{if(c)return Promise.reject();if(D==null&&b.pages.length)return Promise.resolve(b);const K={queryKey:t.queryKey,pageParam:D,direction:j?"backward":"forward",meta:t.options.meta};p(K);const ct=await A(K),{maxPages:g}=t.options,B=j?Zt:Yt;return{pages:B(b.pages,ct,g),pageParams:B(b.pageParams,D,g)}};if(n&&u.length){const b=n==="backward",D=b?de:Qt,j={pages:u,pageParams:h},K=D(r,j);a=await M(j,K,b)}else{const b=e??u.length;do{const D=f===0?h[0]??r.initialPageParam:Qt(r,a);if(f>0&&D==null)break;a=await M(a,D),f++}while(f<b)}return a};t.options.persister?t.fetchFn=()=>{var c,p;return(p=(c=t.options).persister)==null?void 0:p.call(c,w,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=w}}}function Qt(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function de(e,{pages:t,pageParams:s}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,s[0],s):void 0}var v,N,$,st,rt,z,it,nt,kt,ve=(kt=class{constructor(e={}){l(this,v);l(this,N);l(this,$);l(this,st);l(this,rt);l(this,z);l(this,it);l(this,nt);o(this,v,e.queryCache||new oe),o(this,N,e.mutationCache||new le),o(this,$,e.defaultOptions||{}),o(this,st,new Map),o(this,rt,new Map),o(this,z,0)}mount(){lt(this,z)._++,i(this,z)===1&&(o(this,it,Ht.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,v).onFocus())})),o(this,nt,yt.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,v).onOnline())})))}unmount(){var e,t;lt(this,z)._--,i(this,z)===0&&((e=i(this,it))==null||e.call(this),o(this,it,void 0),(t=i(this,nt))==null||t.call(this),o(this,nt,void 0))}isFetching(e){return i(this,v).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,N).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,v).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const s=this.defaultQueryOptions(e),r=i(this,v).build(this,s);return e.revalidateIfStale&&r.isStaleByTime(Ft(s.staleTime,r))&&this.prefetchQuery(s),Promise.resolve(t)}}getQueriesData(e){return i(this,v).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),n=i(this,v).get(r.queryHash),u=n==null?void 0:n.state.data,h=Bt(t,u);if(h!==void 0)return i(this,v).build(this,r).setData(h,{...s,manual:!0})}setQueriesData(e,t,s){return C.batch(()=>i(this,v).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,v).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=i(this,v);C.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,v),r={type:"active",...e};return C.batch(()=>(s.findAll(e).forEach(n=>{n.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const s={revert:!0,...t},r=C.batch(()=>i(this,v).findAll(e).map(n=>n.cancel(s)));return Promise.all(r).then(R).catch(R)}invalidateQueries(e={},t={}){return C.batch(()=>{if(i(this,v).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const s={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(s,t)})}refetchQueries(e={},t){const s={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=C.batch(()=>i(this,v).findAll(e).filter(n=>!n.isDisabled()).map(n=>{let u=n.fetch(void 0,s);return s.throwOnError||(u=u.catch(R)),n.state.fetchStatus==="paused"?Promise.resolve():u}));return Promise.all(r).then(R)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,v).build(this,t);return s.isStaleByTime(Ft(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(R).catch(R)}fetchInfiniteQuery(e){return e.behavior=Dt(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(R).catch(R)}ensureInfiniteQueryData(e){return e.behavior=Dt(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return yt.isOnline()?i(this,N).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,v)}getMutationCache(){return i(this,N)}getDefaultOptions(){return i(this,$)}setDefaultOptions(e){o(this,$,e)}setQueryDefaults(e,t){i(this,st).set(at(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,st).values()];let s={};return t.forEach(r=>{ut(e,r.queryKey)&&(s={...s,...r.defaultOptions})}),s}setMutationDefaults(e,t){i(this,rt).set(at(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,rt).values()];let s={};return t.forEach(r=>{ut(e,r.mutationKey)&&(s={...s,...r.defaultOptions})}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,$).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Pt(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===ft&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...i(this,$).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,v).clear(),i(this,N).clear()}},v=new WeakMap,N=new WeakMap,$=new WeakMap,st=new WeakMap,rt=new WeakMap,z=new WeakMap,it=new WeakMap,nt=new WeakMap,kt),fe=It.createContext(void 0),be=({client:e,children:t})=>(It.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),zt.jsx(fe.Provider,{value:e,children:t}));export{ve as Q,be as a};

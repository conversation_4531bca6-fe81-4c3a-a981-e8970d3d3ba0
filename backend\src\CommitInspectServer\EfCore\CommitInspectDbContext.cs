using Microsoft.EntityFrameworkCore;
using CommitInspectServer.Entities;

namespace CommitInspectServer.EfCore;

public class CommitInspectDbContext : DbContext
{
    public CommitInspectDbContext(DbContextOptions<CommitInspectDbContext> options)
        : base(options)
    {
    }

    public DbSet<Repository> Repositories { get; set; }
    public DbSet<Branch> Branches { get; set; }
    public DbSet<Commit> Commits { get; set; }
    public DbSet<CommitParent> CommitParents { get; set; }
    public DbSet<Tag> Tags { get; set; }
    public DbSet<CommitFile> CommitFiles { get; set; }
    public DbSet<ScanLog> ScanLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CommitParent composite key and relationships
        modelBuilder.Entity<CommitParent>()
            .Has<PERSON>ey(cp => new { cp.ParentCommitId, cp.ChildCommitId });

        modelBuilder.Entity<CommitParent>()
            .HasOne(cp => cp.ParentCommit)
            .WithMany(c => c.Children)
            .HasForeignKey(cp => cp.ParentCommitId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<CommitParent>()
            .HasOne(cp => cp.ChildCommit)
            .WithMany(c => c.Parents)
            .HasForeignKey(cp => cp.ChildCommitId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure Branch-Commit relationship (optional head commit)
        modelBuilder.Entity<Branch>()
            .HasOne(b => b.HeadCommit)
            .WithMany()
            .HasForeignKey(b => b.HeadCommitId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure unique index on Commit Hash
        modelBuilder.Entity<Commit>()
            .HasIndex(c => new { c.Hash, c.RepositoryId })
            .IsUnique();

        // Configure unique index on Branch Name per Repository
        modelBuilder.Entity<Branch>()
            .HasIndex(b => new { b.Name, b.RepositoryId })
            .IsUnique();

        // Configure unique index on Tag Name per Repository
        modelBuilder.Entity<Tag>()
            .HasIndex(t => new { t.Name, t.RepositoryId })
            .IsUnique();
    }
}

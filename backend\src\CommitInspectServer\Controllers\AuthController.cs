using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CommitInspectServer.DTOs;
using CommitInspectServer.Services;

namespace CommitInspectServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly ITokenService _tokenService;

    public AuthController(IConfiguration configuration, ITokenService tokenService)
    {
        _configuration = configuration;
        _tokenService = tokenService;
    }

    [HttpPost("login")]
    public IActionResult Login([FromBody] LoginRequest request)
    {
        var adminPassword = _configuration["Auth:AdminPassword"];
        
        if (string.IsNullOrEmpty(adminPassword))
        {
            return StatusCode(500, new LoginResponse(false, Message: "服务器配置错误"));
        }

        if (request.Password == adminPassword)
        {
            var token = _tokenService.GenerateToken();
            return Ok(new LoginResponse(true, token, "登录成功"));
        }
        
        return BadRequest(new LoginResponse(false, Message: "密码错误"));
    }
}
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CommitInspectServer.EfCore;
using Microsoft.EntityFrameworkCore;

namespace CommitInspectServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CommitController : ControllerBase
{
    private readonly CommitInspectDbContext _context;

    public CommitController(CommitInspectDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetCommits([FromQuery] Guid? repositoryId, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            var query = _context.Commits.AsQueryable();

            if (repositoryId.HasValue)
            {
                query = query.Where(c => c.RepositoryId == repositoryId.Value);
            }

            var totalCount = await query.CountAsync();
            
            var commits = await query
                .OrderByDescending(c => c.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(c => new
                {
                    c.Hash,
                    c.Message,
                    c.Author,
                    c.AuthorEmail,
                    c.CreatedAt,
                    c.RepositoryId
                })
                .ToListAsync();

            return Ok(new
            {
                commits,
                totalCount,
                page,
                pageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取提交列表失败", error = ex.Message });
        }
    }

    [HttpGet("{hash}")]
    public async Task<IActionResult> GetCommit(string hash)
    {
        try
        {
            var commit = await _context.Commits
                .Include(c => c.Files)
                .Include(c => c.Repository)
                .Where(c => c.Hash == hash)
                .FirstOrDefaultAsync();

            if (commit == null)
            {
                return NotFound(new { success = false, message = "提交不存在" });
            }

            var result = new
            {
                commit.Hash,
                commit.Message,
                Author = new
                {
                    Name = commit.Author,
                    Email = commit.AuthorEmail,
                    Username = commit.Author.Replace(" ", "").ToLower() // 简单生成用户名
                },
                Date = commit.CreatedAt,
                Stats = new
                {
                    FilesChanged = commit.Files.Count,
                    Additions = commit.Files.Sum(f => f.Additions),
                    Deletions = commit.Files.Sum(f => f.Deletions)
                },
                Repository = new
                {
                    commit.Repository.Owner,
                    commit.Repository.Name
                },
                Files = commit.Files.Select(f => new
                {
                    f.Filename,
                    Status = f.Status.ToString().ToLower(),
                    f.Additions,
                    f.Deletions,
                    Changes = f.Additions + f.Deletions,
                    f.Patch
                }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取提交详情失败", error = ex.Message });
        }
    }
}
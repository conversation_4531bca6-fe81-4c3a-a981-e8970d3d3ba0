using System.ComponentModel.DataAnnotations;

namespace CommitInspectServer.Entities;

public class ScanLog
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public Guid RepositoryId { get; set; }

    [Required]
    public DateTime StartTime { get; set; }

    [Required]
    public DateTime EndTime { get; set; }

    [Required]
    public ScanStatus Status { get; set; }

    [Required]
    public string Message { get; set; } = string.Empty;

    public int NewCommitsCount { get; set; } = 0;

    public int UpdatedCommitsCount { get; set; } = 0;

    public string? ErrorDetails { get; set; }

    public TimeSpan Duration => EndTime - StartTime;

    public virtual Repository Repository { get; set; } = null!;
}

public enum ScanStatus
{
    Success,
    Warning,
    Error
}
import React, { useState, useEffect } from 'react';
import { useParams, Link, useSearchParams, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowLeft, Clock, Hash, User, FileText, Plus, Minus, Edit } from 'lucide-react';
import { commitApi } from '@/lib/api';
import type { CommitDetail } from '@/types/api';
import { toast } from '@/hooks/use-toast';

const Commit = () => {
  const { hash } = useParams<{ hash: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [commit, setCommit] = useState<CommitDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // 加载提交详情
  const loadCommit = async () => {
    if (!hash) return;
    
    try {
      setLoading(true);
      const data = await commitApi.getCommit(hash);
      setCommit(data);
    } catch (error) {
      console.error('Failed to load commit:', error);
      toast({
        title: "加载失败",
        description: "无法加载提交详情",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCommit();
  }, [hash]);

  const handleGoBack = () => {
    const from = searchParams.get('from');
    if (from) {
      // 如果有来源页面参数，导航到该页面以保持状态
      navigate(decodeURIComponent(from));
    } else {
      // 否则使用浏览器后退
      window.history.back();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'added':
        return <Plus className="w-4 h-4 text-green-600" />;
      case 'removed':
        return <Minus className="w-4 h-4 text-red-600" />;
      case 'modified':
        return <Edit className="w-4 h-4 text-yellow-600" />;
      default:
        return <FileText className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'added':
        return <Badge variant="added">新增</Badge>;
      case 'removed':
        return <Badge variant="removed">删除</Badge>;
      case 'modified':
        return <Badge variant="modified">修改</Badge>;
      default:
        return <Badge variant="default">{status}</Badge>;
    }
  };

  const getFileExtensionColor = (filename: string) => {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'ts':
      case 'tsx':
        return 'text-blue-600';
      case 'js':
      case 'jsx':
        return 'text-yellow-600';
      case 'json':
        return 'text-green-600';
      case 'md':
        return 'text-purple-600';
      default:
        return 'text-muted-foreground';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      </Layout>
    );
  }

  if (!commit) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-foreground mb-2">提交不存在</h2>
          <p className="text-muted-foreground mb-4">无法找到提交 {hash}</p>
          <Button onClick={handleGoBack}>
            返回
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleGoBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold text-foreground">提交详情</h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
              <Hash className="w-4 h-4" />
              <code className="text-xs">{commit.hash}</code>
            </div>
          </div>
        </div>

        {/* Commit Info */}
        <Card>
          <CardHeader>
            <div className="flex items-start gap-4">
              <Avatar className="w-12 h-12">
                <AvatarFallback>
                  {commit.author.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <CardTitle className="text-lg mb-2">
                  {commit.message}
                </CardTitle>
                
                <div className="flex flex-col md:flex-row md:items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span className="font-medium">{commit.author.name}</span>
                    {commit.author.username && <span>({commit.author.username})</span>}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {formatDate(commit.date)}
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-foreground">{commit.stats.filesChanged}</p>
                <p className="text-sm text-muted-foreground">文件变更</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">+{commit.stats.additions}</p>
                <p className="text-sm text-muted-foreground">新增行数</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">-{commit.stats.deletions}</p>
                <p className="text-sm text-muted-foreground">删除行数</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Files Changed */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">文件变更列表</CardTitle>
            <CardDescription>
              此提交共影响 {commit.files.length} 个文件
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-3">
              {commit.files.map((file, index) => (
                <Link
                  key={index}
                  to={`/commit/${hash}/file/${encodeURIComponent(file.filename)}?from=${encodeURIComponent(window.location.pathname + window.location.search)}`}
                  className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors group cursor-pointer"
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {getStatusIcon(file.status)}
                    
                    <div className="flex-1 min-w-0">
                      <p className={`font-mono text-sm font-medium truncate ${getFileExtensionColor(file.filename)}`}>
                        {file.filename}
                      </p>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground mt-1">
                        <span>+{file.additions} -{file.deletions}</span>
                        <span>{file.changes} 变更</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {getStatusBadge(file.status)}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Commit;
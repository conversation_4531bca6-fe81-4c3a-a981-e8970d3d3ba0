import React from 'react';

interface GitHubLogoProps {
  className?: string;
}

export const GitHubLogo: React.FC<GitHubLogoProps> = ({ className = "w-6 h-6" }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 10.95.6-.225 1.017-.6 1.017-1.017v-4.142c-3.383.725-4.1-1.425-4.1-1.425-.55-1.4-1.334-1.775-1.334-1.775-1.09-.742.083-.725.083-.725 1.2.083 1.833 1.225 1.833 1.225 1.067 1.834 2.8 1.3 3.492.992.1-.775.417-1.3.758-1.6-2.667-.3-5.467-1.334-5.467-5.934 0-1.3.467-2.383 1.233-3.217-.125-.3-.533-1.508.117-3.133 0 0 1.008-.325 3.3 1.233.958-.267 1.983-.4 3.008-.4 1.025 0 2.05.133 3.008.4 2.292-1.558 3.3-1.233 3.3-1.233.65 1.625.242 2.833.117 3.133.767.834 1.233 1.917 1.233 3.217 0 4.608-2.808 5.625-5.483 5.917.433.375.817 1.1.817 2.217v3.283c0 .417.408.792 1.017 1.017C20.84 21.4 23.996 17.062 23.996 11.987 23.996 5.367 18.63.001 12.017.001z"/>
    </svg>
  );
};
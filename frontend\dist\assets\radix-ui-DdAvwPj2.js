import{r as c,R as Ge,a as yt,b as ja,c as Fa}from"./react-vendor-DucYQ77b.js";function D(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ka(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function lo(...e){return t=>e.forEach(n=>ka(n,t))}function U(...e){return c.useCallback(lo(...e),e)}var uo={exports:{}},Mt={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=c,t=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),a=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen"),x=Symbol.iterator,p="@@iterator";function w(m){if(m===null||typeof m!="object")return null;var P=x&&m[x]||m[p];return typeof P=="function"?P:null}var b=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function y(m){{for(var P=arguments.length,A=new Array(P>1?P-1:0),$=1;$<P;$++)A[$-1]=arguments[$];S("error",m,A)}}function S(m,P,A){{var $=b.ReactDebugCurrentFrame,J=$.getStackAddendum();J!==""&&(P+="%s",A=A.concat([J]));var Q=A.map(function(K){return String(K)});Q.unshift("Warning: "+P),Function.prototype.apply.call(console[m],console,Q)}}var C=!1,E=!1,_=!1,R=!1,O=!1,I;I=Symbol.for("react.module.reference");function L(m){return!!(typeof m=="string"||typeof m=="function"||m===r||m===s||O||m===o||m===u||m===d||R||m===g||C||E||_||typeof m=="object"&&m!==null&&(m.$$typeof===v||m.$$typeof===f||m.$$typeof===i||m.$$typeof===a||m.$$typeof===l||m.$$typeof===I||m.getModuleId!==void 0))}function W(m,P,A){var $=m.displayName;if($)return $;var J=P.displayName||P.name||"";return J!==""?A+"("+J+")":A}function B(m){return m.displayName||"Context"}function k(m){if(m==null)return null;if(typeof m.tag=="number"&&y("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof m=="function")return m.displayName||m.name||null;if(typeof m=="string")return m;switch(m){case r:return"Fragment";case n:return"Portal";case s:return"Profiler";case o:return"StrictMode";case u:return"Suspense";case d:return"SuspenseList"}if(typeof m=="object")switch(m.$$typeof){case a:var P=m;return B(P)+".Consumer";case i:var A=m;return B(A._context)+".Provider";case l:return W(m,m.render,"ForwardRef");case f:var $=m.displayName||null;return $!==null?$:k(m.type)||"Memo";case v:{var J=m,Q=J._payload,K=J._init;try{return k(K(Q))}catch{return null}}}return null}var T=Object.assign,V=0,N,H,j,z,oe,ce,he;function ue(){}ue.__reactDisabledLog=!0;function de(){{if(V===0){N=console.log,H=console.info,j=console.warn,z=console.error,oe=console.group,ce=console.groupCollapsed,he=console.groupEnd;var m={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:m,log:m,warn:m,error:m,group:m,groupCollapsed:m,groupEnd:m})}V++}}function je(){{if(V--,V===0){var m={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:T({},m,{value:N}),info:T({},m,{value:H}),warn:T({},m,{value:j}),error:T({},m,{value:z}),group:T({},m,{value:oe}),groupCollapsed:T({},m,{value:ce}),groupEnd:T({},m,{value:he})})}V<0&&y("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var xe=b.ReactCurrentDispatcher,F;function q(m,P,A){{if(F===void 0)try{throw Error()}catch(J){var $=J.stack.trim().match(/\n( *(at )?)/);F=$&&$[1]||""}return`
`+F+m}}var ne=!1,G;{var Z=typeof WeakMap=="function"?WeakMap:Map;G=new Z}function X(m,P){if(!m||ne)return"";{var A=G.get(m);if(A!==void 0)return A}var $;ne=!0;var J=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Q;Q=xe.current,xe.current=null,de();try{if(P){var K=function(){throw Error()};if(Object.defineProperty(K.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(K,[])}catch(le){$=le}Reflect.construct(m,[],K)}else{try{K.call()}catch(le){$=le}m.call(K.prototype)}}else{try{throw Error()}catch(le){$=le}m()}}catch(le){if(le&&$&&typeof le.stack=="string"){for(var Y=le.stack.split(`
`),ae=$.stack.split(`
`),te=Y.length-1,re=ae.length-1;te>=1&&re>=0&&Y[te]!==ae[re];)re--;for(;te>=1&&re>=0;te--,re--)if(Y[te]!==ae[re]){if(te!==1||re!==1)do if(te--,re--,re<0||Y[te]!==ae[re]){var ge=`
`+Y[te].replace(" at new "," at ");return m.displayName&&ge.includes("<anonymous>")&&(ge=ge.replace("<anonymous>",m.displayName)),typeof m=="function"&&G.set(m,ge),ge}while(te>=1&&re>=0);break}}}finally{ne=!1,xe.current=Q,je(),Error.prepareStackTrace=J}var et=m?m.displayName||m.name:"",ze=et?q(et):"";return typeof m=="function"&&G.set(m,ze),ze}function ie(m,P,A){return X(m,!1)}function me(m){var P=m.prototype;return!!(P&&P.isReactComponent)}function Pe(m,P,A){if(m==null)return"";if(typeof m=="function")return X(m,me(m));if(typeof m=="string")return q(m);switch(m){case u:return q("Suspense");case d:return q("SuspenseList")}if(typeof m=="object")switch(m.$$typeof){case l:return ie(m.render);case f:return Pe(m.type,P,A);case v:{var $=m,J=$._payload,Q=$._init;try{return Pe(Q(J),P,A)}catch{}}}return""}var ye=Object.prototype.hasOwnProperty,Fe={},Rt=b.ReactDebugCurrentFrame;function De(m){if(m){var P=m._owner,A=Pe(m.type,m._source,P?P.type:null);Rt.setExtraStackFrame(A)}else Rt.setExtraStackFrame(null)}function va(m,P,A,$,J){{var Q=Function.call.bind(ye);for(var K in m)if(Q(m,K)){var Y=void 0;try{if(typeof m[K]!="function"){var ae=Error(($||"React class")+": "+A+" type `"+K+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof m[K]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw ae.name="Invariant Violation",ae}Y=m[K](P,K,$,A,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(te){Y=te}Y&&!(Y instanceof Error)&&(De(J),y("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",$||"React class",A,K,typeof Y),De(null)),Y instanceof Error&&!(Y.message in Fe)&&(Fe[Y.message]=!0,De(J),y("Failed %s type: %s",A,Y.message),De(null))}}}var ha=Array.isArray;function fn(m){return ha(m)}function ma(m){{var P=typeof Symbol=="function"&&Symbol.toStringTag,A=P&&m[Symbol.toStringTag]||m.constructor.name||"Object";return A}}function ga(m){try{return Tr(m),!1}catch{return!0}}function Tr(m){return""+m}function Ar(m){if(ga(m))return y("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",ma(m)),Tr(m)}var mt=b.ReactCurrentOwner,wa={key:!0,ref:!0,__self:!0,__source:!0},_r,Or,pn;pn={};function xa(m){if(ye.call(m,"ref")){var P=Object.getOwnPropertyDescriptor(m,"ref").get;if(P&&P.isReactWarning)return!1}return m.ref!==void 0}function ya(m){if(ye.call(m,"key")){var P=Object.getOwnPropertyDescriptor(m,"key").get;if(P&&P.isReactWarning)return!1}return m.key!==void 0}function ba(m,P){if(typeof m.ref=="string"&&mt.current&&P&&mt.current.stateNode!==P){var A=k(mt.current.type);pn[A]||(y('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',k(mt.current.type),m.ref),pn[A]=!0)}}function Sa(m,P){{var A=function(){_r||(_r=!0,y("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",P))};A.isReactWarning=!0,Object.defineProperty(m,"key",{get:A,configurable:!0})}}function Ca(m,P){{var A=function(){Or||(Or=!0,y("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",P))};A.isReactWarning=!0,Object.defineProperty(m,"ref",{get:A,configurable:!0})}}var Ea=function(m,P,A,$,J,Q,K){var Y={$$typeof:t,type:m,key:P,ref:A,props:K,_owner:Q};return Y._store={},Object.defineProperty(Y._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(Y,"_self",{configurable:!1,enumerable:!1,writable:!1,value:$}),Object.defineProperty(Y,"_source",{configurable:!1,enumerable:!1,writable:!1,value:J}),Object.freeze&&(Object.freeze(Y.props),Object.freeze(Y)),Y};function Pa(m,P,A,$,J){{var Q,K={},Y=null,ae=null;A!==void 0&&(Ar(A),Y=""+A),ya(P)&&(Ar(P.key),Y=""+P.key),xa(P)&&(ae=P.ref,ba(P,J));for(Q in P)ye.call(P,Q)&&!wa.hasOwnProperty(Q)&&(K[Q]=P[Q]);if(m&&m.defaultProps){var te=m.defaultProps;for(Q in te)K[Q]===void 0&&(K[Q]=te[Q])}if(Y||ae){var re=typeof m=="function"?m.displayName||m.name||"Unknown":m;Y&&Sa(K,re),ae&&Ca(K,re)}return Ea(m,Y,ae,J,$,mt.current,K)}}var vn=b.ReactCurrentOwner,Dr=b.ReactDebugCurrentFrame;function Qe(m){if(m){var P=m._owner,A=Pe(m.type,m._source,P?P.type:null);Dr.setExtraStackFrame(A)}else Dr.setExtraStackFrame(null)}var hn;hn=!1;function mn(m){return typeof m=="object"&&m!==null&&m.$$typeof===t}function Ir(){{if(vn.current){var m=k(vn.current.type);if(m)return`

Check the render method of \``+m+"`."}return""}}function Ra(m){return""}var Nr={};function Ta(m){{var P=Ir();if(!P){var A=typeof m=="string"?m:m.displayName||m.name;A&&(P=`

Check the top-level render call using <`+A+">.")}return P}}function Mr(m,P){{if(!m._store||m._store.validated||m.key!=null)return;m._store.validated=!0;var A=Ta(P);if(Nr[A])return;Nr[A]=!0;var $="";m&&m._owner&&m._owner!==vn.current&&($=" It was passed a child from "+k(m._owner.type)+"."),Qe(m),y('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',A,$),Qe(null)}}function Lr(m,P){{if(typeof m!="object")return;if(fn(m))for(var A=0;A<m.length;A++){var $=m[A];mn($)&&Mr($,P)}else if(mn(m))m._store&&(m._store.validated=!0);else if(m){var J=w(m);if(typeof J=="function"&&J!==m.entries)for(var Q=J.call(m),K;!(K=Q.next()).done;)mn(K.value)&&Mr(K.value,P)}}}function Aa(m){{var P=m.type;if(P==null||typeof P=="string")return;var A;if(typeof P=="function")A=P.propTypes;else if(typeof P=="object"&&(P.$$typeof===l||P.$$typeof===f))A=P.propTypes;else return;if(A){var $=k(P);va(A,m.props,"prop",$,m)}else if(P.PropTypes!==void 0&&!hn){hn=!0;var J=k(P);y("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",J||"Unknown")}typeof P.getDefaultProps=="function"&&!P.getDefaultProps.isReactClassApproved&&y("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function _a(m){{for(var P=Object.keys(m.props),A=0;A<P.length;A++){var $=P[A];if($!=="children"&&$!=="key"){Qe(m),y("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",$),Qe(null);break}}m.ref!==null&&(Qe(m),y("Invalid attribute `ref` supplied to `React.Fragment`."),Qe(null))}}var jr={};function Fr(m,P,A,$,J,Q){{var K=L(m);if(!K){var Y="";(m===void 0||typeof m=="object"&&m!==null&&Object.keys(m).length===0)&&(Y+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var ae=Ra();ae?Y+=ae:Y+=Ir();var te;m===null?te="null":fn(m)?te="array":m!==void 0&&m.$$typeof===t?(te="<"+(k(m.type)||"Unknown")+" />",Y=" Did you accidentally export a JSX literal instead of a component?"):te=typeof m,y("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",te,Y)}var re=Pa(m,P,A,J,Q);if(re==null)return re;if(K){var ge=P.children;if(ge!==void 0)if($)if(fn(ge)){for(var et=0;et<ge.length;et++)Lr(ge[et],m);Object.freeze&&Object.freeze(ge)}else y("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Lr(ge,m)}if(ye.call(P,"key")){var ze=k(m),le=Object.keys(P).filter(function(La){return La!=="key"}),gn=le.length>0?"{key: someKey, "+le.join(": ..., ")+": ...}":"{key: someKey}";if(!jr[ze+gn]){var Ma=le.length>0?"{"+le.join(": ..., ")+": ...}":"{}";y(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,gn,ze,Ma,ze),jr[ze+gn]=!0}}return m===r?_a(re):Aa(re),re}}function Oa(m,P,A){return Fr(m,P,A,!0)}function Da(m,P,A){return Fr(m,P,A,!1)}var Ia=Da,Na=Oa;Mt.Fragment=r,Mt.jsx=Ia,Mt.jsxs=Na})();uo.exports=Mt;var h=uo.exports;function $a(e,t=[]){let n=[];function r(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...x}=f,p=(v==null?void 0:v[e][l])||a,w=c.useMemo(()=>x,Object.values(x));return h.jsx(p.Provider,{value:w,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||a,x=c.useContext(g);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Wa(o,...t)]}function Wa(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Xe=c.forwardRef((e,t)=>{const{children:n,...r}=e,o=c.Children.toArray(n),s=o.find(Va);if(s){const i=s.props.children,a=o.map(l=>l===s?c.Children.count(i)>1?c.Children.only(null):c.isValidElement(i)?i.props.children:null:l);return h.jsx(_n,{...r,ref:t,children:c.isValidElement(i)?c.cloneElement(i,void 0,a):null})}return h.jsx(_n,{...r,ref:t,children:n})});Xe.displayName="Slot";var _n=c.forwardRef((e,t)=>{const{children:n,...r}=e;if(c.isValidElement(n)){const o=Ha(n);return c.cloneElement(n,{...Ba(r,n.props),ref:t?lo(t,o):o})}return c.Children.count(n)>1?c.Children.only(null):null});_n.displayName="SlotClone";var zn=({children:e})=>h.jsx(h.Fragment,{children:e});function Va(e){return c.isValidElement(e)&&e.type===zn}function Ba(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Ha(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Gn(e){const t=e+"CollectionProvider",[n,r]=$a(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:x,children:p}=g,w=Ge.useRef(null),b=Ge.useRef(new Map).current;return h.jsx(o,{scope:x,itemMap:b,collectionRef:w,children:p})};i.displayName=t;const a=e+"CollectionSlot",l=Ge.forwardRef((g,x)=>{const{scope:p,children:w}=g,b=s(a,p),y=U(x,b.collectionRef);return h.jsx(Xe,{ref:y,children:w})});l.displayName=a;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=Ge.forwardRef((g,x)=>{const{scope:p,children:w,...b}=g,y=Ge.useRef(null),S=U(x,y),C=s(u,p);return Ge.useEffect(()=>(C.itemMap.set(y,{ref:y,...b}),()=>void C.itemMap.delete(y))),h.jsx(Xe,{[d]:"",ref:S,children:w})});f.displayName=u;function v(g){const x=s(e+"CollectionConsumer",g);return Ge.useCallback(()=>{const w=x.collectionRef.current;if(!w)return[];const b=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(x.itemMap.values()).sort((C,E)=>b.indexOf(C.ref.current)-b.indexOf(E.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},v,r]}function Ua(e,t){const n=c.createContext(t),r=s=>{const{children:i,...a}=s,l=c.useMemo(()=>a,Object.values(a));return h.jsx(n.Provider,{value:l,children:i})};r.displayName=e+"Provider";function o(s){const i=c.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function Ae(e,t=[]){let n=[];function r(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];const u=f=>{var b;const{scope:v,children:g,...x}=f,p=((b=v==null?void 0:v[e])==null?void 0:b[l])||a,w=c.useMemo(()=>x,Object.values(x));return h.jsx(p.Provider,{value:w,children:g})};u.displayName=s+"Provider";function d(f,v){var p;const g=((p=v==null?void 0:v[e])==null?void 0:p[l])||a,x=c.useContext(g);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Ya(o,...t)]}function Ya(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Ka=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],M=Ka.reduce((e,t)=>{const n=c.forwardRef((r,o)=>{const{asChild:s,...i}=r,a=s?Xe:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),h.jsx(a,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function fo(e,t){e&&yt.flushSync(()=>e.dispatchEvent(t))}function ee(e){const t=c.useRef(e);return c.useEffect(()=>{t.current=e}),c.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function za(e,t=globalThis==null?void 0:globalThis.document){const n=ee(e);c.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ga="DismissableLayer",On="dismissableLayer.update",Xa="dismissableLayer.pointerDownOutside",qa="dismissableLayer.focusOutside",kr,po=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),dt=c.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,u=c.useContext(po),[d,f]=c.useState(null),v=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=c.useState({}),x=U(t,R=>f(R)),p=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=p.indexOf(w),y=d?p.indexOf(d):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=b,E=Ja(R=>{const O=R.target,I=[...u.branches].some(L=>L.contains(O));!C||I||(o==null||o(R),i==null||i(R),R.defaultPrevented||a==null||a())},v),_=Qa(R=>{const O=R.target;[...u.branches].some(L=>L.contains(O))||(s==null||s(R),i==null||i(R),R.defaultPrevented||a==null||a())},v);return za(R=>{y===u.layers.size-1&&(r==null||r(R),!R.defaultPrevented&&a&&(R.preventDefault(),a()))},v),c.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(kr=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),$r(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=kr)}},[d,v,n,u]),c.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),$r())},[d,u]),c.useEffect(()=>{const R=()=>g({});return document.addEventListener(On,R),()=>document.removeEventListener(On,R)},[]),h.jsx(M.div,{...l,ref:x,style:{pointerEvents:S?C?"auto":"none":void 0,...e.style},onFocusCapture:D(e.onFocusCapture,_.onFocusCapture),onBlurCapture:D(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:D(e.onPointerDownCapture,E.onPointerDownCapture)})});dt.displayName=Ga;var Za="DismissableLayerBranch",vo=c.forwardRef((e,t)=>{const n=c.useContext(po),r=c.useRef(null),o=U(t,r);return c.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),h.jsx(M.div,{...e,ref:o})});vo.displayName=Za;function Ja(e,t=globalThis==null?void 0:globalThis.document){const n=ee(e),r=c.useRef(!1),o=c.useRef(()=>{});return c.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){ho(Xa,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Qa(e,t=globalThis==null?void 0:globalThis.document){const n=ee(e),r=c.useRef(!1);return c.useEffect(()=>{const o=s=>{s.target&&!r.current&&ho(qa,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function $r(){const e=new CustomEvent(On);document.dispatchEvent(e)}function ho(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?fo(o,s):o.dispatchEvent(s)}var ec=dt,tc=vo,se=globalThis!=null&&globalThis.document?c.useLayoutEffect:()=>{},nc="Portal",bt=c.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=c.useState(!1);se(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?ja.createPortal(h.jsx(M.div,{...r,ref:t}),i):null});bt.displayName=nc;function rc(e,t){return c.useReducer((n,r)=>t[n][r]??n,e)}var ve=e=>{const{present:t,children:n}=e,r=oc(t),o=typeof n=="function"?n({present:r.isPresent}):c.Children.only(n),s=U(r.ref,sc(o));return typeof n=="function"||r.isPresent?c.cloneElement(o,{ref:s}):null};ve.displayName="Presence";function oc(e){const[t,n]=c.useState(),r=c.useRef({}),o=c.useRef(e),s=c.useRef("none"),i=e?"mounted":"unmounted",[a,l]=rc(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return c.useEffect(()=>{const u=Tt(r.current);s.current=a==="mounted"?u:"none"},[a]),se(()=>{const u=r.current,d=o.current;if(d!==e){const v=s.current,g=Tt(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),se(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,f=g=>{const p=Tt(r.current).includes(g.animationName);if(g.target===t&&p&&(l("ANIMATION_END"),!o.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},v=g=>{g.target===t&&(s.current=Tt(r.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:c.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Tt(e){return(e==null?void 0:e.animationName)||"none"}function sc(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function $e({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=ic({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=ee(n),l=c.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&a(f)}else o(u)},[s,e,o,a]);return[i,l]}function ic({defaultProp:e,onChange:t}){const n=c.useState(e),[r]=n,o=c.useRef(r),s=ee(t);return c.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var ac="VisuallyHidden",St=c.forwardRef((e,t)=>h.jsx(M.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));St.displayName=ac;var cc=St,Xn="ToastProvider",[qn,lc,uc]=Gn("Toast"),[mo,jf]=Ae("Toast",[uc]),[dc,zt]=mo(Xn),go=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[a,l]=c.useState(null),[u,d]=c.useState(0),f=c.useRef(!1),v=c.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Xn}\`. Expected non-empty \`string\`.`),h.jsx(qn.Provider,{scope:t,children:h.jsx(dc,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:c.useCallback(()=>d(g=>g+1),[]),onToastRemove:c.useCallback(()=>d(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:v,children:i})})};go.displayName=Xn;var wo="ToastViewport",fc=["F8"],Dn="toast.viewportPause",In="toast.viewportResume",xo=c.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=fc,label:o="Notifications ({hotkey})",...s}=e,i=zt(wo,n),a=lc(n),l=c.useRef(null),u=c.useRef(null),d=c.useRef(null),f=c.useRef(null),v=U(t,f,i.onViewportChange),g=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=i.toastCount>0;c.useEffect(()=>{const w=b=>{var S;r.length!==0&&r.every(C=>b[C]||b.code===C)&&((S=f.current)==null||S.focus())};return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[r]),c.useEffect(()=>{const w=l.current,b=f.current;if(x&&w&&b){const y=()=>{if(!i.isClosePausedRef.current){const _=new CustomEvent(Dn);b.dispatchEvent(_),i.isClosePausedRef.current=!0}},S=()=>{if(i.isClosePausedRef.current){const _=new CustomEvent(In);b.dispatchEvent(_),i.isClosePausedRef.current=!1}},C=_=>{!w.contains(_.relatedTarget)&&S()},E=()=>{w.contains(document.activeElement)||S()};return w.addEventListener("focusin",y),w.addEventListener("focusout",C),w.addEventListener("pointermove",y),w.addEventListener("pointerleave",E),window.addEventListener("blur",y),window.addEventListener("focus",S),()=>{w.removeEventListener("focusin",y),w.removeEventListener("focusout",C),w.removeEventListener("pointermove",y),w.removeEventListener("pointerleave",E),window.removeEventListener("blur",y),window.removeEventListener("focus",S)}}},[x,i.isClosePausedRef]);const p=c.useCallback(({tabbingDirection:w})=>{const y=a().map(S=>{const C=S.ref.current,E=[C,...Pc(C)];return w==="forwards"?E:E.reverse()});return(w==="forwards"?y.reverse():y).flat()},[a]);return c.useEffect(()=>{const w=f.current;if(w){const b=y=>{var E,_,R;const S=y.altKey||y.ctrlKey||y.metaKey;if(y.key==="Tab"&&!S){const O=document.activeElement,I=y.shiftKey;if(y.target===w&&I){(E=u.current)==null||E.focus();return}const B=p({tabbingDirection:I?"backwards":"forwards"}),k=B.findIndex(T=>T===O);wn(B.slice(k+1))?y.preventDefault():I?(_=u.current)==null||_.focus():(R=d.current)==null||R.focus()}};return w.addEventListener("keydown",b),()=>w.removeEventListener("keydown",b)}},[a,p]),h.jsxs(tc,{ref:l,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&h.jsx(Nn,{ref:u,onFocusFromOutsideViewport:()=>{const w=p({tabbingDirection:"forwards"});wn(w)}}),h.jsx(qn.Slot,{scope:n,children:h.jsx(M.ol,{tabIndex:-1,...s,ref:v})}),x&&h.jsx(Nn,{ref:d,onFocusFromOutsideViewport:()=>{const w=p({tabbingDirection:"backwards"});wn(w)}})]})});xo.displayName=wo;var yo="ToastFocusProxy",Nn=c.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=zt(yo,n);return h.jsx(St,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var u;const a=i.relatedTarget;!((u=s.viewport)!=null&&u.contains(a))&&r()}})});Nn.displayName=yo;var Gt="Toast",pc="toast.swipeStart",vc="toast.swipeMove",hc="toast.swipeCancel",mc="toast.swipeEnd",bo=c.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[a=!0,l]=$e({prop:r,defaultProp:o,onChange:s});return h.jsx(ve,{present:n||a,children:h.jsx(xc,{open:a,...i,ref:t,onClose:()=>l(!1),onPause:ee(e.onPause),onResume:ee(e.onResume),onSwipeStart:D(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:D(e.onSwipeMove,u=>{const{x:d,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:D(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:D(e.onSwipeEnd,u=>{const{x:d,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),l(!1)})})})});bo.displayName=Gt;var[gc,wc]=mo(Gt,{onClose(){}}),xc=c.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:d,onSwipeMove:f,onSwipeCancel:v,onSwipeEnd:g,...x}=e,p=zt(Gt,n),[w,b]=c.useState(null),y=U(t,T=>b(T)),S=c.useRef(null),C=c.useRef(null),E=o||p.duration,_=c.useRef(0),R=c.useRef(E),O=c.useRef(0),{onToastAdd:I,onToastRemove:L}=p,W=ee(()=>{var V;(w==null?void 0:w.contains(document.activeElement))&&((V=p.viewport)==null||V.focus()),i()}),B=c.useCallback(T=>{!T||T===1/0||(window.clearTimeout(O.current),_.current=new Date().getTime(),O.current=window.setTimeout(W,T))},[W]);c.useEffect(()=>{const T=p.viewport;if(T){const V=()=>{B(R.current),u==null||u()},N=()=>{const H=new Date().getTime()-_.current;R.current=R.current-H,window.clearTimeout(O.current),l==null||l()};return T.addEventListener(Dn,N),T.addEventListener(In,V),()=>{T.removeEventListener(Dn,N),T.removeEventListener(In,V)}}},[p.viewport,E,l,u,B]),c.useEffect(()=>{s&&!p.isClosePausedRef.current&&B(E)},[s,E,p.isClosePausedRef,B]),c.useEffect(()=>(I(),()=>L()),[I,L]);const k=c.useMemo(()=>w?Ao(w):null,[w]);return p.viewport?h.jsxs(h.Fragment,{children:[k&&h.jsx(yc,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:k}),h.jsx(gc,{scope:n,onClose:W,children:yt.createPortal(h.jsx(qn.ItemSlot,{scope:n,children:h.jsx(ec,{asChild:!0,onEscapeKeyDown:D(a,()=>{p.isFocusedToastEscapeKeyDownRef.current||W(),p.isFocusedToastEscapeKeyDownRef.current=!1}),children:h.jsx(M.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":p.swipeDirection,...x,ref:y,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:D(e.onKeyDown,T=>{T.key==="Escape"&&(a==null||a(T.nativeEvent),T.nativeEvent.defaultPrevented||(p.isFocusedToastEscapeKeyDownRef.current=!0,W()))}),onPointerDown:D(e.onPointerDown,T=>{T.button===0&&(S.current={x:T.clientX,y:T.clientY})}),onPointerMove:D(e.onPointerMove,T=>{if(!S.current)return;const V=T.clientX-S.current.x,N=T.clientY-S.current.y,H=!!C.current,j=["left","right"].includes(p.swipeDirection),z=["left","up"].includes(p.swipeDirection)?Math.min:Math.max,oe=j?z(0,V):0,ce=j?0:z(0,N),he=T.pointerType==="touch"?10:2,ue={x:oe,y:ce},de={originalEvent:T,delta:ue};H?(C.current=ue,At(vc,f,de,{discrete:!1})):Wr(ue,p.swipeDirection,he)?(C.current=ue,At(pc,d,de,{discrete:!1}),T.target.setPointerCapture(T.pointerId)):(Math.abs(V)>he||Math.abs(N)>he)&&(S.current=null)}),onPointerUp:D(e.onPointerUp,T=>{const V=C.current,N=T.target;if(N.hasPointerCapture(T.pointerId)&&N.releasePointerCapture(T.pointerId),C.current=null,S.current=null,V){const H=T.currentTarget,j={originalEvent:T,delta:V};Wr(V,p.swipeDirection,p.swipeThreshold)?At(mc,g,j,{discrete:!0}):At(hc,v,j,{discrete:!0}),H.addEventListener("click",z=>z.preventDefault(),{once:!0})}})})})}),p.viewport)})]}):null}),yc=e=>{const{__scopeToast:t,children:n,...r}=e,o=zt(Gt,t),[s,i]=c.useState(!1),[a,l]=c.useState(!1);return Cc(()=>i(!0)),c.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:h.jsx(bt,{asChild:!0,children:h.jsx(St,{...r,children:s&&h.jsxs(h.Fragment,{children:[o.label," ",n]})})})},bc="ToastTitle",So=c.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return h.jsx(M.div,{...r,ref:t})});So.displayName=bc;var Sc="ToastDescription",Co=c.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return h.jsx(M.div,{...r,ref:t})});Co.displayName=Sc;var Eo="ToastAction",Po=c.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?h.jsx(To,{altText:n,asChild:!0,children:h.jsx(Zn,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Eo}\`. Expected non-empty \`string\`.`),null)});Po.displayName=Eo;var Ro="ToastClose",Zn=c.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=wc(Ro,n);return h.jsx(To,{asChild:!0,children:h.jsx(M.button,{type:"button",...r,ref:t,onClick:D(e.onClick,o.onClose)})})});Zn.displayName=Ro;var To=c.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return h.jsx(M.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Ao(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),Ec(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Ao(r))}}),t}function At(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?fo(o,s):o.dispatchEvent(s)}var Wr=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function Cc(e=()=>{}){const t=ee(e);se(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function Ec(e){return e.nodeType===e.ELEMENT_NODE}function Pc(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function wn(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var Ff=go,kf=xo,$f=bo,Wf=So,Vf=Co,Bf=Po,Hf=Zn,Rc=Fa.useId||(()=>{}),Tc=0;function Ie(e){const[t,n]=c.useState(Rc());return se(()=>{n(r=>r??String(Tc++))},[e]),t?`radix-${t}`:""}const Ac=["top","right","bottom","left"],We=Math.min,fe=Math.max,kt=Math.round,_t=Math.floor,Ve=e=>({x:e,y:e}),_c={left:"right",right:"left",bottom:"top",top:"bottom"},Oc={start:"end",end:"start"};function Mn(e,t,n){return fe(e,We(t,n))}function Ne(e,t){return typeof e=="function"?e(t):e}function Me(e){return e.split("-")[0]}function ft(e){return e.split("-")[1]}function Jn(e){return e==="x"?"y":"x"}function Qn(e){return e==="y"?"height":"width"}function Be(e){return["top","bottom"].includes(Me(e))?"y":"x"}function er(e){return Jn(Be(e))}function Dc(e,t,n){n===void 0&&(n=!1);const r=ft(e),o=er(e),s=Qn(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=$t(i)),[i,$t(i)]}function Ic(e){const t=$t(e);return[Ln(e),t,Ln(t)]}function Ln(e){return e.replace(/start|end/g,t=>Oc[t])}function Nc(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function Mc(e,t,n,r){const o=ft(e);let s=Nc(Me(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Ln)))),s}function $t(e){return e.replace(/left|right|bottom|top/g,t=>_c[t])}function Lc(e){return{top:0,right:0,bottom:0,left:0,...e}}function _o(e){return typeof e!="number"?Lc(e):{top:e,right:e,bottom:e,left:e}}function Wt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Vr(e,t,n){let{reference:r,floating:o}=e;const s=Be(t),i=er(t),a=Qn(i),l=Me(t),u=s==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,v=r[a]/2-o[a]/2;let g;switch(l){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:f};break;case"left":g={x:r.x-o.width,y:f};break;default:g={x:r.x,y:r.y}}switch(ft(t)){case"start":g[i]-=v*(n&&u?-1:1);break;case"end":g[i]+=v*(n&&u?-1:1);break}return g}const jc=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=Vr(u,r,l),v=r,g={},x=0;for(let p=0;p<a.length;p++){const{name:w,fn:b}=a[p],{x:y,y:S,data:C,reset:E}=await b({x:d,y:f,initialPlacement:r,placement:v,strategy:o,middlewareData:g,rects:u,platform:i,elements:{reference:e,floating:t}});d=y??d,f=S??f,g={...g,[w]:{...g[w],...C}},E&&x<=50&&(x++,typeof E=="object"&&(E.placement&&(v=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:f}=Vr(u,v,l)),p=-1)}return{x:d,y:f,placement:v,strategy:o,middlewareData:g}};async function wt(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:v=!1,padding:g=0}=Ne(t,e),x=_o(g),w=a[v?f==="floating"?"reference":"floating":f],b=Wt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(w)))==null||n?w:w.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),y=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,S=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),C=await(s.isElement==null?void 0:s.isElement(S))?await(s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},E=Wt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:S,strategy:l}):y);return{top:(b.top-E.top+x.top)/C.y,bottom:(E.bottom-b.bottom+x.bottom)/C.y,left:(b.left-E.left+x.left)/C.x,right:(E.right-b.right+x.right)/C.x}}const Fc=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=Ne(e,t)||{};if(u==null)return{};const f=_o(d),v={x:n,y:r},g=er(o),x=Qn(g),p=await i.getDimensions(u),w=g==="y",b=w?"top":"left",y=w?"bottom":"right",S=w?"clientHeight":"clientWidth",C=s.reference[x]+s.reference[g]-v[g]-s.floating[x],E=v[g]-s.reference[g],_=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let R=_?_[S]:0;(!R||!await(i.isElement==null?void 0:i.isElement(_)))&&(R=a.floating[S]||s.floating[x]);const O=C/2-E/2,I=R/2-p[x]/2-1,L=We(f[b],I),W=We(f[y],I),B=L,k=R-p[x]-W,T=R/2-p[x]/2+O,V=Mn(B,T,k),N=!l.arrow&&ft(o)!=null&&T!==V&&s.reference[x]/2-(T<B?L:W)-p[x]/2<0,H=N?T<B?T-B:T-k:0;return{[g]:v[g]+H,data:{[g]:V,centerOffset:T-V-H,...N&&{alignmentOffset:H}},reset:N}}}),kc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:v,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:p=!0,...w}=Ne(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const b=Me(o),y=Be(a),S=Me(a)===a,C=await(l.isRTL==null?void 0:l.isRTL(u.floating)),E=v||(S||!p?[$t(a)]:Ic(a)),_=x!=="none";!v&&_&&E.push(...Mc(a,p,x,C));const R=[a,...E],O=await wt(t,w),I=[];let L=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&I.push(O[b]),f){const T=Dc(o,i,C);I.push(O[T[0]],O[T[1]])}if(L=[...L,{placement:o,overflows:I}],!I.every(T=>T<=0)){var W,B;const T=(((W=s.flip)==null?void 0:W.index)||0)+1,V=R[T];if(V)return{data:{index:T,overflows:L},reset:{placement:V}};let N=(B=L.filter(H=>H.overflows[0]<=0).sort((H,j)=>H.overflows[1]-j.overflows[1])[0])==null?void 0:B.placement;if(!N)switch(g){case"bestFit":{var k;const H=(k=L.filter(j=>{if(_){const z=Be(j.placement);return z===y||z==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(z=>z>0).reduce((z,oe)=>z+oe,0)]).sort((j,z)=>j[1]-z[1])[0])==null?void 0:k[0];H&&(N=H);break}case"initialPlacement":N=a;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function Br(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Hr(e){return Ac.some(t=>e[t]>=0)}const $c=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Ne(e,t);switch(r){case"referenceHidden":{const s=await wt(t,{...o,elementContext:"reference"}),i=Br(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Hr(i)}}}case"escaped":{const s=await wt(t,{...o,altBoundary:!0}),i=Br(s,n.floating);return{data:{escapedOffsets:i,escaped:Hr(i)}}}default:return{}}}}};async function Wc(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=Me(n),a=ft(n),l=Be(n)==="y",u=["left","top"].includes(i)?-1:1,d=s&&l?-1:1,f=Ne(t,e);let{mainAxis:v,crossAxis:g,alignmentAxis:x}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof x=="number"&&(g=a==="end"?x*-1:x),l?{x:g*d,y:v*u}:{x:v*u,y:g*d}}const Vc=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await Wc(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},Bc=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:w=>{let{x:b,y}=w;return{x:b,y}}},...l}=Ne(e,t),u={x:n,y:r},d=await wt(t,l),f=Be(Me(o)),v=Jn(f);let g=u[v],x=u[f];if(s){const w=v==="y"?"top":"left",b=v==="y"?"bottom":"right",y=g+d[w],S=g-d[b];g=Mn(y,g,S)}if(i){const w=f==="y"?"top":"left",b=f==="y"?"bottom":"right",y=x+d[w],S=x-d[b];x=Mn(y,x,S)}const p=a.fn({...t,[v]:g,[f]:x});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[v]:s,[f]:i}}}}}},Hc=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=Ne(e,t),d={x:n,y:r},f=Be(o),v=Jn(f);let g=d[v],x=d[f];const p=Ne(a,t),w=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const S=v==="y"?"height":"width",C=s.reference[v]-s.floating[S]+w.mainAxis,E=s.reference[v]+s.reference[S]-w.mainAxis;g<C?g=C:g>E&&(g=E)}if(u){var b,y;const S=v==="y"?"width":"height",C=["top","left"].includes(Me(o)),E=s.reference[f]-s.floating[S]+(C&&((b=i.offset)==null?void 0:b[f])||0)+(C?0:w.crossAxis),_=s.reference[f]+s.reference[S]+(C?0:((y=i.offset)==null?void 0:y[f])||0)-(C?w.crossAxis:0);x<E?x=E:x>_&&(x=_)}return{[v]:g,[f]:x}}}},Uc=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=Ne(e,t),d=await wt(t,u),f=Me(o),v=ft(o),g=Be(o)==="y",{width:x,height:p}=s.floating;let w,b;f==="top"||f==="bottom"?(w=f,b=v===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(b=f,w=v==="end"?"top":"bottom");const y=p-d.top-d.bottom,S=x-d.left-d.right,C=We(p-d[w],y),E=We(x-d[b],S),_=!t.middlewareData.shift;let R=C,O=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(O=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(R=y),_&&!v){const L=fe(d.left,0),W=fe(d.right,0),B=fe(d.top,0),k=fe(d.bottom,0);g?O=x-2*(L!==0||W!==0?L+W:fe(d.left,d.right)):R=p-2*(B!==0||k!==0?B+k:fe(d.top,d.bottom))}await l({...t,availableWidth:O,availableHeight:R});const I=await i.getDimensions(a.floating);return x!==I.width||p!==I.height?{reset:{rects:!0}}:{}}}};function Xt(){return typeof window<"u"}function pt(e){return Oo(e)?(e.nodeName||"").toLowerCase():"#document"}function pe(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function _e(e){var t;return(t=(Oo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Oo(e){return Xt()?e instanceof Node||e instanceof pe(e).Node:!1}function Se(e){return Xt()?e instanceof Element||e instanceof pe(e).Element:!1}function Te(e){return Xt()?e instanceof HTMLElement||e instanceof pe(e).HTMLElement:!1}function Ur(e){return!Xt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof pe(e).ShadowRoot}function Ct(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ce(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Yc(e){return["table","td","th"].includes(pt(e))}function qt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function tr(e){const t=nr(),n=Se(e)?Ce(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Kc(e){let t=He(e);for(;Te(t)&&!at(t);){if(tr(t))return t;if(qt(t))return null;t=He(t)}return null}function nr(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function at(e){return["html","body","#document"].includes(pt(e))}function Ce(e){return pe(e).getComputedStyle(e)}function Zt(e){return Se(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function He(e){if(pt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ur(e)&&e.host||_e(e);return Ur(t)?t.host:t}function Do(e){const t=He(e);return at(t)?e.ownerDocument?e.ownerDocument.body:e.body:Te(t)&&Ct(t)?t:Do(t)}function xt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Do(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=pe(o);if(s){const a=jn(i);return t.concat(i,i.visualViewport||[],Ct(o)?o:[],a&&n?xt(a):[])}return t.concat(o,xt(o,[],n))}function jn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Io(e){const t=Ce(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Te(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=kt(n)!==s||kt(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function rr(e){return Se(e)?e:e.contextElement}function ot(e){const t=rr(e);if(!Te(t))return Ve(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Io(t);let i=(s?kt(n.width):n.width)/r,a=(s?kt(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const zc=Ve(0);function No(e){const t=pe(e);return!nr()||!t.visualViewport?zc:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Gc(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==pe(e)?!1:t}function qe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=rr(e);let i=Ve(1);t&&(r?Se(r)&&(i=ot(r)):i=ot(e));const a=Gc(s,n,r)?No(s):Ve(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,d=o.width/i.x,f=o.height/i.y;if(s){const v=pe(s),g=r&&Se(r)?pe(r):r;let x=v,p=jn(x);for(;p&&r&&g!==x;){const w=ot(p),b=p.getBoundingClientRect(),y=Ce(p),S=b.left+(p.clientLeft+parseFloat(y.paddingLeft))*w.x,C=b.top+(p.clientTop+parseFloat(y.paddingTop))*w.y;l*=w.x,u*=w.y,d*=w.x,f*=w.y,l+=S,u+=C,x=pe(p),p=jn(x)}}return Wt({width:d,height:f,x:l,y:u})}function Xc(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=_e(r),a=t?qt(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=Ve(1);const d=Ve(0),f=Te(r);if((f||!f&&!s)&&((pt(r)!=="body"||Ct(i))&&(l=Zt(r)),Te(r))){const v=qe(r);u=ot(r),d.x=v.x+r.clientLeft,d.y=v.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x,y:n.y*u.y-l.scrollTop*u.y+d.y}}function qc(e){return Array.from(e.getClientRects())}function Fn(e,t){const n=Zt(e).scrollLeft;return t?t.left+n:qe(_e(e)).left+n}function Zc(e){const t=_e(e),n=Zt(e),r=e.ownerDocument.body,o=fe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=fe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Fn(e);const a=-n.scrollTop;return Ce(r).direction==="rtl"&&(i+=fe(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function Jc(e,t){const n=pe(e),r=_e(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=nr();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}function Qc(e,t){const n=qe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Te(e)?ot(e):Ve(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:a,x:l,y:u}}function Yr(e,t,n){let r;if(t==="viewport")r=Jc(e,n);else if(t==="document")r=Zc(_e(e));else if(Se(t))r=Qc(t,n);else{const o=No(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Wt(r)}function Mo(e,t){const n=He(e);return n===t||!Se(n)||at(n)?!1:Ce(n).position==="fixed"||Mo(n,t)}function el(e,t){const n=t.get(e);if(n)return n;let r=xt(e,[],!1).filter(a=>Se(a)&&pt(a)!=="body"),o=null;const s=Ce(e).position==="fixed";let i=s?He(e):e;for(;Se(i)&&!at(i);){const a=Ce(i),l=tr(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ct(i)&&!l&&Mo(e,i))?r=r.filter(d=>d!==i):o=a,i=He(i)}return t.set(e,r),r}function tl(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?qt(t)?[]:el(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((u,d)=>{const f=Yr(t,d,o);return u.top=fe(f.top,u.top),u.right=We(f.right,u.right),u.bottom=We(f.bottom,u.bottom),u.left=fe(f.left,u.left),u},Yr(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function nl(e){const{width:t,height:n}=Io(e);return{width:t,height:n}}function rl(e,t,n){const r=Te(t),o=_e(t),s=n==="fixed",i=qe(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=Ve(0);if(r||!r&&!s)if((pt(t)!=="body"||Ct(o))&&(a=Zt(t)),r){const g=qe(t,!0,s,t);l.x=g.x+t.clientLeft,l.y=g.y+t.clientTop}else o&&(l.x=Fn(o));let u=0,d=0;if(o&&!r&&!s){const g=o.getBoundingClientRect();d=g.top+a.scrollTop,u=g.left+a.scrollLeft-Fn(o,g)}const f=i.left+a.scrollLeft-l.x-u,v=i.top+a.scrollTop-l.y-d;return{x:f,y:v,width:i.width,height:i.height}}function xn(e){return Ce(e).position==="static"}function Kr(e,t){if(!Te(e)||Ce(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return _e(e)===n&&(n=n.ownerDocument.body),n}function Lo(e,t){const n=pe(e);if(qt(e))return n;if(!Te(e)){let o=He(e);for(;o&&!at(o);){if(Se(o)&&!xn(o))return o;o=He(o)}return n}let r=Kr(e,t);for(;r&&Yc(r)&&xn(r);)r=Kr(r,t);return r&&at(r)&&xn(r)&&!tr(r)?n:r||Kc(e)||n}const ol=async function(e){const t=this.getOffsetParent||Lo,n=this.getDimensions,r=await n(e.floating);return{reference:rl(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function sl(e){return Ce(e).direction==="rtl"}const il={convertOffsetParentRelativeRectToViewportRelativeRect:Xc,getDocumentElement:_e,getClippingRect:tl,getOffsetParent:Lo,getElementRects:ol,getClientRects:qc,getDimensions:nl,getScale:ot,isElement:Se,isRTL:sl};function al(e,t){let n=null,r;const o=_e(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const{left:u,top:d,width:f,height:v}=e.getBoundingClientRect();if(a||t(),!f||!v)return;const g=_t(d),x=_t(o.clientWidth-(u+f)),p=_t(o.clientHeight-(d+v)),w=_t(u),y={rootMargin:-g+"px "+-x+"px "+-p+"px "+-w+"px",threshold:fe(0,We(1,l))||1};let S=!0;function C(E){const _=E[0].intersectionRatio;if(_!==l){if(!S)return i();_?i(!1,_):r=setTimeout(()=>{i(!1,1e-7)},1e3)}S=!1}try{n=new IntersectionObserver(C,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,y)}n.observe(e)}return i(!0),s}function cl(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=rr(e),d=o||s?[...u?xt(u):[],...xt(t)]:[];d.forEach(b=>{o&&b.addEventListener("scroll",n,{passive:!0}),s&&b.addEventListener("resize",n)});const f=u&&a?al(u,n):null;let v=-1,g=null;i&&(g=new ResizeObserver(b=>{let[y]=b;y&&y.target===u&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var S;(S=g)==null||S.observe(t)})),n()}),u&&!l&&g.observe(u),g.observe(t));let x,p=l?qe(e):null;l&&w();function w(){const b=qe(e);p&&(b.x!==p.x||b.y!==p.y||b.width!==p.width||b.height!==p.height)&&n(),p=b,x=requestAnimationFrame(w)}return n(),()=>{var b;d.forEach(y=>{o&&y.removeEventListener("scroll",n),s&&y.removeEventListener("resize",n)}),f==null||f(),(b=g)==null||b.disconnect(),g=null,l&&cancelAnimationFrame(x)}}const ll=Vc,ul=Bc,dl=kc,fl=Uc,pl=$c,zr=Fc,vl=Hc,hl=(e,t,n)=>{const r=new Map,o={platform:il,...n},s={...o.platform,_c:r};return jc(e,t,{...o,platform:s})};var Lt=typeof document<"u"?c.useLayoutEffect:c.useEffect;function Vt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Vt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Vt(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function jo(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Gr(e,t){const n=jo(e);return Math.round(t*n)/n}function yn(e){const t=c.useRef(e);return Lt(()=>{t.current=e}),t}function ml(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[d,f]=c.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,g]=c.useState(r);Vt(v,r)||g(r);const[x,p]=c.useState(null),[w,b]=c.useState(null),y=c.useCallback(j=>{j!==_.current&&(_.current=j,p(j))},[]),S=c.useCallback(j=>{j!==R.current&&(R.current=j,b(j))},[]),C=s||x,E=i||w,_=c.useRef(null),R=c.useRef(null),O=c.useRef(d),I=l!=null,L=yn(l),W=yn(o),B=yn(u),k=c.useCallback(()=>{if(!_.current||!R.current)return;const j={placement:t,strategy:n,middleware:v};W.current&&(j.platform=W.current),hl(_.current,R.current,j).then(z=>{const oe={...z,isPositioned:B.current!==!1};T.current&&!Vt(O.current,oe)&&(O.current=oe,yt.flushSync(()=>{f(oe)}))})},[v,t,n,W,B]);Lt(()=>{u===!1&&O.current.isPositioned&&(O.current.isPositioned=!1,f(j=>({...j,isPositioned:!1})))},[u]);const T=c.useRef(!1);Lt(()=>(T.current=!0,()=>{T.current=!1}),[]),Lt(()=>{if(C&&(_.current=C),E&&(R.current=E),C&&E){if(L.current)return L.current(C,E,k);k()}},[C,E,k,L,I]);const V=c.useMemo(()=>({reference:_,floating:R,setReference:y,setFloating:S}),[y,S]),N=c.useMemo(()=>({reference:C,floating:E}),[C,E]),H=c.useMemo(()=>{const j={position:n,left:0,top:0};if(!N.floating)return j;const z=Gr(N.floating,d.x),oe=Gr(N.floating,d.y);return a?{...j,transform:"translate("+z+"px, "+oe+"px)",...jo(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:z,top:oe}},[n,a,N.floating,d.x,d.y]);return c.useMemo(()=>({...d,update:k,refs:V,elements:N,floatingStyles:H}),[d,k,V,N,H])}const gl=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?zr({element:r.current,padding:o}).fn(n):{}:r?zr({element:r,padding:o}).fn(n):{}}}},wl=(e,t)=>({...ll(e),options:[e,t]}),xl=(e,t)=>({...ul(e),options:[e,t]}),yl=(e,t)=>({...vl(e),options:[e,t]}),bl=(e,t)=>({...dl(e),options:[e,t]}),Sl=(e,t)=>({...fl(e),options:[e,t]}),Cl=(e,t)=>({...pl(e),options:[e,t]}),El=(e,t)=>({...gl(e),options:[e,t]});var Pl="Arrow",Fo=c.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return h.jsx(M.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:h.jsx("polygon",{points:"0,0 30,0 15,10"})})});Fo.displayName=Pl;var Rl=Fo;function Tl(e,t=[]){let n=[];function r(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...x}=f,p=(v==null?void 0:v[e][l])||a,w=c.useMemo(()=>x,Object.values(x));return h.jsx(p.Provider,{value:w,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||a,x=c.useContext(g);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Al(o,...t)]}function Al(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function ko(e){const[t,n]=c.useState(void 0);return se(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var or="Popper",[$o,vt]=Tl(or),[_l,Wo]=$o(or),Vo=e=>{const{__scopePopper:t,children:n}=e,[r,o]=c.useState(null);return h.jsx(_l,{scope:t,anchor:r,onAnchorChange:o,children:n})};Vo.displayName=or;var Bo="PopperAnchor",Ho=c.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Wo(Bo,n),i=c.useRef(null),a=U(t,i);return c.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:h.jsx(M.div,{...o,ref:a})});Ho.displayName=Bo;var sr="PopperContent",[Ol,Dl]=$o(sr),Uo=c.forwardRef((e,t)=>{var F,q,ne,G,Z,X;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:x,...p}=e,w=Wo(sr,n),[b,y]=c.useState(null),S=U(t,ie=>y(ie)),[C,E]=c.useState(null),_=ko(C),R=(_==null?void 0:_.width)??0,O=(_==null?void 0:_.height)??0,I=r+(s!=="center"?"-"+s:""),L=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},W=Array.isArray(u)?u:[u],B=W.length>0,k={padding:L,boundary:W.filter(Nl),altBoundary:B},{refs:T,floatingStyles:V,placement:N,isPositioned:H,middlewareData:j}=ml({strategy:"fixed",placement:I,whileElementsMounted:(...ie)=>cl(...ie,{animationFrame:g==="always"}),elements:{reference:w.anchor},middleware:[wl({mainAxis:o+O,alignmentAxis:i}),l&&xl({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?yl():void 0,...k}),l&&bl({...k}),Sl({...k,apply:({elements:ie,rects:me,availableWidth:Pe,availableHeight:ye})=>{const{width:Fe,height:Rt}=me.reference,De=ie.floating.style;De.setProperty("--radix-popper-available-width",`${Pe}px`),De.setProperty("--radix-popper-available-height",`${ye}px`),De.setProperty("--radix-popper-anchor-width",`${Fe}px`),De.setProperty("--radix-popper-anchor-height",`${Rt}px`)}}),C&&El({element:C,padding:a}),Ml({arrowWidth:R,arrowHeight:O}),v&&Cl({strategy:"referenceHidden",...k})]}),[z,oe]=zo(N),ce=ee(x);se(()=>{H&&(ce==null||ce())},[H,ce]);const he=(F=j.arrow)==null?void 0:F.x,ue=(q=j.arrow)==null?void 0:q.y,de=((ne=j.arrow)==null?void 0:ne.centerOffset)!==0,[je,xe]=c.useState();return se(()=>{b&&xe(window.getComputedStyle(b).zIndex)},[b]),h.jsx("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:H?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:je,"--radix-popper-transform-origin":[(G=j.transformOrigin)==null?void 0:G.x,(Z=j.transformOrigin)==null?void 0:Z.y].join(" "),...((X=j.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:h.jsx(Ol,{scope:n,placedSide:z,onArrowChange:E,arrowX:he,arrowY:ue,shouldHideArrow:de,children:h.jsx(M.div,{"data-side":z,"data-align":oe,...p,ref:S,style:{...p.style,animation:H?void 0:"none"}})})})});Uo.displayName=sr;var Yo="PopperArrow",Il={top:"bottom",right:"left",bottom:"top",left:"right"},Ko=c.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Dl(Yo,r),i=Il[s.placedSide];return h.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:h.jsx(Rl,{...o,ref:n,style:{...o.style,display:"block"}})})});Ko.displayName=Yo;function Nl(e){return e!==null}var Ml=e=>({name:"transformOrigin",options:e,fn(t){var w,b,y;const{placement:n,rects:r,middlewareData:o}=t,i=((w=o.arrow)==null?void 0:w.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,d]=zo(n),f={start:"0%",center:"50%",end:"100%"}[d],v=(((b=o.arrow)==null?void 0:b.x)??0)+a/2,g=(((y=o.arrow)==null?void 0:y.y)??0)+l/2;let x="",p="";return u==="bottom"?(x=i?f:`${v}px`,p=`${-l}px`):u==="top"?(x=i?f:`${v}px`,p=`${r.floating.height+l}px`):u==="right"?(x=`${-l}px`,p=i?f:`${g}px`):u==="left"&&(x=`${r.floating.width+l}px`,p=i?f:`${g}px`),{data:{x,y:p}}}});function zo(e){const[t,n="center"]=e.split("-");return[t,n]}var Go=Vo,Jt=Ho,ir=Uo,ar=Ko,[Qt,Uf]=Ae("Tooltip",[vt]),cr=vt(),Xo="TooltipProvider",Ll=700,Xr="tooltip.open",[jl,qo]=Qt(Xo),Zo=e=>{const{__scopeTooltip:t,delayDuration:n=Ll,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,a]=c.useState(!0),l=c.useRef(!1),u=c.useRef(0);return c.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),h.jsx(jl,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:c.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:c.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:c.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:s})};Zo.displayName=Xo;var Jo="Tooltip",[Yf,en]=Qt(Jo),kn="TooltipTrigger",Fl=c.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=en(kn,n),s=qo(kn,n),i=cr(n),a=c.useRef(null),l=U(t,a,o.onTriggerChange),u=c.useRef(!1),d=c.useRef(!1),f=c.useCallback(()=>u.current=!1,[]);return c.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),h.jsx(Jt,{asChild:!0,...i,children:h.jsx(M.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:D(e.onPointerMove,v=>{v.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:D(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:D(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:D(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:D(e.onBlur,o.onClose),onClick:D(e.onClick,o.onClose)})})});Fl.displayName=kn;var kl="TooltipPortal",[Kf,$l]=Qt(kl,{forceMount:void 0}),ct="TooltipContent",Qo=c.forwardRef((e,t)=>{const n=$l(ct,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=en(ct,e.__scopeTooltip);return h.jsx(ve,{present:r||i.open,children:i.disableHoverableContent?h.jsx(es,{side:o,...s,ref:t}):h.jsx(Wl,{side:o,...s,ref:t})})}),Wl=c.forwardRef((e,t)=>{const n=en(ct,e.__scopeTooltip),r=qo(ct,e.__scopeTooltip),o=c.useRef(null),s=U(t,o),[i,a]=c.useState(null),{trigger:l,onClose:u}=n,d=o.current,{onPointerInTransitChange:f}=r,v=c.useCallback(()=>{a(null),f(!1)},[f]),g=c.useCallback((x,p)=>{const w=x.currentTarget,b={x:x.clientX,y:x.clientY},y=Ul(b,w.getBoundingClientRect()),S=Yl(b,y),C=Kl(p.getBoundingClientRect()),E=Gl([...S,...C]);a(E),f(!0)},[f]);return c.useEffect(()=>()=>v(),[v]),c.useEffect(()=>{if(l&&d){const x=w=>g(w,d),p=w=>g(w,l);return l.addEventListener("pointerleave",x),d.addEventListener("pointerleave",p),()=>{l.removeEventListener("pointerleave",x),d.removeEventListener("pointerleave",p)}}},[l,d,g,v]),c.useEffect(()=>{if(i){const x=p=>{const w=p.target,b={x:p.clientX,y:p.clientY},y=(l==null?void 0:l.contains(w))||(d==null?void 0:d.contains(w)),S=!zl(b,i);y?v():S&&(v(),u())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[l,d,i,u,v]),h.jsx(es,{...e,ref:s})}),[Vl,Bl]=Qt(Jo,{isInside:!1}),es=c.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=en(ct,n),u=cr(n),{onClose:d}=l;return c.useEffect(()=>(document.addEventListener(Xr,d),()=>document.removeEventListener(Xr,d)),[d]),c.useEffect(()=>{if(l.trigger){const f=v=>{const g=v.target;g!=null&&g.contains(l.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,d]),h.jsx(dt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:h.jsxs(ir,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[h.jsx(zn,{children:r}),h.jsx(Vl,{scope:n,isInside:!0,children:h.jsx(cc,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Qo.displayName=ct;var ts="TooltipArrow",Hl=c.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=cr(n);return Bl(ts,n).isInside?null:h.jsx(ar,{...o,...r,ref:t})});Hl.displayName=ts;function Ul(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Yl(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Kl(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function zl(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>r!=d>r&&n<(u-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function Gl(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Xl(t)}function Xl(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var zf=Zo,Gf=Qo,bn="focusScope.autoFocusOnMount",Sn="focusScope.autoFocusOnUnmount",qr={bubbles:!1,cancelable:!0},ql="FocusScope",tn=c.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=c.useState(null),u=ee(o),d=ee(s),f=c.useRef(null),v=U(t,p=>l(p)),g=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(r){let p=function(S){if(g.paused||!a)return;const C=S.target;a.contains(C)?f.current=C:ke(f.current,{select:!0})},w=function(S){if(g.paused||!a)return;const C=S.relatedTarget;C!==null&&(a.contains(C)||ke(f.current,{select:!0}))},b=function(S){if(document.activeElement===document.body)for(const E of S)E.removedNodes.length>0&&ke(a)};document.addEventListener("focusin",p),document.addEventListener("focusout",w);const y=new MutationObserver(b);return a&&y.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",w),y.disconnect()}}},[r,a,g.paused]),c.useEffect(()=>{if(a){Jr.add(g);const p=document.activeElement;if(!a.contains(p)){const b=new CustomEvent(bn,qr);a.addEventListener(bn,u),a.dispatchEvent(b),b.defaultPrevented||(Zl(nu(ns(a)),{select:!0}),document.activeElement===p&&ke(a))}return()=>{a.removeEventListener(bn,u),setTimeout(()=>{const b=new CustomEvent(Sn,qr);a.addEventListener(Sn,d),a.dispatchEvent(b),b.defaultPrevented||ke(p??document.body,{select:!0}),a.removeEventListener(Sn,d),Jr.remove(g)},0)}}},[a,u,d,g]);const x=c.useCallback(p=>{if(!n&&!r||g.paused)return;const w=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,b=document.activeElement;if(w&&b){const y=p.currentTarget,[S,C]=Jl(y);S&&C?!p.shiftKey&&b===C?(p.preventDefault(),n&&ke(S,{select:!0})):p.shiftKey&&b===S&&(p.preventDefault(),n&&ke(C,{select:!0})):b===y&&p.preventDefault()}},[n,r,g.paused]);return h.jsx(M.div,{tabIndex:-1,...i,ref:v,onKeyDown:x})});tn.displayName=ql;function Zl(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ke(r,{select:t}),document.activeElement!==n)return}function Jl(e){const t=ns(e),n=Zr(t,e),r=Zr(t.reverse(),e);return[n,r]}function ns(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Zr(e,t){for(const n of e)if(!Ql(n,{upTo:t}))return n}function Ql(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function eu(e){return e instanceof HTMLInputElement&&"select"in e}function ke(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&eu(e)&&t&&e.select()}}var Jr=tu();function tu(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Qr(e,t),e.unshift(t)},remove(t){var n;e=Qr(e,t),(n=e[0])==null||n.resume()}}}function Qr(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function nu(e){return e.filter(t=>t.tagName!=="A")}var Cn=0;function lr(){c.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??eo()),document.body.insertAdjacentElement("beforeend",e[1]??eo()),Cn++,()=>{Cn===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Cn--}},[])}function eo(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Re=function(){return Re=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Re.apply(this,arguments)};function rs(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function ru(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var jt="right-scroll-bar-position",Ft="width-before-scroll-bar",ou="with-scroll-bars-hidden",su="--removed-body-scroll-bar-size";function En(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function iu(e,t){var n=c.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var au=typeof window<"u"?c.useLayoutEffect:c.useEffect,to=new WeakMap;function cu(e,t){var n=iu(null,function(r){return e.forEach(function(o){return En(o,r)})});return au(function(){var r=to.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||En(a,null)}),s.forEach(function(a){o.has(a)||En(a,i)})}to.set(n,e)},[e]),n}function lu(e){return e}function uu(e,t){t===void 0&&(t=lu);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var d=i;i=[],d.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(d){i.push(d),u()},filter:function(d){return i=i.filter(d),n}}}};return o}function du(e){e===void 0&&(e={});var t=uu(null);return t.options=Re({async:!0,ssr:!1},e),t}var os=function(e){var t=e.sideCar,n=rs(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return c.createElement(r,Re({},n))};os.isSideCarExport=!0;function fu(e,t){return e.useMedium(t),os}var ss=du(),Pn=function(){},nn=c.forwardRef(function(e,t){var n=c.useRef(null),r=c.useState({onScrollCapture:Pn,onWheelCapture:Pn,onTouchMoveCapture:Pn}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,g=e.noIsolation,x=e.inert,p=e.allowPinchZoom,w=e.as,b=w===void 0?"div":w,y=e.gapMode,S=rs(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=v,E=cu([n,t]),_=Re(Re({},S),o);return c.createElement(c.Fragment,null,d&&c.createElement(C,{sideCar:ss,removeScrollBar:u,shards:f,noIsolation:g,inert:x,setCallbacks:s,allowPinchZoom:!!p,lockRef:n,gapMode:y}),i?c.cloneElement(c.Children.only(a),Re(Re({},_),{ref:E})):c.createElement(b,Re({},_,{className:l,ref:E}),a))});nn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};nn.classNames={fullWidth:Ft,zeroRight:jt};var pu=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function vu(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=pu();return t&&e.setAttribute("nonce",t),e}function hu(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function mu(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var gu=function(){var e=0,t=null;return{add:function(n){e==0&&(t=vu())&&(hu(t,n),mu(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},wu=function(){var e=gu();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},is=function(){var e=wu(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},xu={left:0,top:0,right:0,gap:0},Rn=function(e){return parseInt(e||"",10)||0},yu=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Rn(n),Rn(r),Rn(o)]},bu=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return xu;var t=yu(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Su=is(),st="data-scroll-locked",Cu=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(ou,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(st,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(jt,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ft,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(jt," .").concat(jt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Ft," .").concat(Ft,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(st,`] {
    `).concat(su,": ").concat(a,`px;
  }
`)},no=function(){var e=parseInt(document.body.getAttribute(st)||"0",10);return isFinite(e)?e:0},Eu=function(){c.useEffect(function(){return document.body.setAttribute(st,(no()+1).toString()),function(){var e=no()-1;e<=0?document.body.removeAttribute(st):document.body.setAttribute(st,e.toString())}},[])},Pu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Eu();var s=c.useMemo(function(){return bu(o)},[o]);return c.createElement(Su,{styles:Cu(s,!t,o,n?"":"!important")})},$n=!1;if(typeof window<"u")try{var Ot=Object.defineProperty({},"passive",{get:function(){return $n=!0,!0}});window.addEventListener("test",Ot,Ot),window.removeEventListener("test",Ot,Ot)}catch{$n=!1}var tt=$n?{passive:!1}:!1,Ru=function(e){return e.tagName==="TEXTAREA"},as=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ru(e)&&n[t]==="visible")},Tu=function(e){return as(e,"overflowY")},Au=function(e){return as(e,"overflowX")},ro=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=cs(e,r);if(o){var s=ls(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},_u=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Ou=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},cs=function(e,t){return e==="v"?Tu(t):Au(t)},ls=function(e,t){return e==="v"?_u(t):Ou(t)},Du=function(e,t){return e==="h"&&t==="rtl"?-1:1},Iu=function(e,t,n,r,o){var s=Du(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),u=!1,d=i>0,f=0,v=0;do{var g=ls(e,a),x=g[0],p=g[1],w=g[2],b=p-w-s*x;(x||b)&&cs(e,a)&&(f+=b,v+=x),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&(Math.abs(f)<1||!o)||!d&&(Math.abs(v)<1||!o))&&(u=!0),u},Dt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},oo=function(e){return[e.deltaX,e.deltaY]},so=function(e){return e&&"current"in e?e.current:e},Nu=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Mu=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Lu=0,nt=[];function ju(e){var t=c.useRef([]),n=c.useRef([0,0]),r=c.useRef(),o=c.useState(Lu++)[0],s=c.useState(is)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=ru([e.lockRef.current],(e.shards||[]).map(so),!0).filter(Boolean);return p.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=c.useCallback(function(p,w){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!i.current.allowPinchZoom;var b=Dt(p),y=n.current,S="deltaX"in p?p.deltaX:y[0]-b[0],C="deltaY"in p?p.deltaY:y[1]-b[1],E,_=p.target,R=Math.abs(S)>Math.abs(C)?"h":"v";if("touches"in p&&R==="h"&&_.type==="range")return!1;var O=ro(R,_);if(!O)return!0;if(O?E=R:(E=R==="v"?"h":"v",O=ro(R,_)),!O)return!1;if(!r.current&&"changedTouches"in p&&(S||C)&&(r.current=E),!E)return!0;var I=r.current||E;return Iu(I,w,p,I==="h"?S:C,!0)},[]),l=c.useCallback(function(p){var w=p;if(!(!nt.length||nt[nt.length-1]!==s)){var b="deltaY"in w?oo(w):Dt(w),y=t.current.filter(function(E){return E.name===w.type&&(E.target===w.target||w.target===E.shadowParent)&&Nu(E.delta,b)})[0];if(y&&y.should){w.cancelable&&w.preventDefault();return}if(!y){var S=(i.current.shards||[]).map(so).filter(Boolean).filter(function(E){return E.contains(w.target)}),C=S.length>0?a(w,S[0]):!i.current.noIsolation;C&&w.cancelable&&w.preventDefault()}}},[]),u=c.useCallback(function(p,w,b,y){var S={name:p,delta:w,target:b,should:y,shadowParent:Fu(b)};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(C){return C!==S})},1)},[]),d=c.useCallback(function(p){n.current=Dt(p),r.current=void 0},[]),f=c.useCallback(function(p){u(p.type,oo(p),p.target,a(p,e.lockRef.current))},[]),v=c.useCallback(function(p){u(p.type,Dt(p),p.target,a(p,e.lockRef.current))},[]);c.useEffect(function(){return nt.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,tt),document.addEventListener("touchmove",l,tt),document.addEventListener("touchstart",d,tt),function(){nt=nt.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,tt),document.removeEventListener("touchmove",l,tt),document.removeEventListener("touchstart",d,tt)}},[]);var g=e.removeScrollBar,x=e.inert;return c.createElement(c.Fragment,null,x?c.createElement(s,{styles:Mu(o)}):null,g?c.createElement(Pu,{gapMode:e.gapMode}):null)}function Fu(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ku=fu(ss,ju);var rn=c.forwardRef(function(e,t){return c.createElement(nn,Re({},e,{ref:t,sideCar:ku}))});rn.classNames=nn.classNames;var $u=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},rt=new WeakMap,It=new WeakMap,Nt={},Tn=0,us=function(e){return e&&(e.host||us(e.parentNode))},Wu=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=us(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Vu=function(e,t,n,r){var o=Wu(t,Array.isArray(e)?e:[e]);Nt[n]||(Nt[n]=new WeakMap);var s=Nt[n],i=[],a=new Set,l=new Set(o),u=function(f){!f||a.has(f)||(a.add(f),u(f.parentNode))};o.forEach(u);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(v){if(a.has(v))d(v);else try{var g=v.getAttribute(r),x=g!==null&&g!=="false",p=(rt.get(v)||0)+1,w=(s.get(v)||0)+1;rt.set(v,p),s.set(v,w),i.push(v),p===1&&x&&It.set(v,!0),w===1&&v.setAttribute(n,"true"),x||v.setAttribute(r,"true")}catch(b){console.error("aria-hidden: cannot operate on ",v,b)}})};return d(t),a.clear(),Tn++,function(){i.forEach(function(f){var v=rt.get(f)-1,g=s.get(f)-1;rt.set(f,v),s.set(f,g),v||(It.has(f)||f.removeAttribute(r),It.delete(f)),g||f.removeAttribute(n)}),Tn--,Tn||(rt=new WeakMap,rt=new WeakMap,It=new WeakMap,Nt={})}},ur=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=$u(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Vu(r,o,n,"aria-hidden")):function(){return null}},dr="Dialog",[ds,fs]=Ae(dr),[Bu,Ee]=ds(dr),ps=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=c.useRef(null),l=c.useRef(null),[u=!1,d]=$e({prop:r,defaultProp:o,onChange:s});return h.jsx(Bu,{scope:t,triggerRef:a,contentRef:l,contentId:Ie(),titleId:Ie(),descriptionId:Ie(),open:u,onOpenChange:d,onOpenToggle:c.useCallback(()=>d(f=>!f),[d]),modal:i,children:n})};ps.displayName=dr;var vs="DialogTrigger",hs=c.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ee(vs,n),s=U(t,o.triggerRef);return h.jsx(M.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":vr(o.open),...r,ref:s,onClick:D(e.onClick,o.onOpenToggle)})});hs.displayName=vs;var fr="DialogPortal",[Hu,ms]=ds(fr,{forceMount:void 0}),gs=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Ee(fr,t);return h.jsx(Hu,{scope:t,forceMount:n,children:c.Children.map(r,i=>h.jsx(ve,{present:n||s.open,children:h.jsx(bt,{asChild:!0,container:o,children:i})}))})};gs.displayName=fr;var Bt="DialogOverlay",ws=c.forwardRef((e,t)=>{const n=ms(Bt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ee(Bt,e.__scopeDialog);return s.modal?h.jsx(ve,{present:r||s.open,children:h.jsx(Uu,{...o,ref:t})}):null});ws.displayName=Bt;var Uu=c.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ee(Bt,n);return h.jsx(rn,{as:Xe,allowPinchZoom:!0,shards:[o.contentRef],children:h.jsx(M.div,{"data-state":vr(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Ze="DialogContent",xs=c.forwardRef((e,t)=>{const n=ms(Ze,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ee(Ze,e.__scopeDialog);return h.jsx(ve,{present:r||s.open,children:s.modal?h.jsx(Yu,{...o,ref:t}):h.jsx(Ku,{...o,ref:t})})});xs.displayName=Ze;var Yu=c.forwardRef((e,t)=>{const n=Ee(Ze,e.__scopeDialog),r=c.useRef(null),o=U(t,n.contentRef,r);return c.useEffect(()=>{const s=r.current;if(s)return ur(s)},[]),h.jsx(ys,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:D(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:D(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:D(e.onFocusOutside,s=>s.preventDefault())})}),Ku=c.forwardRef((e,t)=>{const n=Ee(Ze,e.__scopeDialog),r=c.useRef(!1),o=c.useRef(!1);return h.jsx(ys,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),ys=c.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=Ee(Ze,n),l=c.useRef(null),u=U(t,l);return lr(),h.jsxs(h.Fragment,{children:[h.jsx(tn,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:h.jsx(dt,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":vr(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),h.jsxs(h.Fragment,{children:[h.jsx(Gu,{titleId:a.titleId}),h.jsx(qu,{contentRef:l,descriptionId:a.descriptionId})]})]})}),pr="DialogTitle",bs=c.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ee(pr,n);return h.jsx(M.h2,{id:o.titleId,...r,ref:t})});bs.displayName=pr;var Ss="DialogDescription",Cs=c.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ee(Ss,n);return h.jsx(M.p,{id:o.descriptionId,...r,ref:t})});Cs.displayName=Ss;var Es="DialogClose",Ps=c.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ee(Es,n);return h.jsx(M.button,{type:"button",...r,ref:t,onClick:D(e.onClick,()=>o.onOpenChange(!1))})});Ps.displayName=Es;function vr(e){return e?"open":"closed"}var Rs="DialogTitleWarning",[zu,Ts]=Ua(Rs,{contentName:Ze,titleName:pr,docsSlug:"dialog"}),Gu=({titleId:e})=>{const t=Ts(Rs),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return c.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Xu="DialogDescriptionWarning",qu=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ts(Xu).contentName}}.`;return c.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Zu=ps,Ju=hs,Qu=gs,ed=ws,td=xs,nd=bs,rd=Cs,As=Ps,_s="AlertDialog",[od,Xf]=Ae(_s,[fs]),Le=fs(),Os=e=>{const{__scopeAlertDialog:t,...n}=e,r=Le(t);return h.jsx(Zu,{...r,...n,modal:!0})};Os.displayName=_s;var sd="AlertDialogTrigger",id=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Le(n);return h.jsx(Ju,{...o,...r,ref:t})});id.displayName=sd;var ad="AlertDialogPortal",Ds=e=>{const{__scopeAlertDialog:t,...n}=e,r=Le(t);return h.jsx(Qu,{...r,...n})};Ds.displayName=ad;var cd="AlertDialogOverlay",Is=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Le(n);return h.jsx(ed,{...o,...r,ref:t})});Is.displayName=cd;var it="AlertDialogContent",[ld,ud]=od(it),Ns=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...o}=e,s=Le(n),i=c.useRef(null),a=U(t,i),l=c.useRef(null);return h.jsx(zu,{contentName:it,titleName:Ms,docsSlug:"alert-dialog",children:h.jsx(ld,{scope:n,cancelRef:l,children:h.jsxs(td,{role:"alertdialog",...s,...o,ref:a,onOpenAutoFocus:D(o.onOpenAutoFocus,u=>{var d;u.preventDefault(),(d=l.current)==null||d.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault(),children:[h.jsx(zn,{children:r}),h.jsx(fd,{contentRef:i})]})})})});Ns.displayName=it;var Ms="AlertDialogTitle",Ls=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Le(n);return h.jsx(nd,{...o,...r,ref:t})});Ls.displayName=Ms;var js="AlertDialogDescription",Fs=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Le(n);return h.jsx(rd,{...o,...r,ref:t})});Fs.displayName=js;var dd="AlertDialogAction",ks=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Le(n);return h.jsx(As,{...o,...r,ref:t})});ks.displayName=dd;var $s="AlertDialogCancel",Ws=c.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=ud($s,n),s=Le(n),i=U(t,o);return h.jsx(As,{...s,...r,ref:i})});Ws.displayName=$s;var fd=({contentRef:e})=>{const t=`\`${it}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${it}\` by passing a \`${js}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${it}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return c.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},qf=Os,Zf=Ds,Jf=Is,Qf=Ns,ep=ks,tp=Ws,np=Ls,rp=Fs;function pd(e,t=[]){let n=[];function r(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...x}=f,p=(v==null?void 0:v[e][l])||a,w=c.useMemo(()=>x,Object.values(x));return h.jsx(p.Provider,{value:w,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||a,x=c.useContext(g);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,vd(o,...t)]}function vd(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var hr="Progress",mr=100,[hd,op]=pd(hr),[md,gd]=hd(hr),Vs=c.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:s=wd,...i}=e;(o||o===0)&&!io(o)&&console.error(xd(`${o}`,"Progress"));const a=io(o)?o:mr;r!==null&&!ao(r,a)&&console.error(yd(`${r}`,"Progress"));const l=ao(r,a)?r:null,u=Ht(l)?s(l,a):void 0;return h.jsx(md,{scope:n,value:l,max:a,children:h.jsx(M.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":Ht(l)?l:void 0,"aria-valuetext":u,role:"progressbar","data-state":Us(l,a),"data-value":l??void 0,"data-max":a,...i,ref:t})})});Vs.displayName=hr;var Bs="ProgressIndicator",Hs=c.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=gd(Bs,n);return h.jsx(M.div,{"data-state":Us(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});Hs.displayName=Bs;function wd(e,t){return`${Math.round(e/t*100)}%`}function Us(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Ht(e){return typeof e=="number"}function io(e){return Ht(e)&&!isNaN(e)&&e>0}function ao(e,t){return Ht(e)&&!isNaN(e)&&e<=t&&e>=0}function xd(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${mr}\`.`}function yd(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${mr} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var sp=Vs,ip=Hs,gr="Popover",[Ys,ap]=Ae(gr,[vt]),Et=vt(),[bd,Ue]=Ys(gr),Ks=e=>{const{__scopePopover:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!1}=e,a=Et(t),l=c.useRef(null),[u,d]=c.useState(!1),[f=!1,v]=$e({prop:r,defaultProp:o,onChange:s});return h.jsx(Go,{...a,children:h.jsx(bd,{scope:t,contentId:Ie(),triggerRef:l,open:f,onOpenChange:v,onOpenToggle:c.useCallback(()=>v(g=>!g),[v]),hasCustomAnchor:u,onCustomAnchorAdd:c.useCallback(()=>d(!0),[]),onCustomAnchorRemove:c.useCallback(()=>d(!1),[]),modal:i,children:n})})};Ks.displayName=gr;var zs="PopoverAnchor",Sd=c.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Ue(zs,n),s=Et(n),{onCustomAnchorAdd:i,onCustomAnchorRemove:a}=o;return c.useEffect(()=>(i(),()=>a()),[i,a]),h.jsx(Jt,{...s,...r,ref:t})});Sd.displayName=zs;var Gs="PopoverTrigger",Xs=c.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Ue(Gs,n),s=Et(n),i=U(t,o.triggerRef),a=h.jsx(M.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ei(o.open),...r,ref:i,onClick:D(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?a:h.jsx(Jt,{asChild:!0,...s,children:a})});Xs.displayName=Gs;var wr="PopoverPortal",[Cd,Ed]=Ys(wr,{forceMount:void 0}),qs=e=>{const{__scopePopover:t,forceMount:n,children:r,container:o}=e,s=Ue(wr,t);return h.jsx(Cd,{scope:t,forceMount:n,children:h.jsx(ve,{present:n||s.open,children:h.jsx(bt,{asChild:!0,container:o,children:r})})})};qs.displayName=wr;var lt="PopoverContent",Zs=c.forwardRef((e,t)=>{const n=Ed(lt,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,s=Ue(lt,e.__scopePopover);return h.jsx(ve,{present:r||s.open,children:s.modal?h.jsx(Pd,{...o,ref:t}):h.jsx(Rd,{...o,ref:t})})});Zs.displayName=lt;var Pd=c.forwardRef((e,t)=>{const n=Ue(lt,e.__scopePopover),r=c.useRef(null),o=U(t,r),s=c.useRef(!1);return c.useEffect(()=>{const i=r.current;if(i)return ur(i)},[]),h.jsx(rn,{as:Xe,allowPinchZoom:!0,children:h.jsx(Js,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:D(e.onCloseAutoFocus,i=>{var a;i.preventDefault(),s.current||(a=n.triggerRef.current)==null||a.focus()}),onPointerDownOutside:D(e.onPointerDownOutside,i=>{const a=i.detail.originalEvent,l=a.button===0&&a.ctrlKey===!0,u=a.button===2||l;s.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:D(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1})})})}),Rd=c.forwardRef((e,t)=>{const n=Ue(lt,e.__scopePopover),r=c.useRef(!1),o=c.useRef(!1);return h.jsx(Js,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Js=c.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,disableOutsidePointerEvents:i,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:d,...f}=e,v=Ue(lt,n),g=Et(n);return lr(),h.jsx(tn,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:h.jsx(dt,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:d,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>v.onOpenChange(!1),children:h.jsx(ir,{"data-state":ei(v.open),role:"dialog",id:v.contentId,...g,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Qs="PopoverClose",Td=c.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Ue(Qs,n);return h.jsx(M.button,{type:"button",...r,ref:t,onClick:D(e.onClick,()=>o.onOpenChange(!1))})});Td.displayName=Qs;var Ad="PopoverArrow",_d=c.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Et(n);return h.jsx(ar,{...o,...r,ref:t})});_d.displayName=Ad;function ei(e){return e?"open":"closed"}var cp=Ks,lp=Xs,up=qs,dp=Zs;function Wn(e,[t,n]){return Math.min(n,Math.max(t,e))}var Od=c.createContext(void 0);function on(e){const t=c.useContext(Od);return e||t||"ltr"}function ti(e){const t=c.useRef({value:e,previous:e});return c.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Dd=[" ","Enter","ArrowUp","ArrowDown"],Id=[" ","Enter"],Pt="Select",[sn,an,Nd]=Gn(Pt),[ht,fp]=Ae(Pt,[Nd,vt]),cn=vt(),[Md,Ye]=ht(Pt),[Ld,jd]=ht(Pt),ni=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:a,onValueChange:l,dir:u,name:d,autoComplete:f,disabled:v,required:g,form:x}=e,p=cn(t),[w,b]=c.useState(null),[y,S]=c.useState(null),[C,E]=c.useState(!1),_=on(u),[R=!1,O]=$e({prop:r,defaultProp:o,onChange:s}),[I,L]=$e({prop:i,defaultProp:a,onChange:l}),W=c.useRef(null),B=w?x||!!w.closest("form"):!0,[k,T]=c.useState(new Set),V=Array.from(k).map(N=>N.props.value).join(";");return h.jsx(Go,{...p,children:h.jsxs(Md,{required:g,scope:t,trigger:w,onTriggerChange:b,valueNode:y,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:E,contentId:Ie(),value:I,onValueChange:L,open:R,onOpenChange:O,dir:_,triggerPointerDownPosRef:W,disabled:v,children:[h.jsx(sn.Provider,{scope:t,children:h.jsx(Ld,{scope:e.__scopeSelect,onNativeOptionAdd:c.useCallback(N=>{T(H=>new Set(H).add(N))},[]),onNativeOptionRemove:c.useCallback(N=>{T(H=>{const j=new Set(H);return j.delete(N),j})},[]),children:n})}),B?h.jsxs(Ti,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:f,value:I,onChange:N=>L(N.target.value),disabled:v,form:x,children:[I===void 0?h.jsx("option",{value:""}):null,Array.from(k)]},V):null]})})};ni.displayName=Pt;var ri="SelectTrigger",oi=c.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=cn(n),i=Ye(ri,n),a=i.disabled||r,l=U(t,i.onTriggerChange),u=an(n),d=c.useRef("touch"),[f,v,g]=Ai(p=>{const w=u().filter(S=>!S.disabled),b=w.find(S=>S.value===i.value),y=_i(w,p,b);y!==void 0&&i.onValueChange(y.value)}),x=p=>{a||(i.onOpenChange(!0),g()),p&&(i.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return h.jsx(Jt,{asChild:!0,...s,children:h.jsx(M.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":Ri(i.value)?"":void 0,...o,ref:l,onClick:D(o.onClick,p=>{p.currentTarget.focus(),d.current!=="mouse"&&x(p)}),onPointerDown:D(o.onPointerDown,p=>{d.current=p.pointerType;const w=p.target;w.hasPointerCapture(p.pointerId)&&w.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(x(p),p.preventDefault())}),onKeyDown:D(o.onKeyDown,p=>{const w=f.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&v(p.key),!(w&&p.key===" ")&&Dd.includes(p.key)&&(x(),p.preventDefault())})})})});oi.displayName=ri;var si="SelectValue",ii=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:i="",...a}=e,l=Ye(si,n),{onValueNodeHasChildrenChange:u}=l,d=s!==void 0,f=U(t,l.onValueNodeChange);return se(()=>{u(d)},[u,d]),h.jsx(M.span,{...a,ref:f,style:{pointerEvents:"none"},children:Ri(l.value)?h.jsx(h.Fragment,{children:i}):s})});ii.displayName=si;var Fd="SelectIcon",ai=c.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return h.jsx(M.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});ai.displayName=Fd;var kd="SelectPortal",ci=e=>h.jsx(bt,{asChild:!0,...e});ci.displayName=kd;var Je="SelectContent",li=c.forwardRef((e,t)=>{const n=Ye(Je,e.__scopeSelect),[r,o]=c.useState();if(se(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?yt.createPortal(h.jsx(ui,{scope:e.__scopeSelect,children:h.jsx(sn.Slot,{scope:e.__scopeSelect,children:h.jsx("div",{children:e.children})})}),s):null}return h.jsx(di,{...e,ref:t})});li.displayName=Je;var be=10,[ui,Ke]=ht(Je),$d="SelectContentImpl",di=c.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:a,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:x,hideWhenDetached:p,avoidCollisions:w,...b}=e,y=Ye(Je,n),[S,C]=c.useState(null),[E,_]=c.useState(null),R=U(t,F=>C(F)),[O,I]=c.useState(null),[L,W]=c.useState(null),B=an(n),[k,T]=c.useState(!1),V=c.useRef(!1);c.useEffect(()=>{if(S)return ur(S)},[S]),lr();const N=c.useCallback(F=>{const[q,...ne]=B().map(X=>X.ref.current),[G]=ne.slice(-1),Z=document.activeElement;for(const X of F)if(X===Z||(X==null||X.scrollIntoView({block:"nearest"}),X===q&&E&&(E.scrollTop=0),X===G&&E&&(E.scrollTop=E.scrollHeight),X==null||X.focus(),document.activeElement!==Z))return},[B,E]),H=c.useCallback(()=>N([O,S]),[N,O,S]);c.useEffect(()=>{k&&H()},[k,H]);const{onOpenChange:j,triggerPointerDownPosRef:z}=y;c.useEffect(()=>{if(S){let F={x:0,y:0};const q=G=>{var Z,X;F={x:Math.abs(Math.round(G.pageX)-(((Z=z.current)==null?void 0:Z.x)??0)),y:Math.abs(Math.round(G.pageY)-(((X=z.current)==null?void 0:X.y)??0))}},ne=G=>{F.x<=10&&F.y<=10?G.preventDefault():S.contains(G.target)||j(!1),document.removeEventListener("pointermove",q),z.current=null};return z.current!==null&&(document.addEventListener("pointermove",q),document.addEventListener("pointerup",ne,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",q),document.removeEventListener("pointerup",ne,{capture:!0})}}},[S,j,z]),c.useEffect(()=>{const F=()=>j(!1);return window.addEventListener("blur",F),window.addEventListener("resize",F),()=>{window.removeEventListener("blur",F),window.removeEventListener("resize",F)}},[j]);const[oe,ce]=Ai(F=>{const q=B().filter(Z=>!Z.disabled),ne=q.find(Z=>Z.ref.current===document.activeElement),G=_i(q,F,ne);G&&setTimeout(()=>G.ref.current.focus())}),he=c.useCallback((F,q,ne)=>{const G=!V.current&&!ne;(y.value!==void 0&&y.value===q||G)&&(I(F),G&&(V.current=!0))},[y.value]),ue=c.useCallback(()=>S==null?void 0:S.focus(),[S]),de=c.useCallback((F,q,ne)=>{const G=!V.current&&!ne;(y.value!==void 0&&y.value===q||G)&&W(F)},[y.value]),je=r==="popper"?Vn:fi,xe=je===Vn?{side:a,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:x,hideWhenDetached:p,avoidCollisions:w}:{};return h.jsx(ui,{scope:n,content:S,viewport:E,onViewportChange:_,itemRefCallback:he,selectedItem:O,onItemLeave:ue,itemTextRefCallback:de,focusSelectedItem:H,selectedItemText:L,position:r,isPositioned:k,searchRef:oe,children:h.jsx(rn,{as:Xe,allowPinchZoom:!0,children:h.jsx(tn,{asChild:!0,trapped:y.open,onMountAutoFocus:F=>{F.preventDefault()},onUnmountAutoFocus:D(o,F=>{var q;(q=y.trigger)==null||q.focus({preventScroll:!0}),F.preventDefault()}),children:h.jsx(dt,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:F=>F.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:h.jsx(je,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:F=>F.preventDefault(),...b,...xe,onPlaced:()=>T(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:D(b.onKeyDown,F=>{const q=F.ctrlKey||F.altKey||F.metaKey;if(F.key==="Tab"&&F.preventDefault(),!q&&F.key.length===1&&ce(F.key),["ArrowUp","ArrowDown","Home","End"].includes(F.key)){let G=B().filter(Z=>!Z.disabled).map(Z=>Z.ref.current);if(["ArrowUp","End"].includes(F.key)&&(G=G.slice().reverse()),["ArrowUp","ArrowDown"].includes(F.key)){const Z=F.target,X=G.indexOf(Z);G=G.slice(X+1)}setTimeout(()=>N(G)),F.preventDefault()}})})})})})})});di.displayName=$d;var Wd="SelectItemAlignedPosition",fi=c.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=Ye(Je,n),i=Ke(Je,n),[a,l]=c.useState(null),[u,d]=c.useState(null),f=U(t,R=>d(R)),v=an(n),g=c.useRef(!1),x=c.useRef(!0),{viewport:p,selectedItem:w,selectedItemText:b,focusSelectedItem:y}=i,S=c.useCallback(()=>{if(s.trigger&&s.valueNode&&a&&u&&p&&w&&b){const R=s.trigger.getBoundingClientRect(),O=u.getBoundingClientRect(),I=s.valueNode.getBoundingClientRect(),L=b.getBoundingClientRect();if(s.dir!=="rtl"){const Z=L.left-O.left,X=I.left-Z,ie=R.left-X,me=R.width+ie,Pe=Math.max(me,O.width),ye=window.innerWidth-be,Fe=Wn(X,[be,Math.max(be,ye-Pe)]);a.style.minWidth=me+"px",a.style.left=Fe+"px"}else{const Z=O.right-L.right,X=window.innerWidth-I.right-Z,ie=window.innerWidth-R.right-X,me=R.width+ie,Pe=Math.max(me,O.width),ye=window.innerWidth-be,Fe=Wn(X,[be,Math.max(be,ye-Pe)]);a.style.minWidth=me+"px",a.style.right=Fe+"px"}const W=v(),B=window.innerHeight-be*2,k=p.scrollHeight,T=window.getComputedStyle(u),V=parseInt(T.borderTopWidth,10),N=parseInt(T.paddingTop,10),H=parseInt(T.borderBottomWidth,10),j=parseInt(T.paddingBottom,10),z=V+N+k+j+H,oe=Math.min(w.offsetHeight*5,z),ce=window.getComputedStyle(p),he=parseInt(ce.paddingTop,10),ue=parseInt(ce.paddingBottom,10),de=R.top+R.height/2-be,je=B-de,xe=w.offsetHeight/2,F=w.offsetTop+xe,q=V+N+F,ne=z-q;if(q<=de){const Z=W.length>0&&w===W[W.length-1].ref.current;a.style.bottom="0px";const X=u.clientHeight-p.offsetTop-p.offsetHeight,ie=Math.max(je,xe+(Z?ue:0)+X+H),me=q+ie;a.style.height=me+"px"}else{const Z=W.length>0&&w===W[0].ref.current;a.style.top="0px";const ie=Math.max(de,V+p.offsetTop+(Z?he:0)+xe)+ne;a.style.height=ie+"px",p.scrollTop=q-de+p.offsetTop}a.style.margin=`${be}px 0`,a.style.minHeight=oe+"px",a.style.maxHeight=B+"px",r==null||r(),requestAnimationFrame(()=>g.current=!0)}},[v,s.trigger,s.valueNode,a,u,p,w,b,s.dir,r]);se(()=>S(),[S]);const[C,E]=c.useState();se(()=>{u&&E(window.getComputedStyle(u).zIndex)},[u]);const _=c.useCallback(R=>{R&&x.current===!0&&(S(),y==null||y(),x.current=!1)},[S,y]);return h.jsx(Bd,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:g,onScrollButtonChange:_,children:h.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:h.jsx(M.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});fi.displayName=Wd;var Vd="SelectPopperPosition",Vn=c.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=be,...s}=e,i=cn(n);return h.jsx(ir,{...i,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Vn.displayName=Vd;var[Bd,xr]=ht(Je,{}),Bn="SelectViewport",pi=c.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=Ke(Bn,n),i=xr(Bn,n),a=U(t,s.onViewportChange),l=c.useRef(0);return h.jsxs(h.Fragment,{children:[h.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),h.jsx(sn.Slot,{scope:n,children:h.jsx(M.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:D(o.onScroll,u=>{const d=u.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:v}=i;if(v!=null&&v.current&&f){const g=Math.abs(l.current-d.scrollTop);if(g>0){const x=window.innerHeight-be*2,p=parseFloat(f.style.minHeight),w=parseFloat(f.style.height),b=Math.max(p,w);if(b<x){const y=b+g,S=Math.min(x,y),C=y-S;f.style.height=S+"px",f.style.bottom==="0px"&&(d.scrollTop=C>0?C:0,f.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});pi.displayName=Bn;var vi="SelectGroup",[Hd,Ud]=ht(vi),Yd=c.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Ie();return h.jsx(Hd,{scope:n,id:o,children:h.jsx(M.div,{role:"group","aria-labelledby":o,...r,ref:t})})});Yd.displayName=vi;var hi="SelectLabel",mi=c.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Ud(hi,n);return h.jsx(M.div,{id:o.id,...r,ref:t})});mi.displayName=hi;var Ut="SelectItem",[Kd,gi]=ht(Ut),wi=c.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...i}=e,a=Ye(Ut,n),l=Ke(Ut,n),u=a.value===r,[d,f]=c.useState(s??""),[v,g]=c.useState(!1),x=U(t,y=>{var S;return(S=l.itemRefCallback)==null?void 0:S.call(l,y,r,o)}),p=Ie(),w=c.useRef("touch"),b=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return h.jsx(Kd,{scope:n,value:r,disabled:o,textId:p,isSelected:u,onItemTextChange:c.useCallback(y=>{f(S=>S||((y==null?void 0:y.textContent)??"").trim())},[]),children:h.jsx(sn.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:h.jsx(M.div,{role:"option","aria-labelledby":p,"data-highlighted":v?"":void 0,"aria-selected":u&&v,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:x,onFocus:D(i.onFocus,()=>g(!0)),onBlur:D(i.onBlur,()=>g(!1)),onClick:D(i.onClick,()=>{w.current!=="mouse"&&b()}),onPointerUp:D(i.onPointerUp,()=>{w.current==="mouse"&&b()}),onPointerDown:D(i.onPointerDown,y=>{w.current=y.pointerType}),onPointerMove:D(i.onPointerMove,y=>{var S;w.current=y.pointerType,o?(S=l.onItemLeave)==null||S.call(l):w.current==="mouse"&&y.currentTarget.focus({preventScroll:!0})}),onPointerLeave:D(i.onPointerLeave,y=>{var S;y.currentTarget===document.activeElement&&((S=l.onItemLeave)==null||S.call(l))}),onKeyDown:D(i.onKeyDown,y=>{var C;((C=l.searchRef)==null?void 0:C.current)!==""&&y.key===" "||(Id.includes(y.key)&&b(),y.key===" "&&y.preventDefault())})})})})});wi.displayName=Ut;var gt="SelectItemText",xi=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,i=Ye(gt,n),a=Ke(gt,n),l=gi(gt,n),u=jd(gt,n),[d,f]=c.useState(null),v=U(t,b=>f(b),l.onItemTextChange,b=>{var y;return(y=a.itemTextRefCallback)==null?void 0:y.call(a,b,l.value,l.disabled)}),g=d==null?void 0:d.textContent,x=c.useMemo(()=>h.jsx("option",{value:l.value,disabled:l.disabled,children:g},l.value),[l.disabled,l.value,g]),{onNativeOptionAdd:p,onNativeOptionRemove:w}=u;return se(()=>(p(x),()=>w(x)),[p,w,x]),h.jsxs(h.Fragment,{children:[h.jsx(M.span,{id:l.textId,...s,ref:v}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?yt.createPortal(s.children,i.valueNode):null]})});xi.displayName=gt;var yi="SelectItemIndicator",bi=c.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return gi(yi,n).isSelected?h.jsx(M.span,{"aria-hidden":!0,...r,ref:t}):null});bi.displayName=yi;var Hn="SelectScrollUpButton",Si=c.forwardRef((e,t)=>{const n=Ke(Hn,e.__scopeSelect),r=xr(Hn,e.__scopeSelect),[o,s]=c.useState(!1),i=U(t,r.onScrollButtonChange);return se(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?h.jsx(Ei,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop-l.offsetHeight)}}):null});Si.displayName=Hn;var Un="SelectScrollDownButton",Ci=c.forwardRef((e,t)=>{const n=Ke(Un,e.__scopeSelect),r=xr(Un,e.__scopeSelect),[o,s]=c.useState(!1),i=U(t,r.onScrollButtonChange);return se(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<u;s(d)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?h.jsx(Ei,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop+l.offsetHeight)}}):null});Ci.displayName=Un;var Ei=c.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=Ke("SelectScrollButton",n),i=c.useRef(null),a=an(n),l=c.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return c.useEffect(()=>()=>l(),[l]),se(()=>{var d;const u=a().find(f=>f.ref.current===document.activeElement);(d=u==null?void 0:u.ref.current)==null||d.scrollIntoView({block:"nearest"})},[a]),h.jsx(M.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:D(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:D(o.onPointerMove,()=>{var u;(u=s.onItemLeave)==null||u.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:D(o.onPointerLeave,()=>{l()})})}),zd="SelectSeparator",Pi=c.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return h.jsx(M.div,{"aria-hidden":!0,...r,ref:t})});Pi.displayName=zd;var Yn="SelectArrow",Gd=c.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=cn(n),s=Ye(Yn,n),i=Ke(Yn,n);return s.open&&i.position==="popper"?h.jsx(ar,{...o,...r,ref:t}):null});Gd.displayName=Yn;function Ri(e){return e===""||e===void 0}var Ti=c.forwardRef((e,t)=>{const{value:n,...r}=e,o=c.useRef(null),s=U(t,o),i=ti(n);return c.useEffect(()=>{const a=o.current,l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==n&&d){const f=new Event("change",{bubbles:!0});d.call(a,n),a.dispatchEvent(f)}},[i,n]),h.jsx(St,{asChild:!0,children:h.jsx("select",{...r,ref:s,defaultValue:n})})});Ti.displayName="BubbleSelect";function Ai(e){const t=ee(e),n=c.useRef(""),r=c.useRef(0),o=c.useCallback(i=>{const a=n.current+i;t(a),function l(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(a)},[t]),s=c.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function _i(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=Xd(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function Xd(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var pp=ni,vp=oi,hp=ii,mp=ai,gp=ci,wp=li,xp=pi,yp=mi,bp=wi,Sp=xi,Cp=bi,Ep=Si,Pp=Ci,Rp=Pi,qd="Label",Oi=c.forwardRef((e,t)=>h.jsx(M.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Oi.displayName=qd;var Tp=Oi,yr="Switch",[Zd,Ap]=Ae(yr),[Jd,Qd]=Zd(yr),Di=c.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:o,defaultChecked:s,required:i,disabled:a,value:l="on",onCheckedChange:u,form:d,...f}=e,[v,g]=c.useState(null),x=U(t,S=>g(S)),p=c.useRef(!1),w=v?d||!!v.closest("form"):!0,[b=!1,y]=$e({prop:o,defaultProp:s,onChange:u});return h.jsxs(Jd,{scope:n,checked:b,disabled:a,children:[h.jsx(M.button,{type:"button",role:"switch","aria-checked":b,"aria-required":i,"data-state":Mi(b),"data-disabled":a?"":void 0,disabled:a,value:l,...f,ref:x,onClick:D(e.onClick,S=>{y(C=>!C),w&&(p.current=S.isPropagationStopped(),p.current||S.stopPropagation())})}),w&&h.jsx(ef,{control:v,bubbles:!p.current,name:r,value:l,checked:b,required:i,disabled:a,form:d,style:{transform:"translateX(-100%)"}})]})});Di.displayName=yr;var Ii="SwitchThumb",Ni=c.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=Qd(Ii,n);return h.jsx(M.span,{"data-state":Mi(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});Ni.displayName=Ii;var ef=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,s=c.useRef(null),i=ti(n),a=ko(t);return c.useEffect(()=>{const l=s.current,u=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(u,"checked").set;if(i!==n&&f){const v=new Event("click",{bubbles:r});f.call(l,n),l.dispatchEvent(v)}},[i,n,r]),h.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:s,style:{...e.style,...a,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Mi(e){return e?"checked":"unchecked"}var _p=Di,Op=Ni;function tf(e,t){return c.useReducer((n,r)=>t[n][r]??n,e)}var br="ScrollArea",[Li,Dp]=Ae(br),[nf,we]=Li(br),ji=c.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:s=600,...i}=e,[a,l]=c.useState(null),[u,d]=c.useState(null),[f,v]=c.useState(null),[g,x]=c.useState(null),[p,w]=c.useState(null),[b,y]=c.useState(0),[S,C]=c.useState(0),[E,_]=c.useState(!1),[R,O]=c.useState(!1),I=U(t,W=>l(W)),L=on(o);return h.jsx(nf,{scope:n,type:r,dir:L,scrollHideDelay:s,scrollArea:a,viewport:u,onViewportChange:d,content:f,onContentChange:v,scrollbarX:g,onScrollbarXChange:x,scrollbarXEnabled:E,onScrollbarXEnabledChange:_,scrollbarY:p,onScrollbarYChange:w,scrollbarYEnabled:R,onScrollbarYEnabledChange:O,onCornerWidthChange:y,onCornerHeightChange:C,children:h.jsx(M.div,{dir:L,...i,ref:I,style:{position:"relative","--radix-scroll-area-corner-width":b+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});ji.displayName=br;var Fi="ScrollAreaViewport",ki=c.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:r,asChild:o,nonce:s,...i}=e,a=we(Fi,n),l=c.useRef(null),u=U(t,l,a.onViewportChange);return h.jsxs(h.Fragment,{children:[h.jsx("style",{dangerouslySetInnerHTML:{__html:`
[data-radix-scroll-area-viewport] {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}
[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: none;
}
:where([data-radix-scroll-area-viewport]) {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
:where([data-radix-scroll-area-content]) {
  flex-grow: 1;
}
`},nonce:s}),h.jsx(M.div,{"data-radix-scroll-area-viewport":"",...i,asChild:o,ref:u,style:{overflowX:a.scrollbarXEnabled?"scroll":"hidden",overflowY:a.scrollbarYEnabled?"scroll":"hidden",...e.style},children:hf({asChild:o,children:r},d=>h.jsx("div",{"data-radix-scroll-area-content":"",ref:a.onContentChange,style:{minWidth:a.scrollbarXEnabled?"fit-content":void 0},children:d}))})]})});ki.displayName=Fi;var Oe="ScrollAreaScrollbar",rf=c.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=we(Oe,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:i}=o,a=e.orientation==="horizontal";return c.useEffect(()=>(a?s(!0):i(!0),()=>{a?s(!1):i(!1)}),[a,s,i]),o.type==="hover"?h.jsx(of,{...r,ref:t,forceMount:n}):o.type==="scroll"?h.jsx(sf,{...r,ref:t,forceMount:n}):o.type==="auto"?h.jsx($i,{...r,ref:t,forceMount:n}):o.type==="always"?h.jsx(Sr,{...r,ref:t}):null});rf.displayName=Oe;var of=c.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=we(Oe,e.__scopeScrollArea),[s,i]=c.useState(!1);return c.useEffect(()=>{const a=o.scrollArea;let l=0;if(a){const u=()=>{window.clearTimeout(l),i(!0)},d=()=>{l=window.setTimeout(()=>i(!1),o.scrollHideDelay)};return a.addEventListener("pointerenter",u),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(l),a.removeEventListener("pointerenter",u),a.removeEventListener("pointerleave",d)}}},[o.scrollArea,o.scrollHideDelay]),h.jsx(ve,{present:n||s,children:h.jsx($i,{"data-state":s?"visible":"hidden",...r,ref:t})})}),sf=c.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=we(Oe,e.__scopeScrollArea),s=e.orientation==="horizontal",i=un(()=>l("SCROLL_END"),100),[a,l]=tf("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return c.useEffect(()=>{if(a==="idle"){const u=window.setTimeout(()=>l("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(u)}},[a,o.scrollHideDelay,l]),c.useEffect(()=>{const u=o.viewport,d=s?"scrollLeft":"scrollTop";if(u){let f=u[d];const v=()=>{const g=u[d];f!==g&&(l("SCROLL"),i()),f=g};return u.addEventListener("scroll",v),()=>u.removeEventListener("scroll",v)}},[o.viewport,s,l,i]),h.jsx(ve,{present:n||a!=="hidden",children:h.jsx(Sr,{"data-state":a==="hidden"?"hidden":"visible",...r,ref:t,onPointerEnter:D(e.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:D(e.onPointerLeave,()=>l("POINTER_LEAVE"))})})}),$i=c.forwardRef((e,t)=>{const n=we(Oe,e.__scopeScrollArea),{forceMount:r,...o}=e,[s,i]=c.useState(!1),a=e.orientation==="horizontal",l=un(()=>{if(n.viewport){const u=n.viewport.offsetWidth<n.viewport.scrollWidth,d=n.viewport.offsetHeight<n.viewport.scrollHeight;i(a?u:d)}},10);return ut(n.viewport,l),ut(n.content,l),h.jsx(ve,{present:r||s,children:h.jsx(Sr,{"data-state":s?"visible":"hidden",...o,ref:t})})}),Sr=c.forwardRef((e,t)=>{const{orientation:n="vertical",...r}=e,o=we(Oe,e.__scopeScrollArea),s=c.useRef(null),i=c.useRef(0),[a,l]=c.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=Hi(a.viewport,a.content),d={...r,sizes:a,onSizesChange:l,hasThumb:u>0&&u<1,onThumbChange:v=>s.current=v,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:v=>i.current=v};function f(v,g){return pf(v,i.current,a,g)}return n==="horizontal"?h.jsx(af,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&s.current){const v=o.viewport.scrollLeft,g=co(v,a,o.dir);s.current.style.transform=`translate3d(${g}px, 0, 0)`}},onWheelScroll:v=>{o.viewport&&(o.viewport.scrollLeft=v)},onDragScroll:v=>{o.viewport&&(o.viewport.scrollLeft=f(v,o.dir))}}):n==="vertical"?h.jsx(cf,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&s.current){const v=o.viewport.scrollTop,g=co(v,a);s.current.style.transform=`translate3d(0, ${g}px, 0)`}},onWheelScroll:v=>{o.viewport&&(o.viewport.scrollTop=v)},onDragScroll:v=>{o.viewport&&(o.viewport.scrollTop=f(v))}}):null}),af=c.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,s=we(Oe,e.__scopeScrollArea),[i,a]=c.useState(),l=c.useRef(null),u=U(t,l,s.onScrollbarXChange);return c.useEffect(()=>{l.current&&a(getComputedStyle(l.current))},[l]),h.jsx(Vi,{"data-orientation":"horizontal",...o,ref:u,sizes:n,style:{bottom:0,left:s.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:s.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ln(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,f)=>{if(s.viewport){const v=s.viewport.scrollLeft+d.deltaX;e.onWheelScroll(v),Yi(v,f)&&d.preventDefault()}},onResize:()=>{l.current&&s.viewport&&i&&r({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:Kt(i.paddingLeft),paddingEnd:Kt(i.paddingRight)}})}})}),cf=c.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,s=we(Oe,e.__scopeScrollArea),[i,a]=c.useState(),l=c.useRef(null),u=U(t,l,s.onScrollbarYChange);return c.useEffect(()=>{l.current&&a(getComputedStyle(l.current))},[l]),h.jsx(Vi,{"data-orientation":"vertical",...o,ref:u,sizes:n,style:{top:0,right:s.dir==="ltr"?0:void 0,left:s.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ln(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,f)=>{if(s.viewport){const v=s.viewport.scrollTop+d.deltaY;e.onWheelScroll(v),Yi(v,f)&&d.preventDefault()}},onResize:()=>{l.current&&s.viewport&&i&&r({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:Kt(i.paddingTop),paddingEnd:Kt(i.paddingBottom)}})}})}),[lf,Wi]=Li(Oe),Vi=c.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:s,onThumbPointerUp:i,onThumbPointerDown:a,onThumbPositionChange:l,onDragScroll:u,onWheelScroll:d,onResize:f,...v}=e,g=we(Oe,n),[x,p]=c.useState(null),w=U(t,I=>p(I)),b=c.useRef(null),y=c.useRef(""),S=g.viewport,C=r.content-r.viewport,E=ee(d),_=ee(l),R=un(f,10);function O(I){if(b.current){const L=I.clientX-b.current.left,W=I.clientY-b.current.top;u({x:L,y:W})}}return c.useEffect(()=>{const I=L=>{const W=L.target;(x==null?void 0:x.contains(W))&&E(L,C)};return document.addEventListener("wheel",I,{passive:!1}),()=>document.removeEventListener("wheel",I,{passive:!1})},[S,x,C,E]),c.useEffect(_,[r,_]),ut(x,R),ut(g.content,R),h.jsx(lf,{scope:n,scrollbar:x,hasThumb:o,onThumbChange:ee(s),onThumbPointerUp:ee(i),onThumbPositionChange:_,onThumbPointerDown:ee(a),children:h.jsx(M.div,{...v,ref:w,style:{position:"absolute",...v.style},onPointerDown:D(e.onPointerDown,I=>{I.button===0&&(I.target.setPointerCapture(I.pointerId),b.current=x.getBoundingClientRect(),y.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",g.viewport&&(g.viewport.style.scrollBehavior="auto"),O(I))}),onPointerMove:D(e.onPointerMove,O),onPointerUp:D(e.onPointerUp,I=>{const L=I.target;L.hasPointerCapture(I.pointerId)&&L.releasePointerCapture(I.pointerId),document.body.style.webkitUserSelect=y.current,g.viewport&&(g.viewport.style.scrollBehavior=""),b.current=null})})})}),Yt="ScrollAreaThumb",uf=c.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Wi(Yt,e.__scopeScrollArea);return h.jsx(ve,{present:n||o.hasThumb,children:h.jsx(df,{ref:t,...r})})}),df=c.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:r,...o}=e,s=we(Yt,n),i=Wi(Yt,n),{onThumbPositionChange:a}=i,l=U(t,f=>i.onThumbChange(f)),u=c.useRef(),d=un(()=>{u.current&&(u.current(),u.current=void 0)},100);return c.useEffect(()=>{const f=s.viewport;if(f){const v=()=>{if(d(),!u.current){const g=vf(f,a);u.current=g,a()}};return a(),f.addEventListener("scroll",v),()=>f.removeEventListener("scroll",v)}},[s.viewport,d,a]),h.jsx(M.div,{"data-state":i.hasThumb?"visible":"hidden",...o,ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:D(e.onPointerDownCapture,f=>{const g=f.target.getBoundingClientRect(),x=f.clientX-g.left,p=f.clientY-g.top;i.onThumbPointerDown({x,y:p})}),onPointerUp:D(e.onPointerUp,i.onThumbPointerUp)})});uf.displayName=Yt;var Cr="ScrollAreaCorner",Bi=c.forwardRef((e,t)=>{const n=we(Cr,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?h.jsx(ff,{...e,ref:t}):null});Bi.displayName=Cr;var ff=c.forwardRef((e,t)=>{const{__scopeScrollArea:n,...r}=e,o=we(Cr,n),[s,i]=c.useState(0),[a,l]=c.useState(0),u=!!(s&&a);return ut(o.scrollbarX,()=>{var f;const d=((f=o.scrollbarX)==null?void 0:f.offsetHeight)||0;o.onCornerHeightChange(d),l(d)}),ut(o.scrollbarY,()=>{var f;const d=((f=o.scrollbarY)==null?void 0:f.offsetWidth)||0;o.onCornerWidthChange(d),i(d)}),u?h.jsx(M.div,{...r,ref:t,style:{width:s,height:a,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function Kt(e){return e?parseInt(e,10):0}function Hi(e,t){const n=e/t;return isNaN(n)?0:n}function ln(e){const t=Hi(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function pf(e,t,n,r="ltr"){const o=ln(n),s=o/2,i=t||s,a=o-i,l=n.scrollbar.paddingStart+i,u=n.scrollbar.size-n.scrollbar.paddingEnd-a,d=n.content-n.viewport,f=r==="ltr"?[0,d]:[d*-1,0];return Ui([l,u],f)(e)}function co(e,t,n="ltr"){const r=ln(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,s=t.scrollbar.size-o,i=t.content-t.viewport,a=s-r,l=n==="ltr"?[0,i]:[i*-1,0],u=Wn(e,l);return Ui([0,i],[0,a])(u)}function Ui(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function Yi(e,t){return e>0&&e<t}var vf=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function o(){const s={left:e.scrollLeft,top:e.scrollTop},i=n.left!==s.left,a=n.top!==s.top;(i||a)&&t(),n=s,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function un(e,t){const n=ee(e),r=c.useRef(0);return c.useEffect(()=>()=>window.clearTimeout(r.current),[]),c.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function ut(e,t){const n=ee(t);se(()=>{let r=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,n])}function hf(e,t){const{asChild:n,children:r}=e;if(!n)return typeof t=="function"?t(r):t;const o=c.Children.only(r);return c.cloneElement(o,{children:typeof t=="function"?t(o.props.children):t})}var Ip=ji,Np=ki,Mp=Bi,Er="Avatar",[mf,Lp]=Ae(Er),[gf,Ki]=mf(Er),zi=c.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,s]=c.useState("idle");return h.jsx(gf,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:h.jsx(M.span,{...r,ref:t})})});zi.displayName=Er;var Gi="AvatarImage",Xi=c.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...s}=e,i=Ki(Gi,n),a=wf(r,s.referrerPolicy),l=ee(u=>{o(u),i.onImageLoadingStatusChange(u)});return se(()=>{a!=="idle"&&l(a)},[a,l]),a==="loaded"?h.jsx(M.img,{...s,ref:t,src:r}):null});Xi.displayName=Gi;var qi="AvatarFallback",Zi=c.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,s=Ki(qi,n),[i,a]=c.useState(r===void 0);return c.useEffect(()=>{if(r!==void 0){const l=window.setTimeout(()=>a(!0),r);return()=>window.clearTimeout(l)}},[r]),i&&s.imageLoadingStatus!=="loaded"?h.jsx(M.span,{...o,ref:t}):null});Zi.displayName=qi;function wf(e,t){const[n,r]=c.useState("idle");return se(()=>{if(!e){r("error");return}let o=!0;const s=new window.Image,i=a=>()=>{o&&r(a)};return r("loading"),s.onload=i("loaded"),s.onerror=i("error"),s.src=e,t&&(s.referrerPolicy=t),()=>{o=!1}},[e,t]),n}var jp=zi,Fp=Xi,kp=Zi;function xf(e,t=[]){let n=[];function r(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...x}=f,p=(v==null?void 0:v[e][l])||a,w=c.useMemo(()=>x,Object.values(x));return h.jsx(p.Provider,{value:w,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||a,x=c.useContext(g);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,yf(o,...t)]}function yf(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var An="rovingFocusGroup.onEntryFocus",bf={bubbles:!1,cancelable:!0},dn="RovingFocusGroup",[Kn,Ji,Sf]=Gn(dn),[Cf,Qi]=xf(dn,[Sf]),[Ef,Pf]=Cf(dn),ea=c.forwardRef((e,t)=>h.jsx(Kn.Provider,{scope:e.__scopeRovingFocusGroup,children:h.jsx(Kn.Slot,{scope:e.__scopeRovingFocusGroup,children:h.jsx(Rf,{...e,ref:t})})}));ea.displayName=dn;var Rf=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...f}=e,v=c.useRef(null),g=U(t,v),x=on(s),[p=null,w]=$e({prop:i,defaultProp:a,onChange:l}),[b,y]=c.useState(!1),S=ee(u),C=Ji(n),E=c.useRef(!1),[_,R]=c.useState(0);return c.useEffect(()=>{const O=v.current;if(O)return O.addEventListener(An,S),()=>O.removeEventListener(An,S)},[S]),h.jsx(Ef,{scope:n,orientation:r,dir:x,loop:o,currentTabStopId:p,onItemFocus:c.useCallback(O=>w(O),[w]),onItemShiftTab:c.useCallback(()=>y(!0),[]),onFocusableItemAdd:c.useCallback(()=>R(O=>O+1),[]),onFocusableItemRemove:c.useCallback(()=>R(O=>O-1),[]),children:h.jsx(M.div,{tabIndex:b||_===0?-1:0,"data-orientation":r,...f,ref:g,style:{outline:"none",...e.style},onMouseDown:D(e.onMouseDown,()=>{E.current=!0}),onFocus:D(e.onFocus,O=>{const I=!E.current;if(O.target===O.currentTarget&&I&&!b){const L=new CustomEvent(An,bf);if(O.currentTarget.dispatchEvent(L),!L.defaultPrevented){const W=C().filter(N=>N.focusable),B=W.find(N=>N.active),k=W.find(N=>N.id===p),V=[B,k,...W].filter(Boolean).map(N=>N.ref.current);ra(V,d)}}E.current=!1}),onBlur:D(e.onBlur,()=>y(!1))})})}),ta="RovingFocusGroupItem",na=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,a=Ie(),l=s||a,u=Pf(ta,n),d=u.currentTabStopId===l,f=Ji(n),{onFocusableItemAdd:v,onFocusableItemRemove:g}=u;return c.useEffect(()=>{if(r)return v(),()=>g()},[r,v,g]),h.jsx(Kn.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:h.jsx(M.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...i,ref:t,onMouseDown:D(e.onMouseDown,x=>{r?u.onItemFocus(l):x.preventDefault()}),onFocus:D(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:D(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){u.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const p=_f(x,u.orientation,u.dir);if(p!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let b=f().filter(y=>y.focusable).map(y=>y.ref.current);if(p==="last")b.reverse();else if(p==="prev"||p==="next"){p==="prev"&&b.reverse();const y=b.indexOf(x.currentTarget);b=u.loop?Of(b,y+1):b.slice(y+1)}setTimeout(()=>ra(b))}})})})});na.displayName=ta;var Tf={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Af(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function _f(e,t,n){const r=Af(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Tf[r]}function ra(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Of(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Df=ea,If=na,Pr="Tabs",[Nf,$p]=Ae(Pr,[Qi]),oa=Qi(),[Mf,Rr]=Nf(Pr),sa=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:i="horizontal",dir:a,activationMode:l="automatic",...u}=e,d=on(a),[f,v]=$e({prop:r,onChange:o,defaultProp:s});return h.jsx(Mf,{scope:n,baseId:Ie(),value:f,onValueChange:v,orientation:i,dir:d,activationMode:l,children:h.jsx(M.div,{dir:d,"data-orientation":i,...u,ref:t})})});sa.displayName=Pr;var ia="TabsList",aa=c.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=Rr(ia,n),i=oa(n);return h.jsx(Df,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:r,children:h.jsx(M.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});aa.displayName=ia;var ca="TabsTrigger",la=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,i=Rr(ca,n),a=oa(n),l=fa(i.baseId,r),u=pa(i.baseId,r),d=r===i.value;return h.jsx(If,{asChild:!0,...a,focusable:!o,active:d,children:h.jsx(M.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...s,ref:t,onMouseDown:D(e.onMouseDown,f=>{!o&&f.button===0&&f.ctrlKey===!1?i.onValueChange(r):f.preventDefault()}),onKeyDown:D(e.onKeyDown,f=>{[" ","Enter"].includes(f.key)&&i.onValueChange(r)}),onFocus:D(e.onFocus,()=>{const f=i.activationMode!=="manual";!d&&!o&&f&&i.onValueChange(r)})})})});la.displayName=ca;var ua="TabsContent",da=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...i}=e,a=Rr(ua,n),l=fa(a.baseId,r),u=pa(a.baseId,r),d=r===a.value,f=c.useRef(d);return c.useEffect(()=>{const v=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(v)},[]),h.jsx(ve,{present:o||d,children:({present:v})=>h.jsx(M.div,{"data-state":d?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":l,hidden:!v,id:u,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:v&&s})})});da.displayName=ua;function fa(e,t){return`${e}-trigger-${t}`}function pa(e,t){return`${e}-content-${t}`}var Wp=sa,Vp=aa,Bp=la,Hp=da;export{Wp as $,Bf as A,Pp as B,Hf as C,Vf as D,gp as E,wp as F,xp as G,bp as H,ip as I,Cp as J,Sp as K,yp as L,Rp as M,pp as N,Jf as O,Ff as P,hp as Q,$f as R,Xe as S,Wf as T,jp as U,kf as V,Fp as W,kp as X,Vp as Y,Bp as Z,Hp as _,Gf as a,zf as b,Qf as c,np as d,rp as e,ep as f,tp as g,qf as h,Zf as i,h as j,sp as k,up as l,dp as m,cp as n,lp as o,Tp as p,_p as q,Op as r,Ip as s,Np as t,Mp as u,rf as v,uf as w,vp as x,mp as y,Ep as z};

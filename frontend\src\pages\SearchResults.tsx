import React, { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { GitBranch, Star, Clock, Search, ArrowLeft, Loader2, GitCommit, Plus } from 'lucide-react';
import { GitHubLogo } from '@/components/GitHubLogo';
import { repositoryApi } from '@/lib/api';
import type { Repository } from '@/types/api';
import { useToast } from '@/hooks/use-toast';


const SearchResults = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [newSearchQuery, setNewSearchQuery] = useState('');
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [followingRepos, setFollowingRepos] = useState<Set<string>>(new Set());
  
  const searchQuery = searchParams.get('q') || '';

  // 搜索仓库
  const searchRepositories = async (query: string, page: number = 1) => {
    if (!query.trim()) {
      setRepositories([]);
      setTotalCount(0);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const result = await repositoryApi.searchRepositories(query, page, 30);
      setRepositories(result.repositories);
      setTotalCount(result.totalCount);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
      setRepositories([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // 当搜索查询改变时触发搜索
  useEffect(() => {
    if (searchQuery) {
      searchRepositories(searchQuery, 1);
      // 将当前搜索关键词填入文本框
      setNewSearchQuery(searchQuery);
    } else {
      setRepositories([]);
      setTotalCount(0);
      setError(null);
      setNewSearchQuery('');
    }
  }, [searchQuery]);

  // 加载已关注的仓库列表
  useEffect(() => {
    const loadFollowedRepositories = async () => {
      try {
        const followedRepos = await repositoryApi.getFollowedRepositories({ showToast: false });
        setFollowingRepos(new Set(followedRepos));
      } catch (error) {
        console.error('Failed to load followed repositories:', error);
      }
    };

    loadFollowedRepositories();
  }, []);

  const handleNewSearch = () => {
    if (newSearchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(newSearchQuery.trim())}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNewSearch();
    }
  };

  const handleFollowRepo = async (e: React.MouseEvent, repo: Repository) => {
    e.preventDefault();
    e.stopPropagation();
    
    const repoKey = `${repo.owner}/${repo.name}`;
    
    if (followingRepos.has(repoKey)) {
      toast({
        title: "已关注",
        description: `您已经关注了仓库 ${repoKey}`,
        variant: "default",
      });
      return;
    }

    try {
      // 调用后端API添加仓库关注
      await repositoryApi.followRepository(repo.owner, repo.name);
      
      // 更新本地状态
      setFollowingRepos(prev => new Set([...prev, repoKey]));
      
      toast({
        title: "关注成功",
        description: `已成功关注仓库 ${repoKey}`,
      });
    } catch (error) {
      console.error('Failed to follow repository:', error);
      toast({
        title: "关注失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      });
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-3">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              返回仪表盘
            </Button>
          </div>
          
          <div>
            <h2 className="text-3xl font-bold text-foreground">
              搜索结果
            </h2>
            <p className="text-muted-foreground mt-1">
              {searchQuery ? `搜索 "${searchQuery}" 的结果` : '请输入搜索关键词'}
            </p>
          </div>

          {/* New Search Bar */}
          <div className="flex items-center gap-3 max-w-2xl">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="搜索仓库..."
                value={newSearchQuery}
                onChange={(e) => setNewSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-10"
              />
            </div>
            <Button 
              onClick={handleNewSearch}
              disabled={!newSearchQuery.trim()}
              className="flex items-center gap-2"
            >
              <Search className="w-4 h-4" />
              搜索
            </Button>
          </div>
        </div>

        {/* Search Results */}
        {searchQuery && (
          <>
            {/* Results Summary */}
            <div className="flex items-center gap-4">
              {loading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">搜索中...</span>
                </div>
              ) : (
                <>
                  <Badge variant="outline" className="text-sm">
                    找到 {totalCount} 个仓库
                  </Badge>
                  {repositories.length > 0 && (
                    <p className="text-sm text-muted-foreground">
                      匹配关键词: "{searchQuery}"
                    </p>
                  )}
                </>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <Card>
                <CardContent className="py-12 text-center">
                  <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-foreground mb-2">
                    搜索出错
                  </p>
                  <p className="text-muted-foreground">
                    {error}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Repository Grid */}
            {!loading && !error && repositories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {repositories.map((repo) => {
                  const repoKey = `${repo.owner}/${repo.name}`;
                  const isFollowing = followingRepos.has(repoKey);
                  
                  return (
                    <Card key={repo.id} className="hover:shadow-elegant transition-all duration-300 group h-full flex flex-col">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            <GitHubLogo className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-lg group-hover:text-primary transition-colors truncate">
                                {repo.owner}/{repo.name}
                              </CardTitle>
                              <CardDescription className="text-sm line-clamp-2">
                                {repo.description || '暂无描述'}
                              </CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            <Badge variant={repo.isActive ? 'default' : 'secondary'} className="whitespace-nowrap">
                              {repo.isActive ? '活跃' : '暂停'}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardContent className="space-y-4 flex-1 flex flex-col justify-between">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Star className="w-4 h-4" />
                              {(repo.stars || 0).toLocaleString()}
                            </div>
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <GitCommit className="w-4 h-4" />
                              {repo.language || '未知'}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Clock className="w-4 h-4" />
                            最后提交: {repo.lastCommit || '未知'}
                          </div>
                        </div>
                        
                        <div className="mt-auto pt-4">
                          <Button
                            onClick={(e) => handleFollowRepo(e, repo)}
                            disabled={isFollowing}
                            className={`w-full flex items-center gap-2 ${
                              isFollowing 
                                ? 'bg-github-success hover:bg-github-success/90 text-white' 
                                : ''
                            }`}
                            variant={isFollowing ? 'default' : 'outline'}
                          >
                            <Plus className="w-4 h-4" />
                            {isFollowing ? '已关注' : '添加关注'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : !loading && !error && (
              <Card>
                <CardContent className="py-12 text-center">
                  <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-foreground mb-2">
                    未找到匹配的仓库
                  </p>
                  <p className="text-muted-foreground">
                    尝试使用其他关键词或检查拼写
                  </p>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {/* No Search Query */}
        {!searchQuery && (
          <Card>
            <CardContent className="py-12 text-center">
              <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-foreground mb-2">
                开始搜索仓库
              </p>
              <p className="text-muted-foreground">
                在上方输入仓库名称、所有者或描述关键词进行搜索
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </Layout>
  );
};

export default SearchResults;
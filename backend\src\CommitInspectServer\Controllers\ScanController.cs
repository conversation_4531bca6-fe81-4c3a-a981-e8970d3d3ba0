using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CommitInspectServer.Services;
using CommitInspectServer.EfCore;
using CommitInspectServer.Entities;

namespace CommitInspectServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ScanController : ControllerBase
{
    private readonly IGitHubScanService _scanService;
    private readonly CommitInspectDbContext _context;
    private readonly ILogger<ScanController> _logger;

    public ScanController(IGitHubScanService scanService, CommitInspectDbContext context, ILogger<ScanController> logger)
    {
        _scanService = scanService;
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 手工触发扫描单个仓库（通过仓库ID）
    /// </summary>
    /// <param name="id">仓库ID</param>
    /// <returns>扫描结果</returns>
    [HttpPost("repository/{owner}/{name}")]
    public async Task<IActionResult> ScanRepository(string owner, string name)
    {
        try
        {
            _logger.LogInformation("用户手工触发扫描仓库: {Owner}/{Name}", owner, name);
            
            var result = await _scanService.ScanRepositoryAsync(owner, name);
            
            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = "仓库扫描完成",
                    data = new
                    {
                        repositoryId = result.RepositoryId,
                        owner = result.Owner,
                        name = result.Name,
                        newCommitsCount = result.NewCommitsCount,
                        updatedCommitsCount = result.UpdatedCommitsCount,
                        scanDuration = result.ScanDuration.TotalSeconds,
                        scanTime = result.ScanEndTime
                    }
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "仓库扫描失败",
                    error = result.ErrorMessage
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描仓库失败: {Owner}/{Name}", owner, name);
            return StatusCode(500, new
            {
                success = false,
                message = "扫描仓库时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 手工触发扫描所有已关注的仓库
    /// </summary>
    /// <returns>所有仓库的扫描结果</returns>
    [HttpPost("all")]
    public async Task<IActionResult> ScanAllRepositories()
    {
        try
        {
            _logger.LogInformation("用户手工触发扫描所有仓库");
            
            var results = await _scanService.ScanAllRepositoriesAsync();
            
            var successCount = results.Count(r => r.Success);
            var failedCount = results.Count(r => !r.Success);
            var totalNewCommits = results.Sum(r => r.NewCommitsCount);
            var totalUpdatedCommits = results.Sum(r => r.UpdatedCommitsCount);
            
            return Ok(new
            {
                success = true,
                message = "批量扫描完成",
                summary = new
                {
                    totalRepositories = results.Count,
                    successCount,
                    failedCount,
                    totalNewCommits,
                    totalUpdatedCommits,
                    totalScanDuration = results.Sum(r => r.ScanDuration.TotalSeconds)
                },
                details = results.Select(r => new
                {
                    repositoryId = r.RepositoryId,
                    owner = r.Owner,
                    name = r.Name,
                    success = r.Success,
                    errorMessage = r.ErrorMessage,
                    newCommitsCount = r.NewCommitsCount,
                    updatedCommitsCount = r.UpdatedCommitsCount,
                    scanDuration = r.ScanDuration.TotalSeconds,
                    scanTime = r.ScanEndTime
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量扫描仓库失败");
            return StatusCode(500, new
            {
                success = false,
                message = "批量扫描时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取仓库的扫描状态信息
    /// </summary>
    /// <param name="id">仓库ID</param>
    /// <returns>扫描状态</returns>
    [HttpGet("repository/{id}/status")]
    public IActionResult GetRepositoryScanStatus(Guid id)
    {
        try
        {
            // 这里可以返回仓库的最后扫描时间、提交数量等信息
            // 暂时返回基本信息，后续可以添加更多扫描状态跟踪
            return Ok(new
            {
                success = true,
                message = "获取扫描状态成功",
                data = new
                {
                    repositoryId = id,
                    // 可以添加更多状态信息
                    lastScanTime = DateTime.UtcNow, // 示例值
                    isScanning = false // 示例值
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取仓库扫描状态失败: {RepositoryId}", id);
            return StatusCode(500, new
            {
                success = false,
                message = "获取扫描状态时发生错误",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取扫描日志
    /// </summary>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="repositoryId">可选的仓库ID过滤</param>
    /// <returns>扫描日志列表</returns>
    [HttpGet("logs")]
    public async Task<IActionResult> GetScanLogs(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20, 
        [FromQuery] Guid? repositoryId = null)
    {
        try
        {
            var query = _context.ScanLogs
                .Include(s => s.Repository)
                .AsQueryable();

            if (repositoryId.HasValue)
            {
                query = query.Where(s => s.RepositoryId == repositoryId.Value);
            }

            var totalCount = await query.CountAsync();

            var logs = await query
                .OrderByDescending(s => s.StartTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(s => new
                {
                    id = s.Id,
                    timestamp = s.StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    repository = $"{s.Repository.Owner}/{s.Repository.Name}",
                    status = s.Status.ToString().ToLower(),
                    message = s.Message,
                    duration = (int)s.Duration.TotalMilliseconds,
                    newCommitsCount = s.NewCommitsCount,
                    updatedCommitsCount = s.UpdatedCommitsCount,
                    errorDetails = s.ErrorDetails
                })
                .ToListAsync();

            return Ok(new
            {
                success = true,
                data = new
                {
                    logs,
                    pagination = new
                    {
                        page,
                        pageSize,
                        totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取扫描日志失败");
            return StatusCode(500, new
            {
                success = false,
                message = "获取扫描日志时发生错误",
                error = ex.Message
            });
        }
    }
}
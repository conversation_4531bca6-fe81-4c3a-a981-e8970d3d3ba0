using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class CommitFile
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public string Filename { get; set; } = null!;

    public int Additions { get; set; } = 0;

    public int Deletions { get; set; } = 0;

    /// <summary>
    /// 文件状态：added, modified, deleted
    /// </summary>
    [Required]
    public CommitFileStatus Status { get; set; } = CommitFileStatus.Added;

    /// <summary>
    /// Git diff patch
    /// </summary>
    public string? Patch { get; set; }

    [Required]
    public Guid CommitId { get; set; }

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [ForeignKey("CommitId")]
    public virtual Commit Commit { get; set; } = null!;
}

public enum CommitFileStatus
{
    Added,
    Modified,
    Deleted
}

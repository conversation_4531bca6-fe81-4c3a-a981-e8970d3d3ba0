using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class Commit
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public required string Hash { get; set; }

    [Required]
    public required string Message { get; set; }

    [Required]
    public required string Author { get; set; }

    public string? AuthorEmail { get; set; }

    [Required]
    public DateTime CreatedAt { get; set; }

    public int Additions { get; set; } = 0;

    public int Deletions { get; set; } = 0;

    public int FilesChanged { get; set; } = 0;

    [Required]
    public Guid RepositoryId { get; set; }

    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    [ForeignKey("RepositoryId")]
    public virtual Repository Repository { get; set; } = null!;

    public virtual ICollection<CommitFile> Files { get; set; } = new List<CommitFile>();
    public virtual ICollection<CommitParent> Parents { get; set; } = new List<CommitParent>();
    public virtual ICollection<CommitParent> Children { get; set; } = new List<CommitParent>();
}

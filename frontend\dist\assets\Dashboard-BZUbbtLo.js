import{j as e,c as l,b as $,u as ee,L as se,I as ne,B as E,C as d,a as c,G as D,d as V,e as te,f as re,r as p}from"./index-D3FXQGgv.js";import{r as a,e as ie,L as w}from"./react-vendor-DucYQ77b.js";import{B as j}from"./badge-BHFrTODj.js";import{O as k,c as T,d as I,e as L,f as z,g as H,h as ae,i as oe}from"./radix-ui-DdAvwPj2.js";import{U as y,V as me,W as A,X as de,Y as g,Z as R,_ as ce}from"./utils-CUkY6B8V.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const le=ae,ue=oe,M=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(k,{className:l("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...n,ref:r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:17,columnNumber:3},void 0));M.displayName=k.displayName;const U=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(ue,{children:[e.jsxDEV(M,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:33,columnNumber:5},void 0),e.jsxDEV(T,{ref:r,className:l("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-card text-card-foreground p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:34,columnNumber:5},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:32,columnNumber:3},void 0));U.displayName=T.displayName;const B=({className:t,...n})=>e.jsxDEV("div",{className:l("flex flex-col space-y-2 text-center sm:text-left",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:50,columnNumber:3},void 0);B.displayName="AlertDialogHeader";const F=({className:t,...n})=>e.jsxDEV("div",{className:l("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:64,columnNumber:3},void 0);F.displayName="AlertDialogFooter";const G=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(I,{ref:r,className:l("text-lg font-semibold",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:78,columnNumber:3},void 0));G.displayName=I.displayName;const O=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(L,{ref:r,className:l("text-sm text-muted-foreground",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:90,columnNumber:3},void 0));O.displayName=L.displayName;const _=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(z,{ref:r,className:l($(),t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:103,columnNumber:3},void 0));_.displayName=z.displayName;const K=a.forwardRef(({className:t,...n},r)=>e.jsxDEV(H,{ref:r,className:l($({variant:"outline"}),"mt-2 sm:mt-0",t),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/alert-dialog.tsx",lineNumber:115,columnNumber:3},void 0));K.displayName=H.displayName;const S=t=>{const n=new Date(t),i=Math.floor((new Date().getTime()-n.getTime())/1e3);return i<60?`${i} 秒前`:i<3600?`${Math.floor(i/60)} 分钟前`:i<86400?`${Math.floor(i/3600)} 小时前`:i<2592e3?`${Math.floor(i/86400)} 天前`:i<31536e3?`${Math.floor(i/2592e3)} 个月前`:`${Math.floor(i/31536e3)} 年前`},je=()=>{const t=ie(),{toast:n}=ee(),[r,i]=a.useState([]),[x,Q]=a.useState([]),[m,q]=a.useState(null),[b,W]=a.useState(""),[u,v]=a.useState(!0),[f,h]=a.useState({open:!1,repoId:"",repoName:""});a.useEffect(()=>{(async()=>{try{v(!0);const[o,N,J]=await Promise.all([p.getRepositories({showToast:!1}),p.getRepositoryStats({showToast:!1}),p.getRecentCommits(10,{showToast:!1})]);i(o),q(N),Q(J)}catch{n({title:"加载失败",description:"无法加载仪表盘数据，请稍后重试",variant:"destructive"})}finally{v(!1)}})()},[n]);const C=()=>{b.trim()&&t(`/search?q=${encodeURIComponent(b.trim())}`)},X=s=>{s.key==="Enter"&&C()},Y=(s,o,N)=>{s.preventDefault(),s.stopPropagation(),h({open:!0,repoId:o,repoName:N})},Z=async()=>{try{const s=await p.deleteRepository(f.repoId);i(o=>o.filter(N=>N.id!==f.repoId)),s.summary?n({title:"取消关注成功",description:`已取消关注仓库 ${f.repoName}，清除了 ${s.summary.deletedCommits} 个提交缓存和 ${s.summary.deletedCommitFiles} 个文件记录缓存`}):n({title:"取消关注成功",description:`已取消关注仓库 ${f.repoName}`})}catch(s){console.error("Failed to unfollow repository:",s),n({title:"取消关注失败",description:"请稍后重试",variant:"destructive"})}finally{h({open:!1,repoId:"",repoName:""})}},P=()=>{h({open:!1,repoId:"",repoName:""})};return e.jsxDEV(se,{children:[e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-3xl font-bold text-foreground",children:"仓库仪表盘"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:159,columnNumber:13},void 0),e.jsxDEV("p",{className:"text-muted-foreground mt-1",children:"管理您关注的 GitHub 仓库并审查提交记录"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:162,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:158,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV("div",{className:"relative",children:[e.jsxDEV(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:169,columnNumber:15},void 0),e.jsxDEV(ne,{placeholder:"搜索仓库...",value:b,onChange:s=>W(s.target.value),onKeyPress:X,className:"pl-10 w-64"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:170,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:168,columnNumber:13},void 0),e.jsxDEV(E,{onClick:C,disabled:!b.trim(),className:"flex items-center gap-2",children:[e.jsxDEV(y,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:184,columnNumber:15},void 0),"搜索"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:179,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:167,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:157,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxDEV(d,{children:e.jsxDEV(c,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-primary/10 rounded-full",children:e.jsxDEV(D,{className:"w-6 h-6 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:196,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:195,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:u?"...":(m==null?void 0:m.totalRepositories)||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:199,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"关注仓库"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:202,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:198,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:194,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:193,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:192,columnNumber:11},void 0),e.jsxDEV(d,{children:e.jsxDEV(c,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-github-success/10 rounded-full",children:e.jsxDEV(me,{className:"w-6 h-6 text-github-success"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:212,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:211,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:u?"...":(m==null?void 0:m.activeRepositories)||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:215,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"活跃仓库"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:218,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:214,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:210,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:209,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:208,columnNumber:11},void 0),e.jsxDEV(d,{children:e.jsxDEV(c,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-center gap-4",children:[e.jsxDEV("div",{className:"p-3 bg-github-warning/10 rounded-full",children:e.jsxDEV(A,{className:"w-6 h-6 text-github-warning"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:228,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:227,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-2xl font-bold text-foreground",children:u?"...":((m==null?void 0:m.totalStars)||0).toLocaleString()},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:231,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"总 Stars"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:234,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:230,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:226,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:225,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:224,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:191,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u?Array.from({length:6}).map((s,o)=>e.jsxDEV(d,{className:"animate-pulse",children:[e.jsxDEV(V,{className:"pb-3",children:e.jsxDEV("div",{className:"flex items-start justify-between",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV("div",{className:"w-5 h-5 bg-muted rounded"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:250,columnNumber:23},void 0),e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"h-5 bg-muted rounded w-32 mb-2"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:252,columnNumber:25},void 0),e.jsxDEV("div",{className:"h-4 bg-muted rounded w-24"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:253,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:251,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:249,columnNumber:21},void 0),e.jsxDEV("div",{className:"h-6 bg-muted rounded w-12"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:256,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:248,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:247,columnNumber:17},void 0),e.jsxDEV(c,{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"h-4 bg-muted rounded w-16"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:261,columnNumber:21},void 0),e.jsxDEV("div",{className:"h-4 bg-muted rounded w-20"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:262,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:260,columnNumber:19},void 0),e.jsxDEV("div",{className:"h-4 bg-muted rounded w-24"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:264,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:259,columnNumber:17},void 0)]},o,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:246,columnNumber:15},void 0)):r.length>0?r.map(s=>e.jsxDEV(w,{to:`/repository/${s.owner}/${s.name}`,children:e.jsxDEV(d,{className:"hover:shadow-elegant transition-all duration-300 group cursor-pointer h-full flex flex-col",children:[e.jsxDEV(V,{className:"pb-3",children:e.jsxDEV("div",{className:"flex items-start justify-between gap-3",children:[e.jsxDEV("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[e.jsxDEV(D,{className:"w-5 h-5 text-muted-foreground flex-shrink-0"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:275,columnNumber:25},void 0),e.jsxDEV("div",{className:"min-w-0 flex-1",children:[e.jsxDEV(te,{className:"text-lg group-hover:text-primary transition-colors truncate",children:[s.owner,"/",s.name]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:277,columnNumber:27},void 0),e.jsxDEV(re,{className:"text-sm line-clamp-2",children:s.description||"暂无描述"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:280,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:276,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:274,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsxDEV(j,{variant:s.isActive?"default":"secondary",className:"whitespace-nowrap",children:s.isActive?"活跃":"暂停"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:286,columnNumber:25},void 0),e.jsxDEV(E,{variant:"ghost",size:"sm",onClick:o=>Y(o,s.id,`${s.owner}/${s.name}`),className:"h-6 w-6 p-0 text-muted-foreground hover:text-destructive flex-shrink-0",title:"取消关注",children:e.jsxDEV(de,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:296,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:289,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:285,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:273,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:272,columnNumber:19},void 0),e.jsxDEV(c,{className:"space-y-4 flex-1 flex flex-col justify-between",children:[e.jsxDEV("div",{className:"space-y-4",children:e.jsxDEV("div",{className:"flex items-center justify-between text-sm",children:[e.jsxDEV("div",{className:"flex items-center gap-2 text-muted-foreground",children:[e.jsxDEV(A,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:306,columnNumber:27},void 0),s.stars.toLocaleString()]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:305,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-muted-foreground",children:[e.jsxDEV(g,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:310,columnNumber:27},void 0),s.commitCount," 提交"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:309,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:304,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:303,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-sm text-muted-foreground mt-auto",children:[e.jsxDEV(R,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:317,columnNumber:23},void 0),"最后提交: ",s.lastCommitTime?S(s.lastCommitTime):"无"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:316,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:302,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:271,columnNumber:17},void 0)},s.id,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:270,columnNumber:15},void 0)):e.jsxDEV("div",{className:"col-span-full text-center py-12",children:[e.jsxDEV(D,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:326,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-muted-foreground",children:"暂无仓库数据"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:327,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:325,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:242,columnNumber:9},void 0),e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(g,{className:"w-6 h-6 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:335,columnNumber:13},void 0),e.jsxDEV("h3",{className:"text-xl font-semibold text-foreground",children:"最新提交"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:336,columnNumber:13},void 0),e.jsxDEV(j,{variant:"secondary",children:u?"...":x.length},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:337,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:334,columnNumber:11},void 0),e.jsxDEV("div",{className:"space-y-3",children:u?Array.from({length:5}).map((s,o)=>e.jsxDEV(d,{className:"animate-pulse",children:e.jsxDEV(c,{className:"p-4",children:e.jsxDEV("div",{className:"flex items-start gap-4",children:[e.jsxDEV("div",{className:"w-8 h-8 rounded-full bg-muted"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:347,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex-1 space-y-2",children:[e.jsxDEV("div",{className:"h-4 bg-muted rounded w-3/4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:349,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV("div",{className:"h-3 bg-muted rounded w-16"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:351,columnNumber:27},void 0),e.jsxDEV("div",{className:"h-3 bg-muted rounded w-12"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:352,columnNumber:27},void 0),e.jsxDEV("div",{className:"h-5 bg-muted rounded w-20"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:353,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:350,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:348,columnNumber:23},void 0),e.jsxDEV("div",{className:"h-3 bg-muted rounded w-16"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:356,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:346,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:345,columnNumber:19},void 0)},o,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:344,columnNumber:17},void 0)):x.length>0?x.slice(0,10).map(s=>e.jsxDEV(w,{to:`/commit/${s.hash}`,children:e.jsxDEV(d,{className:"hover:shadow-elegant transition-all duration-300 group cursor-pointer",children:e.jsxDEV(c,{className:"p-4",children:e.jsxDEV("div",{className:"flex items-start gap-4",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV("div",{className:"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsxDEV(ce,{className:"w-4 h-4 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:369,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:368,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:367,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex-1 min-w-0",children:e.jsxDEV("div",{className:"flex items-start justify-between gap-4",children:[e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("p",{className:"text-sm font-medium text-foreground group-hover:text-primary transition-colors",children:s.message},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:376,columnNumber:31},void 0),e.jsxDEV("div",{className:"flex items-center gap-3 mt-1",children:[e.jsxDEV("span",{className:"text-xs text-muted-foreground",children:s.author},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:380,columnNumber:33},void 0),e.jsxDEV("span",{className:"text-xs text-muted-foreground",children:"提交到"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:383,columnNumber:33},void 0),e.jsxDEV(j,{variant:"outline",className:"text-xs",children:[s.repository.owner,"/",s.repository.name]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:386,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:379,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:375,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[e.jsxDEV(R,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:393,columnNumber:31},void 0),S(s.createdAt)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:392,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:374,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:373,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:366,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:365,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:364,columnNumber:19},void 0)},s.hash,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:363,columnNumber:17},void 0)):e.jsxDEV(d,{children:e.jsxDEV(c,{className:"p-8 text-center",children:[e.jsxDEV(g,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:406,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-muted-foreground",children:"暂无最新提交"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:407,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:405,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:404,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:340,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:333,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:155,columnNumber:7},void 0),e.jsxDEV(le,{open:f.open,onOpenChange:s=>!s&&P(),children:e.jsxDEV(U,{children:[e.jsxDEV(B,{children:[e.jsxDEV(G,{children:"确认取消关注"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:420,columnNumber:13},void 0),e.jsxDEV(O,{children:["您确定要取消关注仓库 ",e.jsxDEV("strong",{className:"text-foreground",children:f.repoName},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:422,columnNumber:26},void 0)," 吗？",e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:423,columnNumber:15},void 0),e.jsxDEV("span",{className:"text-muted-foreground",children:"此操作将删除该仓库的所有数据，包括提交记录和文件变更信息，且无法撤销。"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:424,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:421,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:419,columnNumber:11},void 0),e.jsxDEV(F,{children:[e.jsxDEV(K,{onClick:P,children:"取消"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:430,columnNumber:13},void 0),e.jsxDEV(_,{onClick:Z,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"确认取消关注"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:433,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:429,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:418,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:417,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Dashboard.tsx",lineNumber:154,columnNumber:5},void 0)};export{je as default};

import{j as r,c as t}from"./index-D3FXQGgv.js";import{r as o}from"./react-vendor-DucYQ77b.js";import{U as l,W as m,X as n}from"./radix-ui-DdAvwPj2.js";const i=o.forwardRef(({className:e,...a},s)=>r.jsxDEV(l,{ref:s,className:t("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/avatar.tsx",lineNumber:10,columnNumber:3},void 0));i.displayName=l.displayName;const c=o.forwardRef(({className:e,...a},s)=>r.jsxDEV(m,{ref:s,className:t("aspect-square h-full w-full",e),...a},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/avatar.tsx",lineNumber:25,columnNumber:3},void 0));c.displayName=m.displayName;const f=o.forwardRef(({className:e,...a},s)=>r.jsxDEV(n,{ref:s,className:t("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/avatar.tsx",lineNumber:37,columnNumber:3},void 0));f.displayName=n.displayName;export{i as A,f as a};

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CommitInspectServer.EfCore;
using CommitInspectServer.Services;
using Microsoft.EntityFrameworkCore;

namespace CommitInspectServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RepositoryController : ControllerBase
{
    private readonly CommitInspectDbContext _context;
    private readonly IGitHubService _gitHubService;

    public RepositoryController(CommitInspectDbContext context, IGitHubService gitHubService)
    {
        _context = context;
        _gitHubService = gitHubService;
    }

    [HttpGet]
    public async Task<IActionResult> GetRepositories()
    {
        try
        {
            var repositories = await _context.Repositories
                .Include(r => r.Commits)
                .Select(r => new
                {
                    r.Id,
                    r.Owner,
                    r.<PERSON>,
                    r.Description,
                    r.<PERSON>,
                    r.<PERSON>,
                    r.G<PERSON>,
                    r.<PERSON>,
                    r.<PERSON>t,
                    LastCommit = r.Commits.OrderByDescending(c => c.CreatedAt).FirstOrDefault(),
                    CommitCount = r.Commits.Count(),
                    IsActive = r.Commits.Any(c => c.CreatedAt > DateTime.UtcNow.AddDays(-7)) // 活跃定义为最近7天有提交
                })
                .ToListAsync();

            var result = repositories.Select(r => new
            {
                r.Id,
                r.Owner,
                r.Name,
                r.Description,
                r.Stars,
                r.Language,
                r.GithubUrl,
                r.CreatedAt,
                r.UpdatedAt,
                r.CommitCount,
                r.IsActive,
                LastCommitTime = r.LastCommit?.CreatedAt,
                LastCommitMessage = r.LastCommit?.Message
            });

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取仓库列表失败", error = ex.Message });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetRepository(Guid id)
    {
        try
        {
            var repository = await _context.Repositories
                .Where(r => r.Id == id)
                .Select(r => new
                {
                    r.Id,
                    r.Name,
                    r.GithubUrl,
                    r.Description,
                    r.CreatedAt,
                    r.UpdatedAt
                })
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            return Ok(repository);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取仓库详情失败", error = ex.Message });
        }
    }

    [HttpGet("stats")]
    public async Task<IActionResult> GetRepositoryStats()
    {
        try
        {
            var totalRepositories = await _context.Repositories.CountAsync();
            var activeRepositories = await _context.Repositories
                .Where(r => r.Commits.Any(c => c.CreatedAt > DateTime.UtcNow.AddDays(-7)))
                .CountAsync();
            var totalStars = await _context.Repositories.SumAsync(r => r.Stars);
            var totalCommits = await _context.Commits.CountAsync();

            return Ok(new
            {
                TotalRepositories = totalRepositories,
                ActiveRepositories = activeRepositories,
                TotalStars = totalStars,
                TotalCommits = totalCommits
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取统计信息失败", error = ex.Message });
        }
    }

    [HttpGet("recent-commits")]
    public async Task<IActionResult> GetRecentCommits([FromQuery] int limit = 10)
    {
        try
        {
            var recentCommits = await _context.Commits
                .Include(c => c.Repository)
                .OrderByDescending(c => c.CreatedAt)
                .Take(limit)
                .Select(c => new
                {
                    c.Hash,
                    c.Message,
                    c.Author,
                    c.AuthorEmail,
                    c.CreatedAt,
                    Repository = new
                    {
                        c.Repository.Owner,
                        c.Repository.Name
                    }
                })
                .ToListAsync();

            return Ok(recentCommits);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取最新提交失败", error = ex.Message });
        }
    }

    [HttpGet("{owner}/{name}")]
    public async Task<IActionResult> GetRepositoryByOwnerName(string owner, string name)
    {
        try
        {
            var repository = await _context.Repositories
                .Where(r => r.Owner == owner && r.Name == name)
                .Select(r => new
                {
                    r.Id,
                    r.Owner,
                    r.Name,
                    r.Description,
                    r.Stars,
                    r.Language,
                    r.GithubUrl,
                    r.CreatedAt,
                    r.UpdatedAt,
                    CommitCount = r.Commits.Count(),
                    LastCommit = r.Commits.OrderByDescending(c => c.CreatedAt).FirstOrDefault()
                })
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            return Ok(repository);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取仓库详情失败", error = ex.Message });
        }
    }

    [HttpGet("{owner}/{name}/branches")]
    public async Task<IActionResult> GetRepositoryBranches(string owner, string name)
    {
        try
        {
            var repository = await _context.Repositories
                .Where(r => r.Owner == owner && r.Name == name)
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            var branches = await _context.Branches
                .Where(b => b.RepositoryId == repository.Id)
                .Select(b => new { 
                    b.Name,
                    b.HeadCommit!.Hash
                })
                .OrderBy(b => b.Name)
                .ToListAsync();

            return Ok(branches);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取分支列表失败", error = ex.Message });
        }
    }

    [HttpGet("{owner}/{name}/commits")]
    public async Task<IActionResult> GetRepositoryCommits(string owner, string name, [FromQuery] string? branch = null, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            var repository = await _context.Repositories
                .Where(r => r.Owner == owner && r.Name == name)
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            var query = _context.Commits
                .Where(c => c.RepositoryId == repository.Id);

            if (!string.IsNullOrEmpty(branch))
            {
                // For now, we'll filter commits by checking if they exist in the branch
                // This is a simplified approach until we implement proper branch-commit relationships
                var branchEntity = await _context.Branches
                    .Where(b => b.RepositoryId == repository.Id && b.Name == branch)
                    .FirstOrDefaultAsync();

                if (branchEntity == null)
                {
                    return NotFound(new { success = false, message = "分支不存在" });
                }

                // For now, return all commits for the repository
                // TODO: Implement proper branch-commit relationship filtering
            }

            var totalCount = await query.CountAsync();

            var commits = await query
                .OrderByDescending(c => c.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(c => new
                {
                    c.Hash,
                    c.Message,
                    c.Author,
                    c.AuthorEmail,
                    c.CreatedAt,
                    c.Additions,
                    c.Deletions,
                    c.FilesChanged
                })
                .ToListAsync();

            return Ok(new
            {
                commits,
                totalCount,
                page,
                pageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取提交列表失败", error = ex.Message });
        }
    }

    [HttpGet("{owner}/{name}/git-flow")]
    public async Task<IActionResult> GetRepositoryGitFlow(string owner, string name, [FromQuery] int limit = 100)
    {
        try
        {
            var repository = await _context.Repositories
                .Where(r => r.Owner == owner && r.Name == name)
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            // 获取最近的 commits 及其父子关系
            var commits = await _context.Commits
                .Include(c => c.Parents)
                    .ThenInclude(p => p.ParentCommit)
                .Include(c => c.Children)
                    .ThenInclude(ch => ch.ChildCommit)
                .Where(c => c.RepositoryId == repository.Id)
                .OrderByDescending(c => c.CreatedAt)
                .Take(limit)
                .Select(c => new
                {
                    c.Id,
                    c.Hash,
                    c.Message,
                    c.Author,
                    c.AuthorEmail,
                    c.CreatedAt,
                    c.Additions,
                    c.Deletions,
                    c.FilesChanged,
                    Parents = c.Parents.Select(p => new
                    {
                        p.ParentCommit.Id,
                        p.ParentCommit.Hash
                    }).ToList(),
                    Children = c.Children.Select(ch => new
                    {
                        ch.ChildCommit.Id,
                        ch.ChildCommit.Hash
                    }).ToList()
                })
                .ToListAsync();

            // 获取仓库的分支信息
            var branches = await _context.Branches
                .Where(b => b.RepositoryId == repository.Id)
                .Select(b => new
                {
                    b.Name,
                    HeadCommitHash = b.HeadCommit != null ? b.HeadCommit.Hash : null
                })
                .ToListAsync();

            // 获取标签信息
            var tags = await _context.Tags
                .Include(t => t.Commit)
                .Where(t => t.RepositoryId == repository.Id)
                .Select(t => new
                {
                    t.Name,
                    t.IsAnnotated,
                    t.AnnotationMessage,
                    CommitHash = t.Commit.Hash
                })
                .ToListAsync();

            return Ok(new
            {
                commits,
                branches,
                tags
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取Git Flow数据失败", error = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteRepository(Guid id)
    {
        try
        {
            var repository = await _context.Repositories
                .Include(r => r.Commits)
                .ThenInclude(c => c.Files)
                .Where(r => r.Id == id)
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            var deletionSummary = await DeleteRepositoryDataAsync(repository);

            return Ok(new 
            { 
                success = true, 
                message = "仓库取消关注成功，已删除所有相关数据",
                summary = deletionSummary
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "取消关注仓库失败", error = ex.Message });
        }
    }

    [HttpDelete("{owner}/{name}")]
    public async Task<IActionResult> DeleteRepositoryByOwnerName(string owner, string name)
    {
        try
        {
            var repository = await _context.Repositories
                .Include(r => r.Commits)
                .ThenInclude(c => c.Files)
                .Where(r => r.Owner == owner && r.Name == name)
                .FirstOrDefaultAsync();

            if (repository == null)
            {
                return NotFound(new { success = false, message = "仓库不存在" });
            }

            var deletionSummary = await DeleteRepositoryDataAsync(repository);

            return Ok(new 
            { 
                success = true, 
                message = "仓库取消关注成功，已删除所有相关数据",
                summary = deletionSummary
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "取消关注仓库失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 删除仓库及其所有相关数据的内部方法
    /// </summary>
    /// <param name="repository">要删除的仓库实体</param>
    /// <returns>删除操作的摘要信息</returns>
    private async Task<object> DeleteRepositoryDataAsync(Entities.Repository repository)
    {
        var commitFilesCount = 0;
        var commitsCount = repository.Commits.Count;

        // 1. 删除所有提交文件记录
        foreach (var commit in repository.Commits)
        {
            commitFilesCount += commit.Files.Count;
            _context.CommitFiles.RemoveRange(commit.Files);
        }

        // 2. 删除所有提交记录
        _context.Commits.RemoveRange(repository.Commits);

        // 3. 删除仓库记录
        _context.Repositories.Remove(repository);

        // 保存所有更改
        await _context.SaveChangesAsync();

        return new
        {
            repositoryId = repository.Id,
            repositoryName = $"{repository.Owner}/{repository.Name}",
            deletedCommits = commitsCount,
            deletedCommitFiles = commitFilesCount,
            deletedAt = DateTime.UtcNow
        };
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchRepositories([FromQuery] string q, [FromQuery] int page = 1, [FromQuery] int per_page = 30)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
            {
                return BadRequest(new { success = false, message = "搜索关键词不能为空" });
            }

            // 获取所有用户仓库（包括分页获取）
            var allRepositories = new List<GitHubRepository>();
            var currentPage = 1;
            const int maxPerPage = 100; // GitHub API 最大每页数量

            // 获取所有仓库（可能需要多次请求）
            while (true)
            {
                var repositoriesPage = await _gitHubService.GetUserRepositoriesAsync(currentPage, maxPerPage);
                if (repositoriesPage == null || repositoriesPage.Count == 0)
                    break;
                
                allRepositories.AddRange(repositoriesPage);
                
                // 如果返回的仓库数量少于最大值，说明已经是最后一页
                if (repositoriesPage.Count < maxPerPage)
                    break;
                
                currentPage++;
            }

            // 本地过滤
            var filteredRepositories = allRepositories.Where(r =>
                r.Name.Contains(q, StringComparison.OrdinalIgnoreCase) ||
                r.Owner.Login.Contains(q, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(r.Description) && r.Description.Contains(q, StringComparison.OrdinalIgnoreCase))
            ).ToList();

            // 按更新时间排序
            filteredRepositories = filteredRepositories.OrderByDescending(r => r.UpdatedAt).ToList();

            // 分页处理
            var totalCount = filteredRepositories.Count;
            var pagedRepositories = filteredRepositories
                .Skip((page - 1) * per_page)
                .Take(per_page)
                .ToList();

            var repositories = pagedRepositories.Select(r => new
            {
                id = r.Id,
                owner = r.Owner.Login,
                name = r.Name,
                description = r.Description,
                stars = r.StargazersCount,
                lastCommit = r.PushedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知",
                activeBranch = r.DefaultBranch,
                isActive = r.PushedAt.HasValue && r.PushedAt.Value > DateTime.UtcNow.AddDays(-7),
                language = r.Language,
                htmlUrl = r.HtmlUrl,
                isPrivate = r.Private,
                createdAt = r.CreatedAt,
                updatedAt = r.UpdatedAt
            });

            return Ok(new
            {
                repositories,
                totalCount,
                page,
                perPage = per_page
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "搜索仓库失败", error = ex.Message });
        }
    }

    [HttpGet("user-repos")]
    public async Task<IActionResult> GetUserRepositories([FromQuery] int page = 1, [FromQuery] int per_page = 100)
    {
        try
        {
            var repositories = await _gitHubService.GetUserRepositoriesAsync(page, per_page);
            
            var result = repositories.Select(r => new
            {
                id = r.Id,
                owner = r.Owner.Login,
                name = r.Name,
                description = r.Description,
                stars = r.StargazersCount,
                lastCommit = r.PushedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知",
                activeBranch = r.DefaultBranch,
                branches = 1,
                isActive = r.PushedAt.HasValue && r.PushedAt.Value > DateTime.UtcNow.AddDays(-7),
                language = r.Language,
                htmlUrl = r.HtmlUrl,
                isPrivate = r.Private,
                createdAt = r.CreatedAt,
                updatedAt = r.UpdatedAt
            });

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取用户仓库失败", error = ex.Message });
        }
    }

    [HttpPost("follow")]
    public async Task<IActionResult> FollowRepository([FromBody] FollowRepositoryRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Owner) || string.IsNullOrWhiteSpace(request.Name))
            {
                return BadRequest(new { success = false, message = "仓库所有者和名称不能为空" });
            }

            // 检查仓库是否已经被关注
            var existingRepo = await _context.Repositories
                .Where(r => r.Owner == request.Owner && r.Name == request.Name)
                .FirstOrDefaultAsync();

            if (existingRepo != null)
            {
                return BadRequest(new { success = false, message = "该仓库已经被关注" });
            }

            // 从GitHub API获取仓库详细信息
            var githubRepos = await _gitHubService.GetUserRepositoriesAsync(1, 100);
            var githubRepo = githubRepos.FirstOrDefault(r => 
                r.Owner.Login.Equals(request.Owner, StringComparison.OrdinalIgnoreCase) && 
                r.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase));

            if (githubRepo == null)
            {
                return NotFound(new { success = false, message = "未找到该仓库或您没有访问权限" });
            }

            // 创建新的仓库记录
            var repository = new Entities.Repository
            {
                Owner = githubRepo.Owner.Login,
                Name = githubRepo.Name,
                Description = githubRepo.Description,
                GithubUrl = githubRepo.HtmlUrl,
                Stars = githubRepo.StargazersCount,
                Language = githubRepo.Language,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Repositories.Add(repository);
            await _context.SaveChangesAsync();

            return Ok(new 
            { 
                success = true, 
                message = "仓库关注成功",
                repository = new
                {
                    repository.Id,
                    repository.Owner,
                    repository.Name,
                    repository.Description,
                    repository.Stars,
                    repository.Language,
                    repository.GithubUrl,
                    repository.CreatedAt,
                    repository.UpdatedAt
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "关注仓库失败", error = ex.Message });
        }
    }

    [HttpGet("followed")]
    public async Task<IActionResult> GetFollowedRepositories()
    {
        try
        {
            var followedRepos = await _context.Repositories
                .Select(r => new
                {
                    r.Owner,
                    r.Name
                })
                .ToListAsync();

            var followedRepoKeys = followedRepos.Select(r => $"{r.Owner}/{r.Name}").ToList();

            return Ok(followedRepoKeys);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "获取已关注仓库失败", error = ex.Message });
        }
    }
}

public class FollowRepositoryRequest
{
    public required string Owner { get; set; }
    public required string Name { get; set; }
}
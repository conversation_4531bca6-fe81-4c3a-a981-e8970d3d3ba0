import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useSearchParams } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GitBranch, Clock, Hash, User, ArrowLeft, RefreshCw, Scan, GitGraph } from 'lucide-react';
import { GitHubLogo } from '@/components/GitHubLogo';
import GitFlowGraph from '@/components/GitFlowGraph';
import { repository<PERSON>pi, scanApi } from '@/lib/api';
import type { Repository as RepositoryType, Commit, Branch, GitFlowData } from '@/types/api';
import { toast } from '@/hooks/use-toast';

const Repository = () => {
  const { owner, name } = useParams<{ owner: string; name: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [repository, setRepository] = useState<RepositoryType | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState('main');
  const [commits, setCommits] = useState<Commit[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [gitFlowData, setGitFlowData] = useState<GitFlowData | null>(null);
  const [gitFlowLoading, setGitFlowLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('commits');

  // 加载仓库基本信息
  const loadRepository = async () => {
    if (!owner || !name) return;
    
    try {
      const data = await repositoryApi.getRepositoryByOwnerName(owner, name);
      setRepository(data);
    } catch (error) {
      console.error('Failed to load repository:', error);
    }
  };

  // 加载分支列表
  const loadBranches = async () => {
    if (!owner || !name) return;
    
    try {
      const data = await repositoryApi.getRepositoryBranches(owner, name);
      setBranches(data);
      
      // 从URL参数中获取分支，如果没有则使用第一个分支
      const branchFromUrl = searchParams.get('branch');
      let targetBranch = 'main';
      
      if (branchFromUrl && data.some(branch => branch.name === branchFromUrl)) {
        targetBranch = branchFromUrl;
      } else if (data.length > 0) {
        targetBranch = data[0].name;
      }
      
      if (targetBranch !== selectedBranch) {
        setSelectedBranch(targetBranch);
        // 更新URL参数
        const newParams = new URLSearchParams(searchParams);
        newParams.set('branch', targetBranch);
        setSearchParams(newParams, { replace: true });
      }
    } catch (error) {
      console.error('Failed to load branches:', error);
    }
  };

  // 加载提交列表
  const loadCommits = async (page = 1, branch = selectedBranch) => {
    if (!owner || !name) return;

    try {
      setLoading(true);
      const data = await repositoryApi.getRepositoryCommits(owner, name, branch, page, 20);

      if (page === 1) {
        setCommits(data.commits);
      } else {
        setCommits(prev => [...prev, ...data.commits]);
      }

      setCurrentPage(page);
      setTotalPages(data.totalPages);
      setHasMore(page < data.totalPages);
    } catch (error) {
      console.error('Failed to load commits:', error);
      toast({
        title: "加载失败",
        description: "无法加载提交记录",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 加载 Git Flow 数据
  const loadGitFlow = async () => {
    if (!owner || !name) return;

    try {
      setGitFlowLoading(true);
      const data = await repositoryApi.getRepositoryGitFlow(owner, name, 100);
      setGitFlowData(data);
    } catch (error) {
      console.error('Failed to load git flow:', error);
      toast({
        title: "加载失败",
        description: "无法加载Git Flow数据",
        variant: "destructive",
      });
    } finally {
      setGitFlowLoading(false);
    }
  };

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      setInitialLoading(true);
      await Promise.all([
        loadRepository(),
        loadBranches(),
      ]);
      setInitialLoading(false);
    };
    
    initializeData();
  }, [owner, name]);

  // 当分支改变时重新加载提交
  useEffect(() => {
    if (!initialLoading && branches.length > 0) {
      if (activeTab === 'commits') {
        loadCommits(1, selectedBranch);
      }
    }
  }, [selectedBranch, initialLoading, branches.length, activeTab]);

  // 当切换到 Git Flow 标签页时加载数据
  useEffect(() => {
    if (!initialLoading && activeTab === 'gitflow' && !gitFlowData) {
      loadGitFlow();
    }
  }, [activeTab, initialLoading, gitFlowData]);

  const handleRefresh = () => {
    if (activeTab === 'commits') {
      loadCommits(1);
    } else if (activeTab === 'gitflow') {
      loadGitFlow();
    }
  };

  const handleBranchChange = (branch: string) => {
    setSelectedBranch(branch);
    // 更新URL参数以保存分支状态
    const newParams = new URLSearchParams(searchParams);
    newParams.set('branch', branch);
    setSearchParams(newParams, { replace: true });
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadCommits(currentPage + 1);
    }
  };

  const handleScan = async () => {
    if (!owner || !name || scanning) return;

    try {
      setScanning(true);
      const result = await scanApi.scanRepositoryByOwnerName(owner, name);

      if (result.success && result.data) {
        toast({
          title: "扫描完成",
          description: `发现 ${result.data.newCommitsCount} 个新提交，用时 ${result.data.scanDuration.toFixed(2)} 秒`,
        });

        // 如果有新提交，刷新数据
        if (result.data.newCommitsCount > 0) {
          if (activeTab === 'commits') {
            await loadCommits(1);
          } else if (activeTab === 'gitflow') {
            await loadGitFlow();
          }
        }
      } else {
        toast({
          title: "扫描失败",
          description: result.error || "扫描时发生未知错误",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to scan repository:', error);
      toast({
        title: "扫描失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setScanning(false);
    }
  };

  const handleCommitClick = (commit: any) => {
    // 导航到 commit 详情页面
    window.open(`/commit/${commit.hash}?from=${encodeURIComponent(`/repository/${owner}/${name}?branch=${selectedBranch}`)}`, '_blank');
  };

  if (initialLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      </Layout>
    );
  }

  if (!repository) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-foreground mb-2">仓库不存在</h2>
          <p className="text-muted-foreground mb-4">无法找到仓库 {owner}/{name}</p>
          <Button asChild>
            <Link to="/dashboard">返回仪表盘</Link>
          </Button>
        </div>
      </Layout>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getCommitTypeColor = (message: string) => {
    const type = message.split(':')[0].toLowerCase();
    switch (type) {
      case 'feat': return 'success';
      case 'fix': return 'danger';
      case 'docs': return 'warning';
      case 'refactor': return 'modified';
      default: return 'default';
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <GitHubLogo className="w-6 h-6 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">
              {repository.owner}/{repository.name}
            </h1>
          </div>
          
          <Button
            variant="default"
            size="sm"
            onClick={handleScan}
            disabled={scanning || loading}
            className="flex items-center gap-2"
          >
            {scanning ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <Scan className="w-4 h-4" />
            )}
            {scanning ? '扫描中...' : '立即扫描'}
          </Button>
        </div>

        {/* Tabs for different views */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="commits" className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  提交历史
                </TabsTrigger>
                <TabsTrigger value="gitflow" className="flex items-center gap-2">
                  <GitGraph className="w-4 h-4" />
                  Git Flow
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <GitBranch className="w-4 h-4 text-muted-foreground" />
                <Select value={selectedBranch} onValueChange={handleBranchChange}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.name} value={branch.name}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading || scanning || gitFlowLoading}
              >
                {(loading || gitFlowLoading) ? (
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                刷新
              </Button>
            </div>
          </div>

          {/* Tab Contents */}
          <TabsContent value="commits" className="space-y-6">
            {/* Commits List */}
            <div className="space-y-6">
              {commits.map((commit) => (
                <Link key={commit.hash} to={`/commit/${commit.hash}?from=${encodeURIComponent(`/repository/${owner}/${name}?branch=${selectedBranch}`)}`}>
                  <Card className="hover:shadow-elegant transition-all duration-300 group cursor-pointer">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Avatar className="w-10 h-10">
                          <AvatarFallback>
                            {commit.author.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-2 mb-3">
                            <div className="flex-1">
                              <p className="font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2">
                                {commit.message}
                              </p>
                              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <User className="w-3 h-3" />
                                  {commit.author}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatDate(commit.createdAt)}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Hash className="w-3 h-3" />
                                  <code className="text-xs">{commit.hash.substring(0, 8)}</code>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <Badge variant={getCommitTypeColor(commit.message) as any}>
                                {commit.message.split(':')[0]}
                              </Badge>
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">文件:</span>
                              <span className="font-medium">{commit.filesChanged || 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="text-green-600">+{commit.additions || 0}</span>
                              <span className="text-red-600">-{commit.deletions || 0}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            {/* Load More */}
            {hasMore && (
              <div className="text-center">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      加载中...
                    </div>
                  ) : (
                    '加载更多提交'
                  )}
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="gitflow" className="space-y-6">
            {gitFlowLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin" />
              </div>
            ) : gitFlowData ? (
              <Card>
                <CardContent className="p-6">
                  <GitFlowGraph
                    commits={gitFlowData.commits}
                    branches={gitFlowData.branches}
                    tags={gitFlowData.tags}
                    onCommitClick={handleCommitClick}
                  />
                </CardContent>
              </Card>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">暂无Git Flow数据</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Repository;
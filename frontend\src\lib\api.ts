import { toast } from '@/hooks/use-toast';
import type { LoginResponse, Repository, Commit, RepositoryStats, CommitDetail, Branch, GitFlowData } from '@/types/api';

// 用于在API中访问认证状态，避免循环依赖
let authLogout: (() => void) | null = null;

export const setAuthLogout = (logoutFn: () => void) => {
  authLogout = logoutFn;
};

interface ApiOptions extends RequestInit {
  showToast?: boolean;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const api = {
  request: async <T = any>(
    url: string,
    options: ApiOptions = {}
  ): Promise<T> => {
    const { showToast = true, ...fetchOptions } = options;

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
      ...fetchOptions,
    };

    // 添加认证token
    const token = localStorage.getItem('authToken');
    if (token) {
      (defaultOptions.headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`/api${url}`, defaultOptions);
      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.message || `HTTP ${response.status}: ${response.statusText}`;
        
        // 处理401错误，自动登出
        if (response.status === 401) {
          if (authLogout) {
            authLogout();
            // 延迟跳转，让logout先执行
            setTimeout(() => {
              window.location.href = '/login';
            }, 100);
          }
          
          if (showToast) {
            toast({
              title: "登录已过期",
              description: "请重新登录",
              variant: "destructive",
            });
          }
        } else if (showToast) {
          toast({
            title: "请求失败",
            description: errorMessage,
            variant: "destructive",
          });
        }
        
        throw new ApiError(errorMessage, response.status, data);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : '网络连接失败';
      
      if (showToast) {
        toast({
          title: "网络错误",
          description: errorMessage,
          variant: "destructive",
        });
      }
      
      throw new ApiError(errorMessage, 0);
    }
  },

  get: <T = any>(url: string, options?: ApiOptions): Promise<T> => {
    return api.request<T>(url, { ...options, method: 'GET' });
  },

  post: <T = any>(url: string, data?: any, options?: ApiOptions): Promise<T> => {
    return api.request<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  put: <T = any>(url: string, data?: any, options?: ApiOptions): Promise<T> => {
    return api.request<T>(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  delete: <T = any>(url: string, options?: ApiOptions): Promise<T> => {
    return api.request<T>(url, { ...options, method: 'DELETE' });
  },
};

// 导出类型
export { ApiError };
export type { ApiResponse, ApiOptions };

// 具体的API请求函数
export const authApi = {
  /**
   * 用户登录
   */
  async login(password: string, options?: ApiOptions): Promise<LoginResponse> {
    return api.post('/auth/login', { password }, options);
  },
};

// 可以根据需要添加更多API模块
export const repositoryApi = {
  /**
   * 获取仓库列表
   */
  async getRepositories(options?: ApiOptions): Promise<Repository[]> {
    return api.get('/repository', options);
  },

  /**
   * 获取单个仓库详情
   */
  async getRepository(id: string, options?: ApiOptions): Promise<Repository> {
    return api.get(`/repository/${id}`, options);
  },

  /**
   * 获取仓库统计信息
   */
  async getRepositoryStats(options?: ApiOptions): Promise<RepositoryStats> {
    return api.get('/repository/stats', options);
  },

  /**
   * 获取最新提交列表
   */
  async getRecentCommits(limit: number = 10, options?: ApiOptions): Promise<Commit[]> {
    return api.get(`/repository/recent-commits?limit=${limit}`, options);
  },

  /**
   * 根据 owner/name 获取仓库详情
   */
  async getRepositoryByOwnerName(owner: string, name: string, options?: ApiOptions): Promise<Repository> {
    return api.get(`/repository/${owner}/${name}`, options);
  },

  /**
   * 获取仓库分支列表
   */
  async getRepositoryBranches(owner: string, name: string, options?: ApiOptions): Promise<Branch[]> {
    return api.get(`/repository/${owner}/${name}/branches`, options);
  },

  /**
   * 获取仓库提交列表
   */
  async getRepositoryCommits(
    owner: string,
    name: string,
    branch?: string,
    page: number = 1,
    pageSize: number = 20,
    options?: ApiOptions
  ): Promise<{ commits: Commit[], totalCount: number, page: number, pageSize: number, totalPages: number }> {
    const queryParams = new URLSearchParams();
    if (branch) queryParams.append('branch', branch);
    queryParams.append('page', page.toString());
    queryParams.append('pageSize', pageSize.toString());

    return api.get(`/repository/${owner}/${name}/commits?${queryParams.toString()}`, options);
  },

  /**
   * 获取仓库 Git Flow 数据
   */
  async getRepositoryGitFlow(
    owner: string,
    name: string,
    limit: number = 100,
    options?: ApiOptions
  ): Promise<GitFlowData> {
    const queryParams = new URLSearchParams();
    queryParams.append('limit', limit.toString());

    return api.get(`/repository/${owner}/${name}/git-flow?${queryParams.toString()}`, options);
  },

  /**
   * 删除仓库（根据ID）
   */
  async deleteRepository(id: string, options?: ApiOptions): Promise<{ 
    success: boolean; 
    message: string; 
    summary?: {
      repositoryId: string;
      repositoryName: string;
      deletedCommits: number;
      deletedCommitFiles: number;
      deletedAt: string;
    };
  }> {
    return api.delete(`/repository/${id}`, options);
  },

  /**
   * 删除仓库（根据owner/name）
   */
  async deleteRepositoryByOwnerName(owner: string, name: string, options?: ApiOptions): Promise<{ 
    success: boolean; 
    message: string; 
    summary?: {
      repositoryId: string;
      repositoryName: string;
      deletedCommits: number;
      deletedCommitFiles: number;
      deletedAt: string;
    };
  }> {
    return api.delete(`/repository/${owner}/${name}`, options);
  },

  /**
   * 搜索GitHub仓库
   */
  async searchRepositories(query: string, page: number = 1, perPage: number = 30, options?: ApiOptions): Promise<{
    repositories: Repository[];
    totalCount: number;
    page: number;
    perPage: number;
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append('q', query);
    queryParams.append('page', page.toString());
    queryParams.append('per_page', perPage.toString());
    
    return api.get(`/repository/search?${queryParams.toString()}`, options);
  },

  /**
   * 获取用户的GitHub仓库
   */
  async getUserRepositories(page: number = 1, perPage: number = 100, options?: ApiOptions): Promise<Repository[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('per_page', perPage.toString());
    
    return api.get(`/repository/user-repos?${queryParams.toString()}`, options);
  },

  /**
   * 关注仓库
   */
  async followRepository(owner: string, name: string, options?: ApiOptions): Promise<{ success: boolean; message: string; repository?: any }> {
    return api.post('/repository/follow', { owner, name }, options);
  },

  /**
   * 获取已关注的仓库列表
   */
  async getFollowedRepositories(options?: ApiOptions): Promise<string[]> {
    return api.get('/repository/followed', options);
  },
};

export const scanApi = {
  /**
   * 扫描单个仓库（通过owner/name）
   */
  async scanRepositoryByOwnerName(owner: string, name: string, options?: ApiOptions): Promise<{
    success: boolean;
    message: string;
    data?: {
      repositoryId: string;
      owner: string;
      name: string;
      newCommitsCount: number;
      updatedCommitsCount: number;
      scanDuration: number;
      scanTime: string;
    };
    error?: string;
  }> {
    return api.post(`/scan/repository/${owner}/${name}`, undefined, options);
  },

  /**
   * 扫描所有仓库
   */
  async scanAllRepositories(options?: ApiOptions): Promise<{
    success: boolean;
    message: string;
    summary: {
      totalRepositories: number;
      successCount: number;
      failedCount: number;
      totalNewCommits: number;
      totalUpdatedCommits: number;
      totalScanDuration: number;
    };
    details: Array<{
      repositoryId: string;
      owner: string;
      name: string;
      success: boolean;
      errorMessage?: string;
      newCommitsCount: number;
      updatedCommitsCount: number;
      scanDuration: number;
      scanTime: string;
    }>;
  }> {
    return api.post('/scan/all', undefined, options);
  },

  /**
   * 获取扫描日志
   */
  async getScanLogs(page: number = 1, pageSize: number = 20, repositoryId?: string, options?: ApiOptions): Promise<{
    success: boolean;
    data: {
      logs: Array<{
        id: string;
        timestamp: string;
        repository: string;
        status: string;
        message: string;
        duration: number;
        newCommitsCount: number;
        updatedCommitsCount: number;
        errorDetails?: string;
      }>;
      pagination: {
        page: number;
        pageSize: number;
        totalCount: number;
        totalPages: number;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('pageSize', pageSize.toString());
    if (repositoryId) queryParams.append('repositoryId', repositoryId);
    
    return api.get(`/scan/logs?${queryParams.toString()}`, options);
  },
};

export const commitApi = {
  /**
   * 获取提交列表
   */
  async getCommits(repositoryId?: string, page: number = 1, pageSize: number = 20, options?: ApiOptions): Promise<{ commits: Commit[], totalCount: number, page: number, pageSize: number, totalPages: number }> {
    const queryParams = new URLSearchParams();
    if (repositoryId) queryParams.append('repositoryId', repositoryId);
    queryParams.append('page', page.toString());
    queryParams.append('pageSize', pageSize.toString());
    
    return api.get(`/commit?${queryParams.toString()}`, options);
  },

  /**
   * 获取单个提交详情
   */
  async getCommit(hash: string, options?: ApiOptions): Promise<CommitDetail> {
    return api.get(`/commit/${hash}`, options);
  },
};

export const statisticsApi = {
  /**
   * 获取总体统计数据
   */
  async getOverviewStatistics(startDate?: Date, endDate?: Date, options?: ApiOptions): Promise<{
    success: boolean;
    data: {
      totalCommits: number;
      totalRepositories: number;
      totalContributors: number;
      totalCodeLines: number;
      totalFiles: number;
      timeRange: {
        startDate: string;
        endDate: string;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    if (startDate) queryParams.append('startDate', startDate.toISOString());
    if (endDate) queryParams.append('endDate', endDate.toISOString());
    
    return api.get(`/statistics/overview?${queryParams.toString()}`, options);
  },

  /**
   * 获取提交趋势数据
   */
  async getCommitTrends(startDate?: Date, endDate?: Date, options?: ApiOptions): Promise<{
    success: boolean;
    data: Array<{
      date: string;
      commits: number;
      timestamp: string;
    }>;
  }> {
    const queryParams = new URLSearchParams();
    if (startDate) queryParams.append('startDate', startDate.toISOString());
    if (endDate) queryParams.append('endDate', endDate.toISOString());
    
    return api.get(`/statistics/commit-trends?${queryParams.toString()}`, options);
  },

  /**
   * 获取贡献者统计
   */
  async getContributorStatistics(startDate?: Date, endDate?: Date, limit: number = 10, options?: ApiOptions): Promise<{
    success: boolean;
    data: Array<{
      name: string;
      commits: number;
      additions: number;
      deletions: number;
      timestamp: string;
      percentage: number;
      color: string;
    }>;
  }> {
    const queryParams = new URLSearchParams();
    if (startDate) queryParams.append('startDate', startDate.toISOString());
    if (endDate) queryParams.append('endDate', endDate.toISOString());
    queryParams.append('limit', limit.toString());
    
    return api.get(`/statistics/contributors?${queryParams.toString()}`, options);
  },

  /**
   * 获取文件类型统计
   */
  async getFileTypeStatistics(options?: ApiOptions): Promise<{
    success: boolean;
    data: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
  }> {
    return api.get('/statistics/file-types', options);
  },

  /**
   * 获取语言分布统计
   */
  async getLanguageStatistics(options?: ApiOptions): Promise<{
    success: boolean;
    data: Array<{
      name: string;
      value: number;
      color: string;
    }>;
  }> {
    return api.get('/statistics/languages', options);
  },
};
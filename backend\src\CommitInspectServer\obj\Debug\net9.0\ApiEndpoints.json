[{"ContainingType": "CommitInspectServer.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CommitInspectServer.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.CommitController", "Method": "GetCommits", "RelativePath": "api/Commit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "repositoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.CommitController", "Method": "GetCommit", "RelativePath": "api/Commit/{sha}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sha", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepositories", "RelativePath": "api/Repository", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepository", "RelativePath": "api/Repository/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "DeleteRepository", "RelativePath": "api/Repository/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepositoryByOwnerName", "RelativePath": "api/Repository/{owner}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "owner", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "DeleteRepositoryByOwnerName", "RelativePath": "api/Repository/{owner}/{name}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "owner", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepositoryBranches", "RelativePath": "api/Repository/{owner}/{name}/branches", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "owner", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepositoryCommits", "RelativePath": "api/Repository/{owner}/{name}/commits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "owner", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}, {"Name": "branch", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "FollowRepository", "RelativePath": "api/Repository/follow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CommitInspectServer.Controllers.FollowRepositoryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetFollowedRepositories", "RelativePath": "api/Repository/followed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRecentCommits", "RelativePath": "api/Repository/recent-commits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "SearchRepositories", "RelativePath": "api/Repository/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "per_page", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetRepositoryStats", "RelativePath": "api/Repository/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.RepositoryController", "Method": "GetUserRepositories", "RelativePath": "api/Repository/user-repos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "per_page", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.ScanController", "Method": "ScanAllRepositories", "RelativePath": "api/Scan/all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.ScanController", "Method": "GetScanLogs", "RelativePath": "api/Scan/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "repositoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.ScanController", "Method": "GetRepositoryScanStatus", "RelativePath": "api/Scan/repository/{id}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.ScanController", "Method": "ScanRepository", "RelativePath": "api/Scan/repository/{owner}/{name}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "owner", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.StatisticsController", "Method": "GetCommitTrends", "RelativePath": "api/Statistics/commit-trends", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.StatisticsController", "Method": "GetContributorStatistics", "RelativePath": "api/Statistics/contributors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.StatisticsController", "Method": "GetFileTypeStatistics", "RelativePath": "api/Statistics/file-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.StatisticsController", "Method": "GetLanguageStatistics", "RelativePath": "api/Statistics/languages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CommitInspectServer.Controllers.StatisticsController", "Method": "GetOverviewStatistics", "RelativePath": "api/Statistics/overview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}]
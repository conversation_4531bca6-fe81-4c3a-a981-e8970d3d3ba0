import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Settings as SettingsIcon, 
  Clock, 
  Globe, 
  Save,
  RefreshCw,
  Github,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { scanApi } from '@/lib/api';

interface ScanLog {
  id: string;
  timestamp: string;
  repository: string;
  status: string;
  message: string;
  duration: number;
  newCommitsCount: number;
  updatedCommitsCount: number;
  errorDetails?: string;
}

const Settings = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    scanInterval: '5', // 分钟
    enableAutoScan: true,
    githubProxy: 'https://github.com.cnpmjs.org',
    enableProxy: false,
    maxRetries: '3',
    timeout: '30' // 秒
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanLogs, setScanLogs] = useState<ScanLog[]>([]);
  const [isLoadingLogs, setIsLoadingLogs] = useState(false);

  // 加载扫描日志
  const loadScanLogs = async () => {
    setIsLoadingLogs(true);
    try {
      const response = await scanApi.getScanLogs(1, 20);
      if (response.success) {
        setScanLogs(response.data.logs);
      }
    } catch (error) {
      console.error('加载扫描日志失败:', error);
    } finally {
      setIsLoadingLogs(false);
    }
  };

  // 组件加载时获取扫描日志
  useEffect(() => {
    loadScanLogs();
  }, []);

  const handleSave = () => {
    toast({
      title: "设置已保存",
      description: "GitHub 扫描设置已成功保存。",
    });
  };

  const handleInputChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleManualScan = async () => {
    setIsScanning(true);
    toast({
      title: "开始扫描",
      description: "正在扫描所有关注的仓库...",
    });
    
    try {
      const result = await scanApi.scanAllRepositories();
      
      if (result.success) {
        toast({
          title: "扫描完成",
          description: `扫描了 ${result.summary.totalRepositories} 个仓库，发现 ${result.summary.totalNewCommits} 个新提交。`,
        });
        // 重新加载扫描日志
        loadScanLogs();
      } else {
        toast({
          title: "扫描失败",
          description: result.message || "扫描过程中发生错误",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "扫描失败",
        description: "扫描过程中发生网络错误",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-github-success" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-github-danger" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-github-warning" />;
      default:
        return <Activity className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="success">成功</Badge>;
      case 'error':
        return <Badge variant="danger">失败</Badge>;
      case 'warning':
        return <Badge variant="warning">警告</Badge>;
      default:
        return <Badge variant="secondary">未知</Badge>;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <SettingsIcon className="w-8 h-8 text-primary" />
            <div>
              <h2 className="text-3xl font-bold text-foreground">
                GitHub 扫描设置
              </h2>
              <p className="text-muted-foreground mt-1">
                配置 GitHub 仓库扫描的参数和代理设置
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={handleManualScan}
              disabled={isScanning}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isScanning ? 'animate-spin' : ''}`} />
              {isScanning ? '扫描中...' : '立即扫描'}
            </Button>
            <Button onClick={handleSave} className="flex items-center gap-2">
              <Save className="w-4 h-4" />
              保存设置
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 扫描设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                扫描设置
              </CardTitle>
              <CardDescription>
                配置自动扫描 GitHub 仓库的时间间隔和参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">启用自动扫描</Label>
                  <p className="text-sm text-muted-foreground">
                    定期自动检查仓库更新
                  </p>
                </div>
                <Switch
                  checked={settings.enableAutoScan}
                  onCheckedChange={(checked) => handleInputChange('enableAutoScan', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="scanInterval">扫描间隔</Label>
                <Select 
                  value={settings.scanInterval} 
                  onValueChange={(value) => handleInputChange('scanInterval', value)}
                  disabled={!settings.enableAutoScan}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择扫描间隔" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 分钟</SelectItem>
                    <SelectItem value="5">5 分钟</SelectItem>
                    <SelectItem value="10">10 分钟</SelectItem>
                    <SelectItem value="15">15 分钟</SelectItem>
                    <SelectItem value="30">30 分钟</SelectItem>
                    <SelectItem value="60">1 小时</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxRetries">最大重试次数</Label>
                  <Input
                    id="maxRetries"
                    type="number"
                    value={settings.maxRetries}
                    onChange={(e) => handleInputChange('maxRetries', e.target.value)}
                    placeholder="3"
                    min="1"
                    max="10"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeout">超时时间（秒）</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={settings.timeout}
                    onChange={(e) => handleInputChange('timeout', e.target.value)}
                    placeholder="30"
                    min="5"
                    max="300"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 代理设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                代理设置
              </CardTitle>
              <CardDescription>
                配置 GitHub 访问代理，用于网络受限的环境
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">启用代理</Label>
                  <p className="text-sm text-muted-foreground">
                    通过代理服务器访问 GitHub
                  </p>
                </div>
                <Switch
                  checked={settings.enableProxy}
                  onCheckedChange={(checked) => handleInputChange('enableProxy', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="githubProxy">GitHub 代理地址</Label>
                <Input
                  id="githubProxy"
                  value={settings.githubProxy}
                  onChange={(e) => handleInputChange('githubProxy', e.target.value)}
                  placeholder="https://github.com.cnpmjs.org"
                  disabled={!settings.enableProxy}
                />
                <p className="text-xs text-muted-foreground">
                  常用代理：github.com.cnpmjs.org, hub.fastgit.xyz
                </p>
              </div>

              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-start gap-2">
                  <Github className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="text-xs text-muted-foreground">
                    <p className="font-medium mb-1">代理说明</p>
                    <p>代理服务器将替换 github.com 域名，用于解决网络访问问题。请确保代理服务器稳定可靠。</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 扫描日志 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              扫描日志
            </CardTitle>
            <CardDescription>
              查看最近的 GitHub 仓库扫描记录和状态
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {isLoadingLogs ? (
                  <div className="text-center text-muted-foreground py-8">
                    加载中...
                  </div>
                ) : scanLogs.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    暂无扫描记录
                  </div>
                ) : (
                  scanLogs.map((log) => (
                    <div key={log.id} className="flex items-start gap-4 p-3 rounded-lg border bg-card/50">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(log.status)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between gap-4 mb-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{log.repository}</span>
                          {getStatusBadge(log.status)}
                        </div>
                        <span className="text-xs text-muted-foreground whitespace-nowrap">
                          {log.timestamp}
                        </span>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-1">
                        {log.message}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>耗时: {log.duration}ms</span>
                        {log.newCommitsCount > 0 && <span>新增: {log.newCommitsCount} 个提交</span>}
                        {log.updatedCommitsCount > 0 && <span>更新: {log.updatedCommitsCount} 个提交</span>}
                      </div>
                    </div>
                  </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Settings;
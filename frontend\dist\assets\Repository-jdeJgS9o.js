import{j as e,C as F,a as O,L as X,B as G,G as ue,d as fe,e as pe,f as Ne,r as w,t as P,g as xe}from"./index-D3FXQGgv.js";import{r as o,j as be,i as he,L as U}from"./react-vendor-DucYQ77b.js";import{B as _}from"./badge-BHFrTODj.js";import{S as je,a as ge,b as ve,c as ye,d as Ce}from"./select-CsIt0xHD.js";import{A as Q,a as Z}from"./avatar-wZOQCpPA.js";import{T as Pe,a as De,b as Y,c as I}from"./tabs-CYmfXndi.js";import{_ as q,Z as J,ag as K,ah as Ee,ai as W,a6 as Ve,aj as Re}from"./utils-CUkY6B8V.js";import"./radix-ui-DdAvwPj2.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const k=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#f97316","#06b6d4","#84cc16","#ec4899","#6b7280"],Ge=({commits:n,onCommitClick:i,className:N})=>{const j=o.useRef(null),g=o.useRef(null),{nodes:S,connections:v,branchMap:L}=o.useMemo(()=>{if(!n.length)return{nodes:[],connections:[],branchMap:new Map};new Map(n.map(r=>[r.hash,r]));const t=new Map;let d=0;n.forEach(r=>{r.branches&&r.branches.forEach(f=>{t.has(f)||t.set(f,{color:k[d%k.length],lane:d++})})}),t.size===0&&t.set("main",{color:k[0],lane:0});const u=80,V=60,x=n.map((r,f)=>{var M;const p=((M=r.branches)==null?void 0:M[0])||"main",h=t.get(p)||t.get("main");return{commit:r,x:h.lane*V+30,y:f*u+40,branch:p,color:h.color,parents:r.parents||[],children:r.children||[]}}),y=[];return x.forEach(r=>{r.parents.forEach(f=>{const p=x.find(h=>h.commit.hash===f);p&&y.push({from:p,to:r,fromX:p.x,fromY:p.y+15,toX:r.x,toY:r.y-15,color:r.color})})}),{nodes:x,connections:y,branchMap:t}},[n]),l=t=>new Date(t).toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),D=t=>{switch(t.split(":")[0].toLowerCase()){case"feat":return"success";case"fix":return"danger";case"docs":return"warning";case"refactor":return"modified";default:return"default"}},$=Math.max(400,L.size*60+60),E=Math.max(200,n.length*80+80);return e.jsxDEV("div",{className:N,children:e.jsxDEV(F,{children:e.jsxDEV(O,{className:"p-0",children:e.jsxDEV("div",{ref:g,className:"relative overflow-auto",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0 bg-gray-50 border-r",children:e.jsxDEV("svg",{ref:j,width:$,height:E,className:"block",children:[v.map((t,d)=>e.jsxDEV("g",{children:t.fromX===t.toX?e.jsxDEV("line",{x1:t.fromX,y1:t.fromY,x2:t.toX,y2:t.toY,stroke:t.color,strokeWidth:"2",className:"opacity-60"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:166,columnNumber:25},void 0):e.jsxDEV("path",{d:`M ${t.fromX} ${t.fromY} Q ${(t.fromX+t.toX)/2} ${(t.fromY+t.toY)/2} ${t.toX} ${t.toY}`,fill:"none",stroke:t.color,strokeWidth:"2",className:"opacity-60"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:177,columnNumber:25},void 0)},d,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:163,columnNumber:21},void 0)),S.map((t,d)=>e.jsxDEV("g",{children:[e.jsxDEV("circle",{cx:t.x,cy:t.y,r:"8",fill:t.color,stroke:"white",strokeWidth:"2",className:"cursor-pointer hover:r-10 transition-all",onClick:()=>i==null?void 0:i(t.commit)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:191,columnNumber:23},void 0),e.jsxDEV("text",{x:t.x+15,y:t.y+4,fontSize:"12",fill:"#374151",className:"font-mono",children:t.commit.hash.substring(0,7)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:201,columnNumber:23},void 0)]},t.commit.hash,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:190,columnNumber:21},void 0))]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:155,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:154,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex-1 min-w-0",children:n.map((t,d)=>e.jsxDEV("div",{className:"border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer transition-colors",style:{minHeight:"80px"},onClick:()=>i==null?void 0:i(t),children:e.jsxDEV("div",{className:"flex items-start gap-3",children:[e.jsxDEV(Q,{className:"w-8 h-8 mt-1",children:e.jsxDEV(Z,{className:"text-xs",children:t.author.split(" ").map(u=>u[0]).join("")},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:226,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:225,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-start md:justify-between gap-2",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("p",{className:"font-medium text-gray-900 text-sm leading-tight line-clamp-2",children:t.message},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:234,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-3 mt-1 text-xs text-gray-500",children:[e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(q,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:239,columnNumber:33},void 0),t.author]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:238,columnNumber:31},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(J,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:243,columnNumber:33},void 0),l(t.createdAt)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:242,columnNumber:31},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(K,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:247,columnNumber:33},void 0),e.jsxDEV("code",{className:"text-xs",children:t.hash.substring(0,8)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:248,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:246,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:237,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:233,columnNumber:27},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:e.jsxDEV(_,{variant:D(t.message),className:"text-xs",children:t.message.split(":")[0]},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:254,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:253,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:232,columnNumber:25},void 0),(t.additions||t.deletions||t.filesChanged)&&e.jsxDEV("div",{className:"flex items-center gap-3 text-xs mt-2",children:[e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV("span",{className:"text-gray-500",children:"文件:"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:263,columnNumber:31},void 0),e.jsxDEV("span",{className:"font-medium",children:t.filesChanged||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:264,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:262,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV("span",{className:"text-green-600",children:["+",t.additions||0]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:267,columnNumber:31},void 0),e.jsxDEV("span",{className:"text-red-600",children:["-",t.deletions||0]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:268,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:266,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:261,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:231,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:224,columnNumber:21},void 0)},t.hash,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:218,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:216,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:152,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:151,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:150,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:149,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitGraph.tsx",lineNumber:148,columnNumber:5},void 0)},Fe=()=>{const{owner:n,name:i}=be(),[N,j]=he(),[g,S]=o.useState(null),[v,L]=o.useState([]),[l,D]=o.useState("main"),[$,E]=o.useState([]),[t,d]=o.useState([]),[u,V]=o.useState(!1),[x,y]=o.useState(!1),[r,f]=o.useState(!0),[p,h]=o.useState(1),[M,ee]=o.useState(1),[A,se]=o.useState(!1),[C,H]=o.useState(!1),[a,B]=o.useState("branch"),te=async()=>{if(!(!n||!i))try{const s=await w.getRepositoryByOwnerName(n,i);S(s)}catch(s){console.error("Failed to load repository:",s)}},ne=async()=>{if(!(!n||!i))try{const s=await w.getRepositoryBranches(n,i);L(s);const c=N.get("branch"),m=N.get("view");let b="main";c&&s.some(de=>de.name===c)?b=c:s.length>0&&(b=s[0].name),b!==l&&D(b),m&&["branch","graph"].includes(m)&&m!==a&&B(m);const z=new URLSearchParams(N);z.set("branch",b),z.set("view",a),j(z,{replace:!0})}catch(s){console.error("Failed to load branches:",s)}},R=async(s=1,c=l)=>{if(!(!n||!i))try{V(!0);const m=await w.getRepositoryCommits(n,i,c,s,20);E(s===1?m.commits:b=>[...b,...m.commits]),h(s),ee(m.totalPages),se(s<m.totalPages)}catch(m){console.error("Failed to load commits:",m),P({title:"加载失败",description:"无法加载提交记录",variant:"destructive"})}finally{V(!1)}},T=async()=>{if(!(!n||!i))try{y(!0);const s=await w.getRepositoryGitGraph(n,i,100);d(s.commits)}catch(s){console.error("Failed to load git graph:",s),P({title:"加载失败",description:"无法加载 Git 图表数据",variant:"destructive"}),B("branch")}finally{y(!1)}};o.useEffect(()=>{(async()=>{f(!0),await Promise.all([te(),ne()]),f(!1)})()},[n,i]),o.useEffect(()=>{!r&&v.length>0&&a==="branch"&&R(1,l)},[l,r,v.length,a]),o.useEffect(()=>{!r&&a==="graph"&&T()},[a,r]);const re=()=>{a==="branch"?R(1):T()},ie=s=>{D(s);const c=new URLSearchParams(N);c.set("branch",s),c.set("view",a),j(c,{replace:!0})},oe=()=>{A&&!u&&R(p+1)},ce=async()=>{if(!(!n||!i||C))try{H(!0);const s=await xe.scanRepositoryByOwnerName(n,i);s.success&&s.data?(P({title:"扫描完成",description:`发现 ${s.data.newCommitsCount} 个新提交，用时 ${s.data.scanDuration.toFixed(2)} 秒`}),s.data.newCommitsCount>0&&(a==="branch"?await R(1):await T())):P({title:"扫描失败",description:s.error||"扫描时发生未知错误",variant:"destructive"})}catch(s){console.error("Failed to scan repository:",s),P({title:"扫描失败",description:"请稍后重试",variant:"destructive"})}finally{H(!1)}},ae=s=>{window.location.href=`/commit/${s.hash}?from=${encodeURIComponent(`/repository/${n}/${i}?view=${a}&branch=${l}`)}`};if(r)return e.jsxDEV(X,{children:e.jsxDEV("div",{className:"flex items-center justify-center h-64",children:e.jsxDEV("div",{className:"w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:235,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:234,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:233,columnNumber:7},void 0);if(!g)return e.jsxDEV(X,{children:e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("h2",{className:"text-2xl font-bold text-foreground mb-2",children:"仓库不存在"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:245,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-muted-foreground mb-4",children:["无法找到仓库 ",n,"/",i]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:246,columnNumber:11},void 0),e.jsxDEV(G,{asChild:!0,children:e.jsxDEV(U,{to:"/dashboard",children:"返回仪表盘"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:248,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:247,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:244,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:243,columnNumber:7},void 0);const me=s=>new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),le=s=>{switch(s.split(":")[0].toLowerCase()){case"feat":return"success";case"fix":return"danger";case"docs":return"warning";case"refactor":return"modified";default:return"default"}};return e.jsxDEV(X,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex items-center justify-between gap-4",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(ue,{className:"w-6 h-6 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:283,columnNumber:13},void 0),e.jsxDEV("h1",{className:"text-2xl font-bold text-foreground",children:[g.owner,"/",g.name]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:284,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:282,columnNumber:11},void 0),e.jsxDEV(G,{variant:"default",size:"sm",onClick:ce,disabled:C||u,className:"flex items-center gap-2",children:[C?e.jsxDEV("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:297,columnNumber:15},void 0):e.jsxDEV(Ee,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:299,columnNumber:15},void 0),C?"扫描中...":"立即扫描"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:289,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:281,columnNumber:9},void 0),e.jsxDEV(F,{children:e.jsxDEV(fe,{children:e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[e.jsxDEV("div",{children:[e.jsxDEV(pe,{className:"text-lg",children:"提交历史"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:310,columnNumber:17},void 0),e.jsxDEV(Ne,{children:a==="branch"?`查看 ${l} 分支的最新提交记录`:"查看所有分支的 Git 流图"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:311,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:309,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex items-center gap-3",children:[a==="branch"&&e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV(W,{className:"w-4 h-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:322,columnNumber:21},void 0),e.jsxDEV(je,{value:l,onValueChange:ie,children:[e.jsxDEV(ge,{className:"w-40",children:e.jsxDEV(ve,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:325,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:324,columnNumber:23},void 0),e.jsxDEV(ye,{children:v.map(s=>e.jsxDEV(Ce,{value:s.name,children:s.name},s.name,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:329,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:327,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:323,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:321,columnNumber:19},void 0),e.jsxDEV(G,{variant:"outline",size:"sm",onClick:re,disabled:(a==="branch"?u:x)||C,children:[(a==="branch"?u:x)?e.jsxDEV("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:345,columnNumber:21},void 0):e.jsxDEV(Ve,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:347,columnNumber:21},void 0),"刷新"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:338,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:319,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:308,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:307,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:306,columnNumber:9},void 0),e.jsxDEV(Pe,{value:a,onValueChange:s=>{const c=s;B(c);const m=new URLSearchParams(N);m.set("view",c),m.set("branch",l),j(m,{replace:!0})},children:[e.jsxDEV(De,{className:"grid w-full grid-cols-2 max-w-md",children:[e.jsxDEV(Y,{value:"branch",className:"flex items-center gap-2",children:[e.jsxDEV(W,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:368,columnNumber:15},void 0),"分支视图"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:367,columnNumber:13},void 0),e.jsxDEV(Y,{value:"graph",className:"flex items-center gap-2",children:[e.jsxDEV(Re,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:372,columnNumber:15},void 0),"Git 流图"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:371,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:366,columnNumber:11},void 0),e.jsxDEV(I,{value:"branch",className:"space-y-6",children:[e.jsxDEV("div",{className:"space-y-6",children:$.map(s=>e.jsxDEV(U,{to:`/commit/${s.hash}?from=${encodeURIComponent(`/repository/${n}/${i}?view=branch&branch=${l}`)}`,children:e.jsxDEV(F,{className:"hover:shadow-elegant transition-all duration-300 group cursor-pointer",children:e.jsxDEV(O,{className:"p-6",children:e.jsxDEV("div",{className:"flex items-start gap-4",children:[e.jsxDEV(Q,{className:"w-10 h-10",children:e.jsxDEV(Z,{children:s.author.split(" ").map(c=>c[0]).join("")},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:386,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:385,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-start md:justify-between gap-2 mb-3",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("p",{className:"font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2",children:s.message},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:394,columnNumber:31},void 0),e.jsxDEV("div",{className:"flex items-center gap-4 mt-2 text-sm text-muted-foreground",children:[e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(q,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:399,columnNumber:35},void 0),s.author]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:398,columnNumber:33},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(J,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:403,columnNumber:35},void 0),me(s.createdAt)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:402,columnNumber:33},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV(K,{className:"w-3 h-3"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:407,columnNumber:35},void 0),e.jsxDEV("code",{className:"text-xs",children:s.hash.substring(0,8)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:408,columnNumber:35},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:406,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:397,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:393,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-2",children:e.jsxDEV(_,{variant:le(s.message),children:s.message.split(":")[0]},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:414,columnNumber:31},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:413,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:392,columnNumber:27},void 0),e.jsxDEV("div",{className:"flex items-center gap-4 text-sm",children:[e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV("span",{className:"text-muted-foreground",children:"文件:"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:422,columnNumber:31},void 0),e.jsxDEV("span",{className:"font-medium",children:s.filesChanged||0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:423,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:421,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-1",children:[e.jsxDEV("span",{className:"text-green-600",children:["+",s.additions||0]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:426,columnNumber:31},void 0),e.jsxDEV("span",{className:"text-red-600",children:["-",s.deletions||0]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:427,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:425,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:420,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:391,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:384,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:383,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:382,columnNumber:19},void 0)},s.hash,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:381,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:379,columnNumber:13},void 0),A&&e.jsxDEV("div",{className:"text-center",children:e.jsxDEV(G,{variant:"outline",size:"lg",onClick:oe,disabled:u,children:u?e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:449,columnNumber:23},void 0),"加载中..."]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:448,columnNumber:21},void 0):"加载更多提交"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:441,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:440,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:377,columnNumber:11},void 0),e.jsxDEV(I,{value:"graph",className:"space-y-6",children:x?e.jsxDEV("div",{className:"flex items-center justify-center h-64",children:e.jsxDEV("div",{className:"w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:463,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:462,columnNumber:15},void 0):e.jsxDEV(Ge,{commits:t,onCommitClick:ae,className:"w-full"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:466,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:460,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:357,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:279,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Repository.tsx",lineNumber:278,columnNumber:5},void 0)};export{Fe as default};

import{j as t,c as n}from"./index-D3FXQGgv.js";import{r as o}from"./react-vendor-DucYQ77b.js";import{Y as a,Z as r,_ as c,$ as m}from"./radix-ui-DdAvwPj2.js";const g=m,f=o.forwardRef(({className:e,...s},i)=>t.jsxDEV(a,{ref:i,className:n("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/tabs.tsx",lineNumber:12,columnNumber:3},void 0));f.displayName=a.displayName;const d=o.forwardRef(({className:e,...s},i)=>t.jsxDEV(r,{ref:i,className:n("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/tabs.tsx",lineNumber:27,columnNumber:3},void 0));d.displayName=r.displayName;const l=o.forwardRef(({className:e,...s},i)=>t.jsxDEV(c,{ref:i,className:n("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/tabs.tsx",lineNumber:42,columnNumber:3},void 0));l.displayName=c.displayName;export{g as T,f as a,d as b,l as c};

// 认证相关类型
export interface LoginRequest {
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  message?: string;
}

// 仓库相关类型
export interface Repository {
  id: string;
  owner: string;
  name: string;
  description?: string;
  stars: number;
  language?: string;
  githubUrl?: string;
  createdAt: string;
  updatedAt: string;
  commitCount: number;
  isActive: boolean;
  lastCommitTime?: string;
  lastCommitMessage?: string;
}

// 提交相关类型
export interface Commit {
  hash: string;
  message: string;
  author: string;
  authorEmail?: string;
  createdAt: string;
  additions?: number;
  deletions?: number;
  filesChanged?: number;
  repository?: {
    owner: string;
    name: string;
  };
}

// Git Flow 提交类型（包含父子关系）
export interface GitFlowCommit {
  id: string;
  hash: string;
  message: string;
  author: string;
  authorEmail?: string;
  createdAt: string;
  additions?: number;
  deletions?: number;
  filesChanged?: number;
  parents: Array<{
    id: string;
    hash: string;
  }>;
  children: Array<{
    id: string;
    hash: string;
  }>;
}

// Git Flow 数据类型
export interface GitFlowData {
  commits: GitFlowCommit[];
  branches: Array<{
    name: string;
    headCommitHash?: string;
  }>;
  tags: Array<{
    name: string;
    isAnnotated: boolean;
    annotationMessage?: string;
    commitHash: string;
  }>;
}

// 提交文件相关类型
export interface CommitFile {
  filename: string;
  status: 'added' | 'modified' | 'deleted';
  additions: number;
  deletions: number;
  changes: number;
  patch?: string;
}

// 提交详情类型
export interface CommitDetail {
  hash: string;
  message: string;
  author: {
    name: string;
    email?: string;
    username?: string;
  };
  date: string;
  stats: {
    filesChanged: number;
    additions: number;
    deletions: number;
  };
  repository?: {
    owner: string;
    name: string;
  };
  files: CommitFile[];
}

// 分支相关类型
export interface Branch {
  name: string;
  headCommitHash?: string;
}

// 标签相关类型
export interface Tag {
  id: string;
  name: string;
  isAnnotated: boolean;
  annotationMessage?: string;
  commitHash: string;
}

// 统计信息类型
export interface RepositoryStats {
  totalRepositories: number;
  activeRepositories: number;
  totalStars: number;
  totalCommits: number;
}
import React, { useEffect, Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import Login from "./pages/Login";

// Lazy load components for code splitting
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Statistics = lazy(() => import("./pages/Statistics"));
const Settings = lazy(() => import("./pages/Settings"));
const SearchResults = lazy(() => import("./pages/SearchResults"));
const Repository = lazy(() => import("./pages/Repository"));
const Commit = lazy(() => import("./pages/Commit"));
const FileDiff = lazy(() => import("./pages/FileDiff"));
const NotFound = lazy(() => import("./pages/NotFound"));
const GitFlowDemo = lazy(() => import("./pages/GitFlowDemo"));

const queryClient = new QueryClient();

const App = () => {
  // 在组件挂载时将 dark 类添加到 document.documentElement
  useEffect(() => {
    document.documentElement.classList.add('dark');

    // 清理函数：在组件卸载时移除 dark 类
    return () => {
      document.documentElement.classList.remove('dark');
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <div>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div>}>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/statistics" element={
                    <ProtectedRoute>
                      <Statistics />
                    </ProtectedRoute>
                  } />
                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <Settings />
                    </ProtectedRoute>
                  } />
                  <Route path="/search" element={
                    <ProtectedRoute>
                      <SearchResults />
                    </ProtectedRoute>
                  } />
                  <Route path="/repository/:owner/:name" element={
                    <ProtectedRoute>
                      <Repository />
                    </ProtectedRoute>
                  } />
                  <Route path="/commit/:hash" element={
                    <ProtectedRoute>
                      <Commit />
                    </ProtectedRoute>
                  } />
                  <Route path="/commit/:hash/file/:filename" element={
                    <ProtectedRoute>
                      <FileDiff />
                    </ProtectedRoute>
                  } />
                  <Route path="/test" element={
                    <ProtectedRoute>
                      <GitFlowDemo />
                    </ProtectedRoute>
                  } />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </BrowserRouter>
          </div>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;

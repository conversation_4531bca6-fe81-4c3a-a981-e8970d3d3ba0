import React, { useEffect, useRef, useState, useMemo } from 'react';
import type { GitFlowCommit } from '@/types/api';

interface GitFlowGraphProps {
  commits: GitFlowCommit[];
  branches: Array<{
    name: string;
    headCommitHash?: string;
  }>;
  tags: Array<{
    name: string;
    isAnnotated: boolean;
    annotationMessage?: string;
    commitHash: string;
  }>;
  onCommitClick?: (commit: GitFlowCommit) => void;
}

interface ProcessedCommit extends GitFlowCommit {
  x: number;
  y: number;
  branch: string;
  showBranch: boolean;
  commitTags: string[];
}

const GitFlowGraph: React.FC<GitFlowGraphProps> = ({ 
  commits, 
  branches, 
  tags, 
  onCommitClick 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredCommit, setHoveredCommit] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const CONFIG = {
    padding: 40,
    nodeRadius: 5,
    nodeSpacingX: 20,
    nodeSpacingY: 30,
    maxMessageLength: 50,
    minCanvasWidth: 600,
    extraBuffer: 50,
    labelPadding: 4,
    labelHeight: 16,
    labelOffset: 10,
    highlightRadius: 4
  };

  const colorPalette = [
    '#ff6b6b', '#4ecdc4', '#feca57', '#5f27cd', '#00d2d3',
    '#1dd1a1', '#c8d6e5', '#ff9ff3', '#48dbfb', '#576574'
  ];

  // 创建分支到颜色的映射
  const branchColorMap = useMemo(() => {
    const map = new Map<string, string>();
    let colorIndex = 0;
    
    // 首先为主分支分配颜色
    const mainBranches = ['main', 'master'];
    mainBranches.forEach(branch => {
      if (branches.some(b => b.name === branch)) {
        map.set(branch, colorPalette[0]);
        colorIndex = 1;
      }
    });
    
    // 为其他分支分配颜色
    branches.forEach(branch => {
      if (!map.has(branch.name)) {
        map.set(branch.name, colorPalette[colorIndex % colorPalette.length]);
        colorIndex++;
      }
    });
    
    return map;
  }, [branches]);

  // 处理 commits 数据，计算位置和分支信息
  const processedCommits = useMemo(() => {
    if (!commits.length) return [];

    // 创建 commit hash 到分支的映射
    const commitToBranch = new Map<string, string>();
    
    // 简单的分支分配逻辑：根据时间顺序和父子关系推断分支
    const branchColumns = new Map<string, number>();
    let nextColumn = 0;
    
    // 为主分支分配第0列
    const mainBranch = branches.find(b => b.name === 'main' || b.name === 'master')?.name || 'main';
    branchColumns.set(mainBranch, 0);
    nextColumn = 1;
    
    // 按时间倒序处理 commits（最新的在前）
    const sortedCommits = [...commits].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    // 简单的分支推断：如果没有父节点，分配到主分支；如果有多个父节点，可能是合并提交
    sortedCommits.forEach((commit, index) => {
      let assignedBranch = mainBranch;
      
      // 如果是合并提交（多个父节点），分配到主分支
      if (commit.parents.length > 1) {
        assignedBranch = mainBranch;
      } else if (commit.parents.length === 1) {
        // 如果有一个父节点，尝试继承父节点的分支
        const parentHash = commit.parents[0].hash;
        const parentBranch = commitToBranch.get(parentHash);
        if (parentBranch) {
          assignedBranch = parentBranch;
        }
      }
      
      // 如果是分支的头提交，使用对应的分支名
      const headBranch = branches.find(b => b.headCommitHash === commit.hash);
      if (headBranch) {
        assignedBranch = headBranch.name;
      }
      
      commitToBranch.set(commit.hash, assignedBranch);
      
      // 确保分支有对应的列
      if (!branchColumns.has(assignedBranch)) {
        branchColumns.set(assignedBranch, nextColumn++);
      }
    });

    // 计算每个分支的头提交
    const branchHeads = new Map<string, string>();
    branches.forEach(branch => {
      if (branch.headCommitHash) {
        branchHeads.set(branch.name, branch.headCommitHash);
      }
    });

    // 创建处理后的 commits
    return sortedCommits.map((commit, index) => {
      const branch = commitToBranch.get(commit.hash) || mainBranch;
      const commitTags = tags.filter(tag => tag.commitHash === commit.hash).map(tag => tag.name);
      
      return {
        ...commit,
        x: branchColumns.get(branch) || 0,
        y: index,
        branch,
        showBranch: branchHeads.get(branch) === commit.hash,
        commitTags
      } as ProcessedCommit;
    });
  }, [commits, branches, tags]);

  const getBranchColor = (branch: string) => branchColorMap.get(branch) || colorPalette[0];
  
  const truncateText = (text: string, maxLength: number) => 
    text.length > maxLength ? text.substring(0, maxLength) + '...' : text;

  const getNodePosition = (commit: ProcessedCommit) => ({
    x: CONFIG.padding + commit.x * CONFIG.nodeSpacingX,
    y: CONFIG.padding + commit.y * CONFIG.nodeSpacingY
  });

  const drawLabel = (
    ctx: CanvasRenderingContext2D, 
    text: string, 
    x: number, 
    y: number, 
    bgColor: string, 
    borderColor: string, 
    textColor: string
  ) => {
    const width = ctx.measureText(text).width + CONFIG.labelPadding * 2;
    ctx.fillStyle = bgColor;
    ctx.fillRect(x, y - 8, width, CONFIG.labelHeight);
    ctx.strokeStyle = borderColor;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y - 8, width, CONFIG.labelHeight);
    ctx.fillStyle = textColor;
    ctx.fillText(text, x + CONFIG.labelPadding, y + 3);
    return width;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !processedCommits.length) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const dpr = window.devicePixelRatio || 1;

    const maxX = Math.max(...processedCommits.map(c => c.x));
    const maxY = Math.max(...processedCommits.map(c => c.y));

    ctx.font = '12px sans-serif';
    let maxTextWidth = 0;
    processedCommits.forEach(commit => {
      let textWidth = CONFIG.nodeRadius + CONFIG.labelOffset;
      if (commit.showBranch) {
        textWidth += ctx.measureText(commit.branch).width + CONFIG.labelPadding * 2 + 5;
      }
      if (commit.commitTags?.length > 0) {
        commit.commitTags.forEach(tag => {
          textWidth += ctx.measureText(tag).width + CONFIG.labelPadding * 2 + 5;
        });
      }
      const displayMessage = truncateText(commit.message, CONFIG.maxMessageLength);
      textWidth += ctx.measureText(displayMessage).width;
      maxTextWidth = Math.max(maxTextWidth, textWidth);
    });

    const logicalWidth = Math.max(CONFIG.minCanvasWidth,
      CONFIG.padding * 2 + maxX * CONFIG.nodeSpacingX + maxTextWidth + CONFIG.extraBuffer);
    const logicalHeight = CONFIG.padding * 2 + maxY * CONFIG.nodeSpacingY + CONFIG.extraBuffer;

    canvas.width = logicalWidth * dpr;
    canvas.height = logicalHeight * dpr;
    canvas.style.width = `${logicalWidth}px`;
    canvas.style.height = `${logicalHeight}px`;
    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);

    ctx.clearRect(0, 0, logicalWidth, logicalHeight);

    // 绘制连接线
    processedCommits.forEach(commit => {
      const pos = getNodePosition(commit);
      const branchColor = getBranchColor(commit.branch);
      
      commit.parents.forEach(parent => {
        const parentCommit = processedCommits.find(c => c.hash === parent.hash);
        if (parentCommit) {
          const parentPos = getNodePosition(parentCommit);
          ctx.beginPath();
          ctx.strokeStyle = branchColor + '80';
          ctx.lineWidth = 3;
          const midY = parentPos.y + (pos.y - parentPos.y) * 0.5;
          ctx.moveTo(parentPos.x, parentPos.y);
          ctx.bezierCurveTo(parentPos.x, midY, pos.x, midY, pos.x, pos.y);
          ctx.stroke();
        }
      });
    });

    // 绘制节点和标签
    processedCommits.forEach(commit => {
      const pos = getNodePosition(commit);
      const branchColor = getBranchColor(commit.branch);

      // 绘制节点外圈
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, CONFIG.nodeRadius + 2, 0, 2 * Math.PI);
      ctx.fillStyle = '#1e1e2f';
      ctx.fill();

      // 绘制节点
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, CONFIG.nodeRadius, 0, 2 * Math.PI);
      ctx.fillStyle = branchColor;
      ctx.fill();

      // 绘制悬停高亮
      if (hoveredCommit === commit.hash) {
        ctx.beginPath();
        ctx.arc(pos.x, pos.y, CONFIG.nodeRadius + CONFIG.highlightRadius, 0, 2 * Math.PI);
        ctx.strokeStyle = branchColor;
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      ctx.font = '11px sans-serif';
      let labelX = pos.x + CONFIG.nodeRadius + CONFIG.labelOffset;

      // 绘制分支标签
      if (commit.showBranch) {
        const width = drawLabel(ctx, commit.branch, labelX, pos.y, branchColor + '40', branchColor, branchColor);
        labelX += width + 5;
      }

      // 绘制标签
      if (commit.commitTags?.length > 0) {
        commit.commitTags.forEach(tag => {
          const width = drawLabel(ctx, tag, labelX, pos.y, '#f1c40f40', '#f1c40f', '#f39c12');
          labelX += width + 5;
        });
      }

      // 绘制提交消息
      ctx.font = '12px sans-serif';
      const displayMessage = truncateText(commit.message, CONFIG.maxMessageLength);
      drawLabel(ctx, displayMessage, labelX, pos.y, '#2d2f3a80', '#444c5a', '#f1f1f1');
    });
  }, [hoveredCommit, processedCommits, getBranchColor]);

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    setMousePosition({ x: e.clientX, y: e.clientY });

    const foundCommit = processedCommits.find(commit => {
      const pos = getNodePosition(commit);
      const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
      return distance <= CONFIG.nodeRadius + 3;
    });

    setHoveredCommit(foundCommit?.hash || null);
  };

  const handleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!onCommitClick) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const foundCommit = processedCommits.find(commit => {
      const pos = getNodePosition(commit);
      const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
      return distance <= CONFIG.nodeRadius + 3;
    });

    if (foundCommit) {
      onCommitClick(foundCommit);
    }
  };

  const hoveredCommitData = hoveredCommit ? processedCommits.find(c => c.hash === hoveredCommit) : null;

  return (
    <div className="relative">
      <canvas
        ref={canvasRef}
        className="border border-border rounded cursor-pointer bg-background"
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setHoveredCommit(null)}
        onClick={handleClick}
      />
      {hoveredCommitData && (() => {
        const containerRect = canvasRef.current?.getBoundingClientRect();
        if (!containerRect) return null;
        
        const relativeY = mousePosition.y - containerRect.top;
        const showAbove = relativeY > containerRect.height / 2;

        return (
          <div
            className="absolute z-10 bg-popover rounded-lg shadow-xl border border-border p-4 pointer-events-none transition-all duration-200"
            style={{
              left: `${mousePosition.x - containerRect.left + 40}px`,
              top: showAbove
                ? `${mousePosition.y - containerRect.top - 10}px`
                : `${mousePosition.y - containerRect.top + 40}px`,
              transform: showAbove ? 'translateY(-100%)' : 'translateY(0%)',
              minWidth: '200px',
              maxWidth: '300px'
            }}
          >
            <div className="flex items-center space-x-2 mb-2">
              <span
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: getBranchColor(hoveredCommitData.branch) }}
              />
              <span className="text-xs px-2 py-1 rounded-full bg-muted text-muted-foreground">
                {hoveredCommitData.branch}
              </span>
            </div>
            <p className="text-sm text-foreground mb-2">{hoveredCommitData.message}</p>
            <div className="text-xs text-muted-foreground mb-2">
              <div>作者: {hoveredCommitData.author}</div>
              <div>时间: {new Date(hoveredCommitData.createdAt).toLocaleString('zh-CN')}</div>
              <div>Hash: <code className="text-xs">{hoveredCommitData.hash.substring(0, 8)}</code></div>
            </div>
            {hoveredCommitData.commitTags?.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {hoveredCommitData.commitTags.map((tag, index) => (
                  <span
                    key={index}
                    className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 border border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-700"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
            {hoveredCommitData.parents.length > 0 && (
              <div className="text-xs text-muted-foreground border-t border-border pt-2 mt-2">
                <span className="font-semibold">Parents:</span> {hoveredCommitData.parents.map(p => p.hash.substring(0, 8)).join(', ')}
              </div>
            )}
            {hoveredCommitData.parents.length === 0 && (
              <div className="text-xs text-muted-foreground border-t border-border pt-2">
                <span className="italic">Root commit (最早的提交)</span>
              </div>
            )}
            {hoveredCommitData.parents.length > 1 && (
              <div className="text-xs text-green-600 mt-1 dark:text-green-400">
                <span className="font-semibold">✓ Merge commit</span>
              </div>
            )}
          </div>
        );
      })()}
    </div>
  );
};

export default GitFlowGraph;

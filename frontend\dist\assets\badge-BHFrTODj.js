import{j as o,c as a}from"./index-D3FXQGgv.js";import{M as n}from"./utils-CUkY6B8V.js";const d=n("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-github-success text-white shadow",warning:"border-transparent bg-github-warning text-github-dark shadow",danger:"border-transparent bg-github-danger text-white shadow",added:"border-transparent bg-code-added text-code-addition shadow",removed:"border-transparent bg-code-removed text-code-deletion shadow",modified:"border-transparent bg-code-modified text-black shadow"}},defaultVariants:{variant:"default"}});function c({className:e,variant:r,...t}){return o.jsxDEV("div",{className:a(d({variant:r}),e),...t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/badge.tsx",lineNumber:35,columnNumber:5},this)}export{c as B};

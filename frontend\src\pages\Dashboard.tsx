import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { GitBranch, Star, Clock, Search, Activity, GitCommit, User, X } from 'lucide-react';
import { GitHubLogo } from '@/components/GitHubLogo';
import { repositoryApi } from '@/lib/api';
import type { Repository, Commit, RepositoryStats } from '@/types/api';
import { useToast } from '@/hooks/use-toast';

const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return `${diffInSeconds} 秒前`;
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} 分钟前`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} 小时前`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} 天前`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} 个月前`;
  return `${Math.floor(diffInSeconds / 31536000)} 年前`;
};

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [recentCommits, setRecentCommits] = useState<Commit[]>([]);
  const [stats, setStats] = useState<RepositoryStats | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    repoId: string;
    repoName: string;
  }>({
    open: false,
    repoId: '',
    repoName: '',
  });

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        
        // 并行加载所有数据
        const [repositoriesData, statsData, commitsData] = await Promise.all([
          repositoryApi.getRepositories({ showToast: false }),
          repositoryApi.getRepositoryStats({ showToast: false }),
          repositoryApi.getRecentCommits(10, { showToast: false }),
        ]);
        
        setRepositories(repositoriesData);
        setStats(statsData);
        setRecentCommits(commitsData);
      } catch (error) {
        toast({
          title: "加载失败",
          description: "无法加载仪表盘数据，请稍后重试",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [toast]);

  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleUnfollowClick = (e: React.MouseEvent, repoId: string, repoName: string) => {
    e.preventDefault(); // 阻止点击卡片的导航
    e.stopPropagation(); // 阻止事件冒泡
    
    // 打开确认对话框
    setConfirmDialog({
      open: true,
      repoId,
      repoName,
    });
  };

  const handleConfirmUnfollow = async () => {
    try {
      // 调用API删除仓库
      const result = await repositoryApi.deleteRepository(confirmDialog.repoId);
      
      // 从UI中移除仓库
      setRepositories(prev => prev.filter(repo => repo.id !== confirmDialog.repoId));
      
      // 显示详细的删除信息
      if (result.summary) {
        toast({
          title: "取消关注成功",
          description: `已取消关注仓库 ${confirmDialog.repoName}，清除了 ${result.summary.deletedCommits} 个提交缓存和 ${result.summary.deletedCommitFiles} 个文件记录缓存`,
        });
      } else {
        toast({
          title: "取消关注成功",
          description: `已取消关注仓库 ${confirmDialog.repoName}`,
        });
      }
    } catch (error) {
      console.error('Failed to unfollow repository:', error);
      toast({
        title: "取消关注失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      // 关闭对话框
      setConfirmDialog({
        open: false,
        repoId: '',
        repoName: '',
      });
    }
  };

  const handleCancelUnfollow = () => {
    setConfirmDialog({
      open: false,
      repoId: '',
      repoName: '',
    });
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-3xl font-bold text-foreground">
              仓库仪表盘
            </h2>
            <p className="text-muted-foreground mt-1">
              管理您关注的 GitHub 仓库并审查提交记录
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="搜索仓库..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-10 w-64"
              />
            </div>
            
            <Button 
              onClick={handleSearch}
              disabled={!searchQuery.trim()}
              className="flex items-center gap-2"
            >
              <Search className="w-4 h-4" />
              搜索
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-full">
                  <GitHubLogo className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {loading ? '...' : stats?.totalRepositories || 0}
                  </p>
                  <p className="text-sm text-muted-foreground">关注仓库</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-github-success/10 rounded-full">
                  <Activity className="w-6 h-6 text-github-success" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {loading ? '...' : stats?.activeRepositories || 0}
                  </p>
                  <p className="text-sm text-muted-foreground">活跃仓库</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-github-warning/10 rounded-full">
                  <Star className="w-6 h-6 text-github-warning" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {loading ? '...' : (stats?.totalStars || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-muted-foreground">总 Stars</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Repository Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            // 加载占位符
            Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-muted rounded" />
                      <div>
                        <div className="h-5 bg-muted rounded w-32 mb-2" />
                        <div className="h-4 bg-muted rounded w-24" />
                      </div>
                    </div>
                    <div className="h-6 bg-muted rounded w-12" />
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="h-4 bg-muted rounded w-16" />
                    <div className="h-4 bg-muted rounded w-20" />
                  </div>
                  <div className="h-4 bg-muted rounded w-24" />
                </CardContent>
              </Card>
            ))
          ) : repositories.length > 0 ? (
            repositories.map((repo) => (
              <Link key={repo.id} to={`/repository/${repo.owner}/${repo.name}`}>
                <Card className="hover:shadow-elegant transition-all duration-300 group cursor-pointer h-full flex flex-col">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <GitHubLogo className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <CardTitle className="text-lg group-hover:text-primary transition-colors truncate">
                            {repo.owner}/{repo.name}
                          </CardTitle>
                          <CardDescription className="text-sm line-clamp-2">
                            {repo.description || '暂无描述'}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Badge variant={repo.isActive ? 'default' : 'secondary'} className="whitespace-nowrap">
                          {repo.isActive ? '活跃' : '暂停'}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleUnfollowClick(e, repo.id, `${repo.owner}/${repo.name}`)}
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive flex-shrink-0"
                          title="取消关注"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4 flex-1 flex flex-col justify-between">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Star className="w-4 h-4" />
                          {repo.stars.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <GitCommit className="w-4 h-4" />
                          {repo.commitCount} 提交
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mt-auto">
                      <Clock className="w-4 h-4" />
                      最后提交: {repo.lastCommitTime ? formatTimeAgo(repo.lastCommitTime) : '无'}
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <GitHubLogo className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">暂无仓库数据</p>
            </div>
          )}
        </div>

        {/* Recent Commits Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <GitCommit className="w-6 h-6 text-primary" />
            <h3 className="text-xl font-semibold text-foreground">最新提交</h3>
            <Badge variant="secondary">{loading ? '...' : recentCommits.length}</Badge>
          </div>
          
          <div className="space-y-3">
            {loading ? (
              // 加载占位符
              Array.from({ length: 5 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-muted" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4" />
                        <div className="flex items-center gap-3">
                          <div className="h-3 bg-muted rounded w-16" />
                          <div className="h-3 bg-muted rounded w-12" />
                          <div className="h-5 bg-muted rounded w-20" />
                        </div>
                      </div>
                      <div className="h-3 bg-muted rounded w-16" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : recentCommits.length > 0 ? (
              recentCommits.slice(0, 10).map((commit) => (
                <Link key={commit.hash} to={`/commit/${commit.hash}`}>
                  <Card className="hover:shadow-elegant transition-all duration-300 group cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <User className="w-4 h-4 text-primary" />
                          </div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                                {commit.message}
                              </p>
                              <div className="flex items-center gap-3 mt-1">
                                <span className="text-xs text-muted-foreground">
                                  {commit.author}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  提交到
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {commit.repository.owner}/{commit.repository.name}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              {formatTimeAgo(commit.createdAt)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <GitCommit className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无最新提交</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

      </div>

      {/* 确认删除对话框 */}
      <AlertDialog open={confirmDialog.open} onOpenChange={(open) => !open && handleCancelUnfollow()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认取消关注</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要取消关注仓库 <strong className="text-foreground">{confirmDialog.repoName}</strong> 吗？
              <br />
              <span className="text-muted-foreground">
                此操作将删除该仓库的所有数据，包括提交记录和文件变更信息，且无法撤销。
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelUnfollow}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmUnfollow}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认取消关注
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default Dashboard;
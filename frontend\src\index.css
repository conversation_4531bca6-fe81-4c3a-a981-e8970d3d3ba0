@tailwind base;
@tailwind components;
@tailwind utilities;

/* GitHub Commit Inspector Design System - Professional Dark Theme for Code Review */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 210 20% 98%;
    --foreground: 222.2 84% 4.9%;
    
    /* GitHub-inspired Theme */
    --github-dark: 213 27% 8%;
    --github-darker: 215 28% 6%;
    --github-border: 213 27% 18%;
    --github-accent: 212 92% 45%;
    --github-success: 120 100% 25%;
    --github-warning: 45 100% 51%;
    --github-danger: 0 100% 67%;
    
    /* Code Review Colors */
    --code-added: 120 63% 15%;
    --code-removed: 0 63% 15%;
    --code-modified: 45 63% 15%;
    --code-addition: 120 100% 86%;
    --code-deletion: 0 100% 86%;
    
    /* UI Components */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 212 92% 45%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 212 92% 65%;
    
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 212 92% 45%;
    
    --radius: 0.75rem;
    
    /* Gradients */
    --gradient-brand: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-dark: linear-gradient(135deg, hsl(var(--github-dark)), hsl(var(--github-darker)));
    
    /* Shadows */
    --shadow-elegant: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 10px 10px -5px hsl(var(--primary) / 0.04);
    --shadow-glow: 0 0 50px hsl(var(--primary-glow) / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    --background: 213 27% 8%;
    --foreground: 213 31% 91%;
    
    --card: 213 27% 10%;
    --card-foreground: 213 31% 91%;
    
    --popover: 213 27% 10%;
    --popover-foreground: 213 31% 91%;
    
    --primary: 212 92% 55%;
    --primary-foreground: 213 27% 8%;
    --primary-glow: 212 92% 75%;
    
    --secondary: 213 27% 16%;
    --secondary-foreground: 213 31% 91%;
    
    --muted: 213 27% 16%;
    --muted-foreground: 213 19% 60%;
    
    --accent: 213 27% 16%;
    --accent-foreground: 213 31% 91%;
    
    --destructive: 0 72% 60%;
    --destructive-foreground: 213 31% 91%;
    
    --border: 213 27% 18%;
    --input: 213 27% 18%;
    --ring: 212 92% 55%;
    
    /* Code Review - Dark Theme */
    --code-added: 120 100% 25%;
    --code-removed: 0 100% 40%;
    --code-modified: 45 100% 50%;
    --code-addition: 120 100% 86%;
    --code-deletion: 0 100% 86%;
    
    /* GitHub Theme Updates */
    --github-border: 213 27% 18%;
    --github-accent: 212 92% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
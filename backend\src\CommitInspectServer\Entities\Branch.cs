using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class Branch
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public required string Name { get; set; }

    [Required]
    public Guid RepositoryId { get; set; }

    [ForeignKey("RepositoryId")]
    public virtual Repository Repository { get; set; } = null!;

    public Guid? HeadCommitId { get; set; }

    [ForeignKey("HeadCommitId")]
    public virtual Commit? HeadCommit { get; set; }
}
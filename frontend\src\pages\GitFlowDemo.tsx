import React, { useEffect, useRef, useState, useMemo } from 'react';

const GitFlowGraph = () => {
  const canvasRef = useRef(null);
  const [hoveredCommit, setHoveredCommit] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const CONFIG = {
    padding: 40,
    nodeRadius: 5,
    nodeSpacingX: 20,
    nodeSpacingY: 30,
    maxMessageLength: 50,
    minCanvasWidth: 600,
    extraBuffer: 50,
    labelPadding: 4,
    labelHeight: 16,
    labelOffset: 10,
    highlightRadius: 4
  };

  const colorPalette = [
    '#ff6b6b', '#4ecdc4', '#feca57', '#5f27cd', '#00d2d3',
    '#1dd1a1', '#c8d6e5', '#ff9ff3', '#48dbfb', '#576574'
  ];

  const rawCommits = [ /* commit 数据不变 */ 
    { id: 'a1', message: 'Initial commit', branch: 'main', parents: [], tags: ['v0.1'] },
    { id: 'a2', message: 'Add README', branch: 'main', parents: ['a1'], tags: [] },
    { id: 'b1', message: 'Create feature branch', branch: 'feature', parents: ['a2'], tags: [] },
    { id: 'a3', message: 'Update config', branch: 'main', parents: ['a2'], tags: [] },
    { id: 'b2', message: 'Add new feature: implement user authentication system with JWT tokens, OAuth2.0 support, and multi-factor authentication capabilities', branch: 'feature', parents: ['b1'], tags: [] },
    { id: 'c1', message: 'Hotfix branch', branch: 'hotfix', parents: ['a3'], tags: ['hotfix-1'] },
    { id: 'b3', message: 'Complete feature', branch: 'feature', parents: ['b2'], tags: [] },
    { id: 'a4', message: 'Merge hotfix', branch: 'main', parents: ['a3', 'c1'], tags: ['v0.2'] },
    { id: 'a5', message: 'Merge feature', branch: 'main', parents: ['a4', 'b3'], tags: [] },
    { id: 'd1', message: 'New development', branch: 'develop', parents: ['a5'], tags: [] },
    { id: 'a6', message: 'Update docs', branch: 'main', parents: ['a5'], tags: [] },
    { id: 'd2', message: 'Refactor code...', branch: 'develop', parents: ['d1'], tags: ['beta-1'] },
    { id: 'e1', message: 'Experimental feature', branch: 'experiment', parents: ['d1'], tags: [] },
    { id: 'd3', message: 'Merge experiment', branch: 'develop', parents: ['d2', 'e1'], tags: [] },
    { id: 'a7', message: 'Release v1.0', branch: 'main', parents: ['a6', 'd3'], tags: ['v1.0', 'latest'] }
  ];

  const branchColorMap = useMemo(() => {
    const map = new Map();
    let colorIndex = 0;
    rawCommits.forEach(commit => {
      if (!map.has(commit.branch)) {
        map.set(commit.branch, colorPalette[colorIndex % colorPalette.length]);
        colorIndex++;
      }
    });
    return map;
  }, [rawCommits]);

  const getBranchColor = (branch) => branchColorMap.get(branch);
  const truncateText = (text, maxLength) => text.length > maxLength ? text.substring(0, maxLength) + '...' : text;

  const getNodePosition = (commit) => ({
    x: CONFIG.padding + commit.x * CONFIG.nodeSpacingX,
    y: CONFIG.padding + commit.y * CONFIG.nodeSpacingY
  });

  const commits = useMemo(() => {
    const branchColumns = new Map();
    let nextColumn = 0;
    const assignBranchColumn = (branch) => {
      if (!branchColumns.has(branch)) {
        branchColumns.set(branch, branch === 'main' ? 0 : ++nextColumn);
      }
      return branchColumns.get(branch);
    };

    const branchHeads = new Map();
    rawCommits.forEach(commit => {
      if (!branchHeads.has(commit.branch) ||
          rawCommits.indexOf(commit) > rawCommits.indexOf(branchHeads.get(commit.branch))) {
        branchHeads.set(commit.branch, commit);
      }
    });

    return rawCommits.map((commit, index) => ({
      ...commit,
      x: assignBranchColumn(commit.branch),
      y: rawCommits.length - 1 - index,
      showBranch: branchHeads.get(commit.branch)?.id === commit.id
    }));
  }, [rawCommits]);

  const drawLabel = (ctx, text, x, y, bgColor, borderColor, textColor) => {
    const width = ctx.measureText(text).width + CONFIG.labelPadding * 2;
    ctx.fillStyle = bgColor;
    ctx.fillRect(x, y - 8, width, CONFIG.labelHeight);
    ctx.strokeStyle = borderColor;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y - 8, width, CONFIG.labelHeight);
    ctx.fillStyle = textColor;
    ctx.fillText(text, x + CONFIG.labelPadding, y + 3);
    return width;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const dpr = window.devicePixelRatio || 1;

    const maxX = Math.max(...commits.map(c => c.x));
    const maxY = Math.max(...commits.map(c => c.y));

    ctx.font = '12px sans-serif';
    let maxTextWidth = 0;
    commits.forEach(commit => {
      let textWidth = CONFIG.nodeRadius + CONFIG.labelOffset;
      if (commit.showBranch) {
        textWidth += ctx.measureText(commit.branch).width + CONFIG.labelPadding * 2 + 5;
      }
      if (commit.tags?.length > 0) {
        commit.tags.forEach(tag => {
          textWidth += ctx.measureText(tag).width + CONFIG.labelPadding * 2 + 5;
        });
      }
      const displayMessage = truncateText(commit.message, CONFIG.maxMessageLength);
      textWidth += ctx.measureText(displayMessage).width;
      maxTextWidth = Math.max(maxTextWidth, textWidth);
    });

    const logicalWidth = Math.max(CONFIG.minCanvasWidth,
      CONFIG.padding * 2 + maxX * CONFIG.nodeSpacingX + maxTextWidth + CONFIG.extraBuffer);
    const logicalHeight = CONFIG.padding * 2 + maxY * CONFIG.nodeSpacingY + CONFIG.extraBuffer;

    canvas.width = logicalWidth * dpr;
    canvas.height = logicalHeight * dpr;
    canvas.style.width = `${logicalWidth}px`;
    canvas.style.height = `${logicalHeight}px`;
    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);

    ctx.clearRect(0, 0, logicalWidth, logicalHeight);

    // draw links
    commits.forEach(commit => {
      const pos = getNodePosition(commit);
      const branchColor = getBranchColor(commit.branch);
      commit.parents.forEach(parentId => {
        const parent = commits.find(c => c.id === parentId);
        if (parent) {
          const parentPos = getNodePosition(parent);
          ctx.beginPath();
          ctx.strokeStyle = branchColor + '80';
          ctx.lineWidth = 3;
          const midY = parentPos.y + (pos.y - parentPos.y) * 0.5;
          ctx.moveTo(parentPos.x, parentPos.y);
          ctx.bezierCurveTo(parentPos.x, midY, pos.x, midY, pos.x, pos.y);
          ctx.stroke();
        }
      });
    });

    // draw nodes and labels
    commits.forEach(commit => {
      const pos = getNodePosition(commit);
      const branchColor = getBranchColor(commit.branch);

      ctx.beginPath();
      ctx.arc(pos.x, pos.y, CONFIG.nodeRadius + 2, 0, 2 * Math.PI);
      ctx.fillStyle = '#1e1e2f';
      ctx.fill();

      ctx.beginPath();
      ctx.arc(pos.x, pos.y, CONFIG.nodeRadius, 0, 2 * Math.PI);
      ctx.fillStyle = branchColor;
      ctx.fill();

      if (hoveredCommit === commit.id) {
        ctx.beginPath();
        ctx.arc(pos.x, pos.y, CONFIG.nodeRadius + CONFIG.highlightRadius, 0, 2 * Math.PI);
        ctx.strokeStyle = branchColor;
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      ctx.font = '11px sans-serif';
      let labelX = pos.x + CONFIG.nodeRadius + CONFIG.labelOffset;

      if (commit.showBranch) {
        const width = drawLabel(ctx, commit.branch, labelX, pos.y, branchColor + '40', branchColor, branchColor);
        labelX += width + 5;
      }

      if (commit.tags?.length > 0) {
        commit.tags.forEach(tag => {
          const width = drawLabel(ctx, tag, labelX, pos.y, '#f1c40f40', '#f1c40f', '#f39c12');
          labelX += width + 5;
        });
      }

      ctx.font = '12px sans-serif';
      const displayMessage = truncateText(commit.message, CONFIG.maxMessageLength);
      drawLabel(ctx, displayMessage, labelX, pos.y, '#2d2f3a80', '#444c5a', '#f1f1f1');
    });
  }, [hoveredCommit, commits, getBranchColor]);

  const handleMouseMove = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    setMousePosition({ x: e.clientX, y: e.clientY });

    const foundCommit = commits.find(commit => {
      const pos = getNodePosition(commit);
      const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
      return distance <= CONFIG.nodeRadius + 3;
    });

    setHoveredCommit(foundCommit?.id || null);
  };

  const hoveredCommitData = hoveredCommit ? commits.find(c => c.id === hoveredCommit) : null;

  return (
    <div className="flex flex-col items-center p-8 bg-gray-900 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-white">Git Flow 可视化</h1>
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 relative">
        <canvas
          ref={canvasRef}
          className="border border-gray-700 rounded cursor-pointer"
          onMouseMove={handleMouseMove}
          onMouseLeave={() => setHoveredCommit(null)}
        />
        {hoveredCommitData && (() => {
          const containerRect = canvasRef.current.getBoundingClientRect();
          const relativeY = mousePosition.y - containerRect.top;
          const showAbove = relativeY > containerRect.height / 2;

          return (
            <div
              className="absolute z-10 bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-4 pointer-events-none transition-all duration-200"
              style={{
                left: `${mousePosition.x - containerRect.left + 40}px`,
                top: showAbove
                  ? `${mousePosition.y - containerRect.top - 10}px`
                  : `${mousePosition.y - containerRect.top + 40}px`,
                transform: showAbove ? 'translateY(-100%)' : 'translateY(0%)',
                minWidth: '200px',
                maxWidth: '300px'
              }}
            >
              <div className="flex items-center space-x-2 mb-2">
                <span
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: getBranchColor(hoveredCommitData.branch) }}
                />
                <span className="text-xs px-2 py-1 rounded-full bg-gray-700 text-gray-200">
                  {hoveredCommitData.branch}
                </span>
              </div>
              <p className="text-sm text-gray-100 mb-2">{hoveredCommitData.message}</p>
              {hoveredCommitData.tags?.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {hoveredCommitData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="text-xs px-2 py-1 rounded bg-yellow-300 text-yellow-900 border border-yellow-400"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
              {hoveredCommitData.parents.length > 0 && (
                <div className="text-xs text-gray-400 border-t border-gray-600 pt-2 mt-2">
                  <span className="font-semibold">Parents:</span> {hoveredCommitData.parents.join(', ')}
                </div>
              )}
              {hoveredCommitData.parents.length === 0 && (
                <div className="text-xs text-gray-400 border-t border-gray-600 pt-2">
                  <span className="italic">Root commit (最早的提交)</span>
                </div>
              )}
              {hoveredCommitData.parents.length > 1 && (
                <div className="text-xs text-green-400 mt-1">
                  <span className="font-semibold">✓ Merge commit</span>
                </div>
              )}
            </div>
          );
        })()}
      </div>
    </div>
  );
};

export default GitFlowGraph;

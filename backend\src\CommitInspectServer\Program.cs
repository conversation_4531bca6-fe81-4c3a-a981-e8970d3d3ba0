using CommitInspectServer.EfCore;
using CommitInspectServer.Authentication;
using CommitInspectServer.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// 添加 Controllers 支持
builder.Services.AddControllers();

// 添加Token服务
builder.Services.AddSingleton<ITokenService>((sp) => new TokenService(sp.GetRequiredService<IConfiguration>()));

// 添加GitHub服务
builder.Services.AddHttpClient<IGitHubService, GitHubService>();

// 添加GitHub扫描服务
builder.Services.AddHttpClient<IGitHubScanService, GitHubScanService>();

// 添加后台服务
builder.Services.AddHostedService<RepositoryScanBackgroundService>();

// 添加身份验证和授权
builder.Services.AddAuthentication(SimpleTokenAuthenticationHandler.SchemeName)
    .AddScheme<AuthenticationSchemeOptions, SimpleTokenAuthenticationHandler>(
        SimpleTokenAuthenticationHandler.SchemeName, 
        options => { });

builder.Services.AddAuthorization();

// 添加 Entity Framework Core 和 SQLite 支持
builder.Services.AddDbContext<CommitInspectDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection") ?? "Data Source=commit-inspect.db"));


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    using(var scope = app.Services.CreateScope())
    {
        //var dbContext = scope.ServiceProvider.GetRequiredService<CommitInspectDbContext>();
        //dbContext.Database.EnsureDeleted();
        //dbContext.Database.EnsureCreated();

        // 生成测试数据
        //var seeder = new DataSeeder(dbContext);
        //await seeder.SeedAsync();
    }
    app.MapOpenApi();
}

// 使用静态文件中间件（用于服务前端文件）
app.UseDefaultFiles();
app.UseStaticFiles();

// 使用身份验证和授权中间件
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

if (app.Environment.IsDevelopment())
{
    app.MapFallback(() => "✅ Server is running");
}
else
{
// 对于 SPA 路由，返回 index.html
app.MapFallback(async context =>
{
    var path = Path.Combine(app.Environment.WebRootPath, "index.html");
    if (File.Exists(path))
    {
        context.Response.ContentType = "text/html";
        await context.Response.SendFileAsync(path);
}
    else
    {
        context.Response.StatusCode = 404;
        await context.Response.WriteAsync("Frontend not found. Please build the frontend first.");
    }
});
}


app.Run();


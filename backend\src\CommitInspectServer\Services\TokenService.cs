using System.Collections.Concurrent;

namespace CommitInspectServer.Services;

public interface ITokenService
{
    string GenerateToken();
    bool ValidateToken(string token);
}

public class TokenService : ITokenService
{
    private readonly int _expirationHours;
    private readonly ConcurrentDictionary<string, DateTime> _tokens = new();

    public TokenService(IConfiguration configuration)
    {
        _expirationHours = configuration.GetValue<int>("Auth:ExpirationHours", 24);
    }

    public string GenerateToken()
    {
        // 生成随机token
        var token = Guid.NewGuid().ToString("N"); // 32位十六进制字符串
        
        var expiresAt = DateTime.UtcNow.AddHours(_expirationHours);
        _tokens.AddOrUpdate(token, expiresAt, (key, oldValue) => expiresAt);
        
        // 清理过期token（简单清理策略）
        CleanupExpiredTokens();
        
        return token;
    }

    public bool ValidateToken(string token)
    {
        if (string.IsNullOrEmpty(token) || !_tokens.TryGetValue(token, out var expiresAt))
        {
            return false;
        }

        // 检查是否过期
        if (DateTime.UtcNow > expiresAt)
        {
            _tokens.TryRemove(token, out _);
            return false;
        }

        // 更新token过期时间
        _tokens.AddOrUpdate(token, DateTime.UtcNow.AddHours(_expirationHours), (key, oldValue) => DateTime.UtcNow.AddHours(_expirationHours));

        return true;
    }

    private void CleanupExpiredTokens()
    {
        var now = DateTime.UtcNow;
        var expiredTokens = _tokens
            .Where(kvp => now > kvp.Value)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var expiredToken in expiredTokens)
        {
            _tokens.TryRemove(expiredToken, out _);
        }
    }
}
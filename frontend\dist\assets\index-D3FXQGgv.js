const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-BZUbbtLo.js","assets/react-vendor-DucYQ77b.js","assets/badge-BHFrTODj.js","assets/utils-CUkY6B8V.js","assets/radix-ui-DdAvwPj2.js","assets/ui-extras-Cs9DDC1j.js","assets/data-Csye86cT.js","assets/Statistics-4h3TH0kC.js","assets/select-CsIt0xHD.js","assets/charts-CFrHoElS.js","assets/Settings-DBpcXiPz.js","assets/SearchResults-CY7_gO4U.js","assets/Repository-jdeJgS9o.js","assets/avatar-wZOQCpPA.js","assets/tabs-CYmfXndi.js","assets/Commit-CJPMizUI.js","assets/FileDiff-BgaImzb1.js","assets/NotFound-Bcz6IzHq.js"])))=>i.map(i=>d[i]);
import{r as d,a as Jt,u as be,N as Ge,e as He,L as J,B as Qt,f as Zt,h as A}from"./react-vendor-DucYQ77b.js";import{V as Ke,R as Xe,A as Je,C as Qe,T as Ze,D as et,P as er,a as tt,b as tr,S as rr}from"./radix-ui-DdAvwPj2.js";import{L as nr,c as or,X as sr,M as rt,N as ir,O as ar,S as cr,P as ur,Q as lr,R as dr,T as mr}from"./utils-CUkY6B8V.js";import{j as fr,T as pr}from"./ui-extras-Cs9DDC1j.js";import{Q as vr,a as gr}from"./data-Csye86cT.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))a(c);new MutationObserver(c=>{for(const p of c)if(p.type==="childList")for(const u of p.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&a(u)}).observe(document,{childList:!0,subtree:!0});function o(c){const p={};return c.integrity&&(p.integrity=c.integrity),c.referrerPolicy&&(p.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?p.credentials="include":c.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function a(c){if(c.ep)return;c.ep=!0;const p=o(c);fetch(c.href,p)}})();var nt={exports:{}},ve={};/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var t=d,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),g=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),w=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),P=Symbol.for("react.lazy"),V=Symbol.for("react.offscreen"),O=Symbol.iterator,te="@@iterator";function re(e){if(e===null||typeof e!="object")return null;var s=O&&e[O]||e[te];return typeof s=="function"?s:null}var U=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function C(e){{for(var s=arguments.length,i=new Array(s>1?s-1:0),l=1;l<s;l++)i[l-1]=arguments[l];Nt("error",e,i)}}function Nt(e,s,i){{var l=U.ReactDebugCurrentFrame,v=l.getStackAddendum();v!==""&&(s+="%s",i=i.concat([v]));var N=i.map(function(f){return String(f)});N.unshift("Warning: "+s),Function.prototype.apply.call(console[e],console,N)}}var xt=!1,ht=!1,yt=!1,jt=!1,Et=!1,he;he=Symbol.for("react.module.reference");function Pt(e){return!!(typeof e=="string"||typeof e=="function"||e===a||e===p||Et||e===c||e===E||e===w||jt||e===V||xt||ht||yt||typeof e=="object"&&e!==null&&(e.$$typeof===P||e.$$typeof===h||e.$$typeof===u||e.$$typeof===g||e.$$typeof===x||e.$$typeof===he||e.getModuleId!==void 0))}function Ct(e,s,i){var l=e.displayName;if(l)return l;var v=s.displayName||s.name||"";return v!==""?i+"("+v+")":i}function ye(e){return e.displayName||"Context"}function T(e){if(e==null)return null;if(typeof e.tag=="number"&&C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case a:return"Fragment";case o:return"Portal";case p:return"Profiler";case c:return"StrictMode";case E:return"Suspense";case w:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case g:var s=e;return ye(s)+".Consumer";case u:var i=e;return ye(i._context)+".Provider";case x:return Ct(e,e.render,"ForwardRef");case h:var l=e.displayName||null;return l!==null?l:T(e.type)||"Memo";case P:{var v=e,N=v._payload,f=v._init;try{return T(f(N))}catch{return null}}}return null}var k=Object.assign,B=0,je,Ee,Pe,Ce,we,De,Se;function Re(){}Re.__reactDisabledLog=!0;function wt(){{if(B===0){je=console.log,Ee=console.info,Pe=console.warn,Ce=console.error,we=console.group,De=console.groupCollapsed,Se=console.groupEnd;var e={configurable:!0,enumerable:!0,value:Re,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}B++}}function Dt(){{if(B--,B===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:k({},e,{value:je}),info:k({},e,{value:Ee}),warn:k({},e,{value:Pe}),error:k({},e,{value:Ce}),group:k({},e,{value:we}),groupCollapsed:k({},e,{value:De}),groupEnd:k({},e,{value:Se})})}B<0&&C("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var ne=U.ReactCurrentDispatcher,oe;function G(e,s,i){{if(oe===void 0)try{throw Error()}catch(v){var l=v.stack.trim().match(/\n( *(at )?)/);oe=l&&l[1]||""}return`
`+oe+e}}var se=!1,H;{var St=typeof WeakMap=="function"?WeakMap:Map;H=new St}function Ve(e,s){if(!e||se)return"";{var i=H.get(e);if(i!==void 0)return i}var l;se=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var N;N=ne.current,ne.current=null,wt();try{if(s){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(R){l=R}Reflect.construct(e,[],f)}else{try{f.call()}catch(R){l=R}e.call(f.prototype)}}else{try{throw Error()}catch(R){l=R}e()}}catch(R){if(R&&l&&typeof R.stack=="string"){for(var m=R.stack.split(`
`),D=l.stack.split(`
`),y=m.length-1,j=D.length-1;y>=1&&j>=0&&m[y]!==D[j];)j--;for(;y>=1&&j>=0;y--,j--)if(m[y]!==D[j]){if(y!==1||j!==1)do if(y--,j--,j<0||m[y]!==D[j]){var _=`
`+m[y].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),typeof e=="function"&&H.set(e,_),_}while(y>=1&&j>=0);break}}}finally{se=!1,ne.current=N,Dt(),Error.prepareStackTrace=v}var M=e?e.displayName||e.name:"",I=M?G(M):"";return typeof e=="function"&&H.set(e,I),I}function Rt(e,s,i){return Ve(e,!1)}function Vt(e){var s=e.prototype;return!!(s&&s.isReactComponent)}function K(e,s,i){if(e==null)return"";if(typeof e=="function")return Ve(e,Vt(e));if(typeof e=="string")return G(e);switch(e){case E:return G("Suspense");case w:return G("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case x:return Rt(e.render);case h:return K(e.type,s,i);case P:{var l=e,v=l._payload,N=l._init;try{return K(N(v),s,i)}catch{}}}return""}var W=Object.prototype.hasOwnProperty,_e={},Te=U.ReactDebugCurrentFrame;function X(e){if(e){var s=e._owner,i=K(e.type,e._source,s?s.type:null);Te.setExtraStackFrame(i)}else Te.setExtraStackFrame(null)}function _t(e,s,i,l,v){{var N=Function.call.bind(W);for(var f in e)if(N(e,f)){var m=void 0;try{if(typeof e[f]!="function"){var D=Error((l||"React class")+": "+i+" type `"+f+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[f]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw D.name="Invariant Violation",D}m=e[f](s,f,l,i,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(y){m=y}m&&!(m instanceof Error)&&(X(v),C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",l||"React class",i,f,typeof m),X(null)),m instanceof Error&&!(m.message in _e)&&(_e[m.message]=!0,X(v),C("Failed %s type: %s",i,m.message),X(null))}}}var Tt=Array.isArray;function ie(e){return Tt(e)}function At(e){{var s=typeof Symbol=="function"&&Symbol.toStringTag,i=s&&e[Symbol.toStringTag]||e.constructor.name||"Object";return i}}function Lt(e){try{return Ae(e),!1}catch{return!0}}function Ae(e){return""+e}function Le(e){if(Lt(e))return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",At(e)),Ae(e)}var Y=U.ReactCurrentOwner,Ot={key:!0,ref:!0,__self:!0,__source:!0},Oe,ke,ae;ae={};function kt(e){if(W.call(e,"ref")){var s=Object.getOwnPropertyDescriptor(e,"ref").get;if(s&&s.isReactWarning)return!1}return e.ref!==void 0}function It(e){if(W.call(e,"key")){var s=Object.getOwnPropertyDescriptor(e,"key").get;if(s&&s.isReactWarning)return!1}return e.key!==void 0}function $t(e,s){if(typeof e.ref=="string"&&Y.current&&s&&Y.current.stateNode!==s){var i=T(Y.current.type);ae[i]||(C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',T(Y.current.type),e.ref),ae[i]=!0)}}function Ft(e,s){{var i=function(){Oe||(Oe=!0,C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",s))};i.isReactWarning=!0,Object.defineProperty(e,"key",{get:i,configurable:!0})}}function Ut(e,s){{var i=function(){ke||(ke=!0,C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",s))};i.isReactWarning=!0,Object.defineProperty(e,"ref",{get:i,configurable:!0})}}var zt=function(e,s,i,l,v,N,f){var m={$$typeof:r,type:e,key:s,ref:i,props:f,_owner:N};return m._store={},Object.defineProperty(m._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(m,"_self",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.defineProperty(m,"_source",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(m.props),Object.freeze(m)),m};function Mt(e,s,i,l,v){{var N,f={},m=null,D=null;i!==void 0&&(Le(i),m=""+i),It(s)&&(Le(s.key),m=""+s.key),kt(s)&&(D=s.ref,$t(s,v));for(N in s)W.call(s,N)&&!Ot.hasOwnProperty(N)&&(f[N]=s[N]);if(e&&e.defaultProps){var y=e.defaultProps;for(N in y)f[N]===void 0&&(f[N]=y[N])}if(m||D){var j=typeof e=="function"?e.displayName||e.name||"Unknown":e;m&&Ft(f,j),D&&Ut(f,j)}return zt(e,m,D,v,l,Y.current,f)}}var ce=U.ReactCurrentOwner,Ie=U.ReactDebugCurrentFrame;function z(e){if(e){var s=e._owner,i=K(e.type,e._source,s?s.type:null);Ie.setExtraStackFrame(i)}else Ie.setExtraStackFrame(null)}var ue;ue=!1;function le(e){return typeof e=="object"&&e!==null&&e.$$typeof===r}function $e(){{if(ce.current){var e=T(ce.current.type);if(e)return`

Check the render method of \``+e+"`."}return""}}function Bt(e){{if(e!==void 0){var s=e.fileName.replace(/^.*[\\\/]/,""),i=e.lineNumber;return`

Check your code at `+s+":"+i+"."}return""}}var Fe={};function Wt(e){{var s=$e();if(!s){var i=typeof e=="string"?e:e.displayName||e.name;i&&(s=`

Check the top-level render call using <`+i+">.")}return s}}function Ue(e,s){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var i=Wt(s);if(Fe[i])return;Fe[i]=!0;var l="";e&&e._owner&&e._owner!==ce.current&&(l=" It was passed a child from "+T(e._owner.type)+"."),z(e),C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',i,l),z(null)}}function ze(e,s){{if(typeof e!="object")return;if(ie(e))for(var i=0;i<e.length;i++){var l=e[i];le(l)&&Ue(l,s)}else if(le(e))e._store&&(e._store.validated=!0);else if(e){var v=re(e);if(typeof v=="function"&&v!==e.entries)for(var N=v.call(e),f;!(f=N.next()).done;)le(f.value)&&Ue(f.value,s)}}}function Yt(e){{var s=e.type;if(s==null||typeof s=="string")return;var i;if(typeof s=="function")i=s.propTypes;else if(typeof s=="object"&&(s.$$typeof===x||s.$$typeof===h))i=s.propTypes;else return;if(i){var l=T(s);_t(i,e.props,"prop",l,e)}else if(s.PropTypes!==void 0&&!ue){ue=!0;var v=T(s);C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",v||"Unknown")}typeof s.getDefaultProps=="function"&&!s.getDefaultProps.isReactClassApproved&&C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function qt(e){{for(var s=Object.keys(e.props),i=0;i<s.length;i++){var l=s[i];if(l!=="children"&&l!=="key"){z(e),C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",l),z(null);break}}e.ref!==null&&(z(e),C("Invalid attribute `ref` supplied to `React.Fragment`."),z(null))}}var Me={};function Gt(e,s,i,l,v,N){{var f=Pt(e);if(!f){var m="";(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(m+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var D=Bt(v);D?m+=D:m+=$e();var y;e===null?y="null":ie(e)?y="array":e!==void 0&&e.$$typeof===r?(y="<"+(T(e.type)||"Unknown")+" />",m=" Did you accidentally export a JSX literal instead of a component?"):y=typeof e,C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",y,m)}var j=Mt(e,s,i,v,N);if(j==null)return j;if(f){var _=s.children;if(_!==void 0)if(l)if(ie(_)){for(var M=0;M<_.length;M++)ze(_[M],e);Object.freeze&&Object.freeze(_)}else C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ze(_,e)}if(W.call(s,"key")){var I=T(e),R=Object.keys(s).filter(function(Xt){return Xt!=="key"}),de=R.length>0?"{key: someKey, "+R.join(": ..., ")+": ...}":"{key: someKey}";if(!Me[I+de]){var Kt=R.length>0?"{"+R.join(": ..., ")+": ...}":"{}";C(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,de,I,Kt,I),Me[I+de]=!0}}return e===a?qt(j):Yt(j),j}}var Ht=Gt;ve.Fragment=a,ve.jsxDEV=Ht})();nt.exports=ve;var n=nt.exports,ot,Be=Jt;{var We=Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;ot=function(t,r){We.usingClientEntryPoint=!0;try{return Be.createRoot(t,r)}finally{We.usingClientEntryPoint=!1}}}const br="modulepreload",Nr=function(t){return"/"+t},Ye={},L=function(r,o,a){let c=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),g=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));c=Promise.allSettled(o.map(x=>{if(x=Nr(x),x in Ye)return;Ye[x]=!0;const E=x.endsWith(".css"),w=E?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${x}"]${w}`))return;const h=document.createElement("link");if(h.rel=E?"stylesheet":br,E||(h.as="script"),h.crossOrigin="",h.href=x,g&&h.setAttribute("nonce",g),document.head.appendChild(h),E)return new Promise((P,V)=>{h.addEventListener("load",P),h.addEventListener("error",()=>V(new Error(`Unable to preload CSS for ${x}`)))})}))}function p(u){const g=new Event("vite:preloadError",{cancelable:!0});if(g.payload=u,window.dispatchEvent(g),!g.defaultPrevented)throw u}return c.then(u=>{for(const g of u||[])g.status==="rejected"&&p(g.reason);return r().catch(p)})},xr=1,hr=1e6;let me=0;function yr(){return me=(me+1)%Number.MAX_SAFE_INTEGER,me.toString()}const fe=new Map,qe=t=>{if(fe.has(t))return;const r=setTimeout(()=>{fe.delete(t),q({type:"REMOVE_TOAST",toastId:t})},hr);fe.set(t,r)},jr=(t,r)=>{switch(r.type){case"ADD_TOAST":return{...t,toasts:[r.toast,...t.toasts].slice(0,xr)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(o=>o.id===r.toast.id?{...o,...r.toast}:o)};case"DISMISS_TOAST":{const{toastId:o}=r;return o?qe(o):t.toasts.forEach(a=>{qe(a.id)}),{...t,toasts:t.toasts.map(a=>a.id===o||o===void 0?{...a,open:!1}:a)}}case"REMOVE_TOAST":return r.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(o=>o.id!==r.toastId)}}},Q=[];let Z={toasts:[]};function q(t){Z=jr(Z,t),Q.forEach(r=>{r(Z)})}function ee({...t}){const r=yr(),o=c=>q({type:"UPDATE_TOAST",toast:{...c,id:r}}),a=()=>q({type:"DISMISS_TOAST",toastId:r});return q({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:c=>{c||a()}}}),{id:r,dismiss:a,update:o}}function Ne(){const[t,r]=d.useState(Z);return d.useEffect(()=>(Q.push(r),()=>{const o=Q.indexOf(r);o>-1&&Q.splice(o,1)}),[t]),{...t,toast:ee,dismiss:o=>q({type:"DISMISS_TOAST",toastId:o})}}function S(...t){return nr(or(t))}const Er=er,st=d.forwardRef(({className:t,...r},o)=>n.jsxDEV(Ke,{ref:o,className:S("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:14,columnNumber:3},void 0));st.displayName=Ke.displayName;const Pr=rt("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),it=d.forwardRef(({className:t,variant:r,...o},a)=>n.jsxDEV(Xe,{ref:a,className:S(Pr({variant:r}),t),...o},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:47,columnNumber:5},void 0));it.displayName=Xe.displayName;const Cr=d.forwardRef(({className:t,...r},o)=>n.jsxDEV(Je,{ref:o,className:S("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:60,columnNumber:3},void 0));Cr.displayName=Je.displayName;const at=d.forwardRef(({className:t,...r},o)=>n.jsxDEV(Qe,{ref:o,className:S("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...r,children:n.jsxDEV(sr,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:84,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:75,columnNumber:3},void 0));at.displayName=Qe.displayName;const ct=d.forwardRef(({className:t,...r},o)=>n.jsxDEV(Ze,{ref:o,className:S("text-sm font-semibold",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:93,columnNumber:3},void 0));ct.displayName=Ze.displayName;const ut=d.forwardRef(({className:t,...r},o)=>n.jsxDEV(et,{ref:o,className:S("text-sm opacity-90",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toast.tsx",lineNumber:105,columnNumber:3},void 0));ut.displayName=et.displayName;function wr(){const{toasts:t}=Ne();return n.jsxDEV(Er,{children:[t.map(function({id:r,title:o,description:a,action:c,...p}){return n.jsxDEV(it,{...p,children:[n.jsxDEV("div",{className:"grid gap-1",children:[o&&n.jsxDEV(ct,{children:o},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:20,columnNumber:25},this),a&&n.jsxDEV(ut,{children:a},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:22,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:19,columnNumber:13},this),c,n.jsxDEV(at,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:26,columnNumber:13},this)]},r,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:18,columnNumber:11},this)}),n.jsxDEV(st,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:30,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/toaster.tsx",lineNumber:15,columnNumber:5},this)}const Dr=({...t})=>{const{theme:r="system"}=fr();return n.jsxDEV(pr,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/sonner.tsx",lineNumber:10,columnNumber:5},void 0)},Sr=tr,Rr=d.forwardRef(({className:t,sideOffset:r=4,...o},a)=>n.jsxDEV(tt,{ref:a,sideOffset:r,className:S("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/tooltip.tsx",lineNumber:16,columnNumber:3},void 0));Rr.displayName=tt.displayName;let ge=null;const Vr=t=>{ge=t};class pe extends Error{constructor(r,o,a){super(r),this.status=o,this.response=a,this.name="ApiError"}}const b={request:async(t,r={})=>{const{showToast:o=!0,...a}=r,c={headers:{"Content-Type":"application/json",...a.headers},...a},p=localStorage.getItem("authToken");p&&(c.headers.Authorization=`Bearer ${p}`);try{const u=await fetch(`/api${t}`,c),g=await u.json();if(!u.ok){const x=g.message||`HTTP ${u.status}: ${u.statusText}`;throw u.status===401?(ge&&(ge(),setTimeout(()=>{window.location.href="/login"},100)),o&&ee({title:"登录已过期",description:"请重新登录",variant:"destructive"})):o&&ee({title:"请求失败",description:x,variant:"destructive"}),new pe(x,u.status,g)}return g}catch(u){if(u instanceof pe)throw u;const g=u instanceof Error?u.message:"网络连接失败";throw o&&ee({title:"网络错误",description:g,variant:"destructive"}),new pe(g,0)}},get:(t,r)=>b.request(t,{...r,method:"GET"}),post:(t,r,o)=>b.request(t,{...o,method:"POST",body:r?JSON.stringify(r):void 0}),put:(t,r,o)=>b.request(t,{...o,method:"PUT",body:r?JSON.stringify(r):void 0}),delete:(t,r)=>b.request(t,{...r,method:"DELETE"})},_r={async login(t,r){return b.post("/auth/login",{password:t},r)}},Qr={async getRepositories(t){return b.get("/repository",t)},async getRepository(t,r){return b.get(`/repository/${t}`,r)},async getRepositoryStats(t){return b.get("/repository/stats",t)},async getRecentCommits(t=10,r){return b.get(`/repository/recent-commits?limit=${t}`,r)},async getRepositoryByOwnerName(t,r,o){return b.get(`/repository/${t}/${r}`,o)},async getRepositoryBranches(t,r,o){return b.get(`/repository/${t}/${r}/branches`,o)},async getRepositoryCommits(t,r,o,a=1,c=20,p){const u=new URLSearchParams;return o&&u.append("branch",o),u.append("page",a.toString()),u.append("pageSize",c.toString()),b.get(`/repository/${t}/${r}/commits?${u.toString()}`,p)},async getRepositoryGitGraph(t,r,o=100,a){const c=new URLSearchParams;return c.append("limit",o.toString()),b.get(`/repository/${t}/${r}/git-graph?${c.toString()}`,a)},async deleteRepository(t,r){return b.delete(`/repository/${t}`,r)},async deleteRepositoryByOwnerName(t,r,o){return b.delete(`/repository/${t}/${r}`,o)},async searchRepositories(t,r=1,o=30,a){const c=new URLSearchParams;return c.append("q",t),c.append("page",r.toString()),c.append("per_page",o.toString()),b.get(`/repository/search?${c.toString()}`,a)},async getUserRepositories(t=1,r=100,o){const a=new URLSearchParams;return a.append("page",t.toString()),a.append("per_page",r.toString()),b.get(`/repository/user-repos?${a.toString()}`,o)},async followRepository(t,r,o){return b.post("/repository/follow",{owner:t,name:r},o)},async getFollowedRepositories(t){return b.get("/repository/followed",t)}},Zr={async scanRepositoryByOwnerName(t,r,o){return b.post(`/scan/repository/${t}/${r}`,void 0,o)},async scanAllRepositories(t){return b.post("/scan/all",void 0,t)},async getScanLogs(t=1,r=20,o,a){const c=new URLSearchParams;return c.append("page",t.toString()),c.append("pageSize",r.toString()),o&&c.append("repositoryId",o),b.get(`/scan/logs?${c.toString()}`,a)}},en={async getCommits(t,r=1,o=20,a){const c=new URLSearchParams;return t&&c.append("repositoryId",t),c.append("page",r.toString()),c.append("pageSize",o.toString()),b.get(`/commit?${c.toString()}`,a)},async getCommit(t,r){return b.get(`/commit/${t}`,r)}},tn={async getOverviewStatistics(t,r,o){const a=new URLSearchParams;return t&&a.append("startDate",t.toISOString()),r&&a.append("endDate",r.toISOString()),b.get(`/statistics/overview?${a.toString()}`,o)},async getCommitTrends(t,r,o){const a=new URLSearchParams;return t&&a.append("startDate",t.toISOString()),r&&a.append("endDate",r.toISOString()),b.get(`/statistics/commit-trends?${a.toString()}`,o)},async getContributorStatistics(t,r,o=10,a){const c=new URLSearchParams;return t&&c.append("startDate",t.toISOString()),r&&c.append("endDate",r.toISOString()),c.append("limit",o.toString()),b.get(`/statistics/contributors?${c.toString()}`,a)},async getFileTypeStatistics(t){return b.get("/statistics/file-types",t)},async getLanguageStatistics(t){return b.get("/statistics/languages",t)}},lt=d.createContext(void 0),Tr=({children:t})=>{const[r,o]=d.useState({isAuthenticated:!1}),[a,c]=d.useState(!0),p=E=>E?Date.now()>E:!0;d.useEffect(()=>{(()=>{const w=localStorage.getItem("isAuthenticated")==="true",h=localStorage.getItem("authToken"),P=localStorage.getItem("tokenExpiry");if(w&&h&&P){const V=parseInt(P,10);p(V)?g():o({isAuthenticated:!0,token:h,tokenExpiry:V})}else o({isAuthenticated:!1});c(!1)})(),Vr(g)},[]);const u=async E=>{try{const w=await _r.login(E,{showToast:!1});if(w.success&&w.token){const h=Date.now()+864e5,P={isAuthenticated:!0,token:w.token,tokenExpiry:h};return o(P),localStorage.setItem("isAuthenticated","true"),localStorage.setItem("authToken",w.token),localStorage.setItem("tokenExpiry",h.toString()),!0}return!1}catch{return!1}},g=()=>{o({isAuthenticated:!1}),localStorage.removeItem("isAuthenticated"),localStorage.removeItem("authToken"),localStorage.removeItem("tokenExpiry")},x={user:r,login:u,logout:g,isLoading:a};return n.jsxDEV(lt.Provider,{value:x,children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/contexts/AuthContext.tsx",lineNumber:109,columnNumber:5},void 0)},xe=()=>{const t=d.useContext(lt);if(t===void 0)throw new Error("useAuth must be used within an AuthProvider");return t},$=({children:t})=>{const{user:r,isLoading:o}=xe(),a=be();return o?n.jsxDEV("div",{className:"min-h-screen flex items-center justify-center",children:n.jsxDEV("div",{className:"flex flex-col items-center gap-4",children:[n.jsxDEV("div",{className:"w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:18,columnNumber:11},void 0),n.jsxDEV("p",{className:"text-muted-foreground",children:"验证身份中..."},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:19,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:17,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:16,columnNumber:7},void 0):r.isAuthenticated?n.jsxDEV(n.Fragment,{children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:30,columnNumber:10},void 0):n.jsxDEV(Ge,{to:"/login",state:{from:a},replace:!0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ProtectedRoute.tsx",lineNumber:27,columnNumber:12},void 0)},dt=({className:t="w-6 h-6"})=>n.jsxDEV("svg",{className:t,viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:n.jsxDEV("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 10.95.6-.225 1.017-.6 1.017-1.017v-4.142c-3.383.725-4.1-1.425-4.1-1.425-.55-1.4-1.334-1.775-1.334-1.775-1.09-.742.083-.725.083-.725 1.2.083 1.833 1.225 1.833 1.225 1.067 1.834 2.8 1.3 3.492.992.1-.775.417-1.3.758-1.6-2.667-.3-5.467-1.334-5.467-5.934 0-1.3.467-2.383 1.233-3.217-.125-.3-.533-1.508.117-3.133 0 0 1.008-.325 3.3 1.233.958-.267 1.983-.4 3.008-.4 1.025 0 2.05.133 3.008.4 2.292-1.558 3.3-1.233 3.3-1.233.65 1.625.242 2.833.117 3.133.767.834 1.233 1.917 1.233 3.217 0 4.608-2.808 5.625-5.483 5.917.433.375.817 1.1.817 2.217v3.283c0 .417.408.792 1.017 1.017C20.84 21.4 23.996 17.062 23.996 11.987 23.996 5.367 18.63.001 12.017.001z"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitHubLogo.tsx",lineNumber:15,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/GitHubLogo.tsx",lineNumber:9,columnNumber:5},void 0),Ar=rt("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",github:"bg-github-dark text-white border border-github-border hover:bg-github-darker shadow-sm",success:"bg-github-success text-white hover:bg-github-success/90 shadow-sm",warning:"bg-github-warning text-github-dark hover:bg-github-warning/90 shadow-sm",danger:"bg-github-danger text-white hover:bg-github-danger/90 shadow-sm",glow:"bg-gradient-brand text-primary-foreground shadow-glow hover:shadow-elegant transition-all duration-300"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),F=d.forwardRef(({className:t,variant:r,size:o,asChild:a=!1,...c},p)=>{const u=a?rr:"button";return n.jsxDEV(u,{className:S(Ar({variant:r,size:o,className:t})),ref:p,...c},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/button.tsx",lineNumber:49,columnNumber:7},void 0)});F.displayName="Button";const Lr=({children:t,showNav:r=!0})=>{const o=be(),a=He(),{logout:c}=xe(),{toast:p}=Ne(),u=x=>o.pathname===x,g=()=>{c(),p({title:"退出成功",description:"您已安全退出系统"}),a("/login",{replace:!0})};return r?n.jsxDEV("div",{className:"min-h-screen bg-background",children:[n.jsxDEV("header",{className:"border-b border-github-border bg-card",children:n.jsxDEV("div",{className:"container mx-auto px-4 py-4",children:n.jsxDEV("div",{className:"flex items-center justify-between",children:[n.jsxDEV("div",{className:"flex items-center gap-4",children:n.jsxDEV(J,{to:"/dashboard",className:"flex items-center gap-3",children:[n.jsxDEV(dt,{className:"w-8 h-8 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:43,columnNumber:17},void 0),n.jsxDEV("h1",{className:"text-xl font-bold text-foreground",children:"Commit Inspector"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:44,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:42,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:41,columnNumber:13},void 0),n.jsxDEV("nav",{className:"flex items-center gap-2",children:[n.jsxDEV(F,{variant:u("/dashboard")?"default":"ghost",size:"sm",asChild:!0,className:u("/dashboard")?"":"text-foreground hover:text-primary",children:n.jsxDEV(J,{to:"/dashboard",className:"flex items-center gap-2",children:[n.jsxDEV(ir,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:58,columnNumber:19},void 0),"仪表盘"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:57,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:51,columnNumber:15},void 0),n.jsxDEV(F,{variant:u("/statistics")?"default":"ghost",size:"sm",asChild:!0,className:u("/statistics")?"":"text-foreground hover:text-primary",children:n.jsxDEV(J,{to:"/statistics",className:"flex items-center gap-2",children:[n.jsxDEV(ar,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:70,columnNumber:19},void 0),"统计"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:69,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:63,columnNumber:15},void 0),n.jsxDEV(F,{variant:u("/settings")?"default":"ghost",size:"sm",asChild:!0,className:u("/settings")?"":"text-foreground hover:text-primary",children:n.jsxDEV(J,{to:"/settings",className:"flex items-center gap-2",children:[n.jsxDEV(cr,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:82,columnNumber:19},void 0),"设置"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:81,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:75,columnNumber:15},void 0),n.jsxDEV(F,{variant:"ghost",size:"sm",onClick:g,className:"flex items-center gap-2 text-foreground hover:text-primary",children:[n.jsxDEV(ur,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:93,columnNumber:17},void 0),"退出"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:87,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:50,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:40,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:39,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:38,columnNumber:7},void 0),n.jsxDEV("main",{className:"container mx-auto px-4 py-6",children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:102,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:36,columnNumber:5},void 0):n.jsxDEV("div",{className:"min-h-screen bg-background",children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/Layout.tsx",lineNumber:32,columnNumber:12},void 0)},mt=d.forwardRef(({className:t,type:r,...o},a)=>n.jsxDEV("input",{type:r,className:S("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...o},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/input.tsx",lineNumber:8,columnNumber:7},void 0));mt.displayName="Input";const ft=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("div",{ref:o,className:S("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:9,columnNumber:3},void 0));ft.displayName="Card";const pt=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("div",{ref:o,className:S("flex flex-col space-y-1.5 p-6",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:24,columnNumber:3},void 0));pt.displayName="CardHeader";const vt=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("h3",{ref:o,className:S("text-2xl font-semibold leading-none tracking-tight",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:36,columnNumber:3},void 0));vt.displayName="CardTitle";const gt=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("p",{ref:o,className:S("text-sm text-muted-foreground",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:51,columnNumber:3},void 0));gt.displayName="CardDescription";const bt=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("div",{ref:o,className:S("p-6 pt-0",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:63,columnNumber:3},void 0));bt.displayName="CardContent";const Or=d.forwardRef(({className:t,...r},o)=>n.jsxDEV("div",{ref:o,className:S("flex items-center p-6 pt-0",t),...r},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/card.tsx",lineNumber:71,columnNumber:3},void 0));Or.displayName="CardFooter";const kr=()=>{const[t,r]=d.useState(""),[o,a]=d.useState(!1),[c,p]=d.useState(!1),u=He(),g=be(),{toast:x}=Ne(),{user:E,login:w}=xe();d.useEffect(()=>{var P,V;if(E.isAuthenticated){const O=((V=(P=g.state)==null?void 0:P.from)==null?void 0:V.pathname)||"/dashboard";u(O,{replace:!0})}},[E.isAuthenticated,u,g]);const h=async P=>{var V,O;P.preventDefault(),p(!0);try{if(await w(t)){x({title:"登录成功",description:"欢迎使用 GitHub 提交审查工具"});const re=((O=(V=g.state)==null?void 0:V.from)==null?void 0:O.pathname)||"/dashboard";u(re,{replace:!0})}else x({title:"登录失败",description:"密码错误，请重试",variant:"destructive"})}catch{x({title:"登录失败",description:"网络连接失败，请检查网络连接",variant:"destructive"})}finally{p(!1)}};return n.jsxDEV(Lr,{showNav:!1,children:n.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gradient-dark",children:n.jsxDEV(ft,{className:"w-full max-w-md mx-4 shadow-elegant",children:[n.jsxDEV(pt,{className:"text-center space-y-4",children:[n.jsxDEV("div",{className:"flex justify-center",children:n.jsxDEV("div",{className:"p-3 bg-gradient-brand rounded-full shadow-glow",children:n.jsxDEV(dt,{className:"w-8 h-8 text-white"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:70,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:69,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:68,columnNumber:13},void 0),n.jsxDEV("div",{children:[n.jsxDEV(vt,{className:"text-2xl font-bold",children:"GitHub 提交检查器"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:74,columnNumber:15},void 0),n.jsxDEV(gt,{className:"text-muted-foreground mt-2",children:"专业的代码审查和提交差异分析工具"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:77,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:73,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:67,columnNumber:11},void 0),n.jsxDEV(bt,{children:[n.jsxDEV("form",{onSubmit:h,className:"space-y-6",children:[n.jsxDEV("div",{className:"space-y-2",children:[n.jsxDEV("label",{htmlFor:"password",className:"text-sm font-medium text-foreground",children:"管理员密码"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:86,columnNumber:17},void 0),n.jsxDEV("div",{className:"relative",children:[n.jsxDEV(mt,{id:"password",type:o?"text":"password",value:t,onChange:P=>r(P.target.value),placeholder:"请输入系统密码",className:"pr-10",required:!0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:90,columnNumber:19},void 0),n.jsxDEV(F,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>a(!o),children:o?n.jsxDEV(lr,{className:"h-4 w-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:107,columnNumber:23},void 0):n.jsxDEV(dr,{className:"h-4 w-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:109,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:99,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:89,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:85,columnNumber:15},void 0),n.jsxDEV(F,{type:"submit",variant:"glow",size:"lg",className:"w-full",disabled:c,children:c?n.jsxDEV("div",{className:"flex items-center gap-2",children:[n.jsxDEV("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:124,columnNumber:21},void 0),"验证中..."]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:123,columnNumber:19},void 0):n.jsxDEV("div",{className:"flex items-center gap-2",children:[n.jsxDEV(mr,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:129,columnNumber:21},void 0),"安全登录"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:128,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:115,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:84,columnNumber:13},void 0),n.jsxDEV("div",{className:"mt-6 pt-6 border-t border-border",children:n.jsxDEV("div",{className:"text-center text-sm text-muted-foreground",children:n.jsxDEV("p",{children:["提示：演示密码为 ",n.jsxDEV("code",{className:"bg-muted px-1 py-0.5 rounded text-xs",children:"admin123"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:138,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:138,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:137,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:136,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:83,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:66,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:65,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Login.tsx",lineNumber:64,columnNumber:5},void 0)},Ir=d.lazy(()=>L(()=>import("./Dashboard-BZUbbtLo.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),$r=d.lazy(()=>L(()=>import("./Statistics-4h3TH0kC.js"),__vite__mapDeps([7,1,2,3,4,5,8,9,6]))),Fr=d.lazy(()=>L(()=>import("./Settings-DBpcXiPz.js"),__vite__mapDeps([10,1,4,3,2,8,5,6]))),Ur=d.lazy(()=>L(()=>import("./SearchResults-CY7_gO4U.js"),__vite__mapDeps([11,1,2,3,4,5,6]))),zr=d.lazy(()=>L(()=>import("./Repository-jdeJgS9o.js"),__vite__mapDeps([12,1,2,3,8,4,13,14,5,6]))),Mr=d.lazy(()=>L(()=>import("./Commit-CJPMizUI.js"),__vite__mapDeps([15,1,2,3,13,4,5,6]))),Br=d.lazy(()=>L(()=>import("./FileDiff-BgaImzb1.js"),__vite__mapDeps([16,1,2,3,14,4,5,6]))),Wr=d.lazy(()=>L(()=>import("./NotFound-Bcz6IzHq.js"),__vite__mapDeps([17,1,4,3,5,6]))),Yr=new vr,qr=()=>(d.useEffect(()=>(document.documentElement.classList.add("dark"),()=>{document.documentElement.classList.remove("dark")}),[]),n.jsxDEV(gr,{client:Yr,children:n.jsxDEV(Sr,{children:n.jsxDEV(Tr,{children:n.jsxDEV("div",{children:[n.jsxDEV(wr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:39,columnNumber:13},void 0),n.jsxDEV(Dr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:40,columnNumber:13},void 0),n.jsxDEV(Qt,{children:n.jsxDEV(d.Suspense,{fallback:n.jsxDEV("div",{className:"flex items-center justify-center min-h-screen",children:n.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:42,columnNumber:98},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:42,columnNumber:35},void 0),children:n.jsxDEV(Zt,{children:[n.jsxDEV(A,{path:"/",element:n.jsxDEV(Ge,{to:"/dashboard",replace:!0},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:44,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:44,columnNumber:19},void 0),n.jsxDEV(A,{path:"/login",element:n.jsxDEV(kr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:45,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:45,columnNumber:19},void 0),n.jsxDEV(A,{path:"/dashboard",element:n.jsxDEV($,{children:n.jsxDEV(Ir,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:48,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:47,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:46,columnNumber:19},void 0),n.jsxDEV(A,{path:"/statistics",element:n.jsxDEV($,{children:n.jsxDEV($r,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:53,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:52,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:51,columnNumber:19},void 0),n.jsxDEV(A,{path:"/settings",element:n.jsxDEV($,{children:n.jsxDEV(Fr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:58,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:57,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:56,columnNumber:19},void 0),n.jsxDEV(A,{path:"/search",element:n.jsxDEV($,{children:n.jsxDEV(Ur,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:63,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:62,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:61,columnNumber:19},void 0),n.jsxDEV(A,{path:"/repository/:owner/:name",element:n.jsxDEV($,{children:n.jsxDEV(zr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:68,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:67,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:66,columnNumber:19},void 0),n.jsxDEV(A,{path:"/commit/:hash",element:n.jsxDEV($,{children:n.jsxDEV(Mr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:73,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:72,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:71,columnNumber:19},void 0),n.jsxDEV(A,{path:"/commit/:hash/file/:filename",element:n.jsxDEV($,{children:n.jsxDEV(Br,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:78,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:77,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:76,columnNumber:19},void 0),n.jsxDEV(A,{path:"*",element:n.jsxDEV(Wr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:81,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:81,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:43,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:42,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:41,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:38,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:37,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:36,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/App.tsx",lineNumber:35,columnNumber:5},void 0));ot(document.getElementById("root")).render(n.jsxDEV(qr,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/main.tsx",lineNumber:5,columnNumber:53},void 0));export{F as B,ft as C,dt as G,mt as I,Lr as L,bt as a,Ar as b,S as c,pt as d,vt as e,gt as f,Zr as g,en as h,n as j,Qr as r,tn as s,ee as t,Ne as u};

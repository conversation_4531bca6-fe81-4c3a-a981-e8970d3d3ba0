using System.Text.Json;
using System.Net.Http.Headers;

namespace CommitInspectServer.Services;

public interface IGitHubService
{
    Task<GitHubSearchResult> SearchRepositoriesAsync(string query, int page = 1, int perPage = 30);
    Task<List<GitHubRepository>> GetUserRepositoriesAsync(int page = 1, int perPage = 100);
    Task<List<GitHubRepository>> GetOrganizationRepositoriesAsync(string org, int page = 1, int perPage = 100);
}

public class GitHubService : IGitHubService
{
    private readonly HttpClient _httpClient;
    private readonly string _personalAccessToken;

    public GitHubService(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _personalAccessToken = configuration["GitHub:PersonalAccessToken"] ?? throw new ArgumentException("GitHub Personal Access Token is required");
        
        _httpClient.BaseAddress = new Uri("https://api.github.com/");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "CommitInspectServer");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("token", _personalAccessToken);
    }

    public async Task<GitHubSearchResult> SearchRepositoriesAsync(string query, int page = 1, int perPage = 30)
    {
        try
        {
            var searchQuery = $"user:@me {query}";
            var url = $"search/repositories?q={Uri.EscapeDataString(searchQuery)}&page={page}&per_page={perPage}&sort=updated&order=desc";
            
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var searchResult = JsonSerializer.Deserialize<GitHubSearchResult>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return searchResult ?? new GitHubSearchResult { TotalCount = 0, Items = new List<GitHubRepository>() };
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to search GitHub repositories: {ex.Message}", ex);
        }
    }

    public async Task<List<GitHubRepository>> GetUserRepositoriesAsync(int page = 1, int perPage = 100)
    {
        try
        {
            var url = $"user/repos?page={page}&per_page={perPage}&sort=updated&affiliation=owner,collaborator,organization_member";
            
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var repositories = JsonSerializer.Deserialize<List<GitHubRepository>>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return repositories ?? new List<GitHubRepository>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to get user repositories: {ex.Message}", ex);
        }
    }

    public async Task<List<GitHubRepository>> GetOrganizationRepositoriesAsync(string org, int page = 1, int perPage = 100)
    {
        try
        {
            var url = $"orgs/{org}/repos?page={page}&per_page={perPage}&sort=updated";
            
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var repositories = JsonSerializer.Deserialize<List<GitHubRepository>>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return repositories ?? new List<GitHubRepository>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to get organization repositories: {ex.Message}", ex);
        }
    }
}

public class GitHubSearchResult
{
    public int TotalCount { get; set; }
    public List<GitHubRepository> Items { get; set; } = new();
}

public class GitHubRepository
{
    public long Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Private { get; set; }
    public GitHubOwner Owner { get; set; } = new();
    public string HtmlUrl { get; set; } = string.Empty;
    public int StargazersCount { get; set; }
    public int ForksCount { get; set; }
    public string Language { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? PushedAt { get; set; }
    public string DefaultBranch { get; set; } = "main";
}

public class GitHubOwner
{
    public long Id { get; set; }
    public string Login { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
}
import React, { useState, useMemo, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { 
  TrendingUp, 
  GitCommit, 
  Users, 
  Code, 
  FileText, 
  Calendar as CalendarIcon,
  Star,
  GitBranch,
  Activity,
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { statisticsApi } from '@/lib/api';
import { toast } from '@/hooks/use-toast';

// 类型定义
interface OverviewStats {
  totalCommits: number;
  totalRepositories: number;
  totalContributors: number;
  totalCodeLines: number;
  totalFiles: number;
}

interface CommitTrendData {
  date: string;
  commits: number;
  timestamp: Date;
}

interface ContributorData {
  name: string;
  commits: number;
  additions: number;
  deletions: number;
  timestamp: Date;
  percentage: number;
  color: string;
}

interface LanguageData {
  name: string;
  value: number;
  color: string;
}

interface FileTypeData {
  type: string;
  count: number;
  percentage: number;
}

// 快速日期范围选项
const quickDateRanges = [
  { label: '过去7天', days: 7 },
  { label: '过去30天', days: 30 },
  { label: '过去3个月', days: 90 },
  { label: '过去6个月', days: 180 },
  { label: '过去1年', days: 365 },
  { label: '今年', value: 'thisYear' },
  { label: '去年', value: 'lastYear' },
  { label: '本月', value: 'thisMonth' },
  { label: '上个月', value: 'lastMonth' },
];

// 获取快速日期范围的实际日期
const getQuickDateRange = (value: string | number) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  if (typeof value === 'number') {
    const startDate = new Date(today);
    startDate.setDate(startDate.getDate() - value);
    return { startDate, endDate: today };
  }
  
  switch (value) {
    case 'thisYear':
      return {
        startDate: new Date(now.getFullYear(), 0, 1),
        endDate: today
      };
    case 'lastYear':
      return {
        startDate: new Date(now.getFullYear() - 1, 0, 1),
        endDate: new Date(now.getFullYear() - 1, 11, 31)
      };
    case 'thisMonth':
      return {
        startDate: new Date(now.getFullYear(), now.getMonth(), 1),
        endDate: today
      };
    case 'lastMonth':
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      return {
        startDate: lastMonth,
        endDate: new Date(now.getFullYear(), now.getMonth(), 0)
      };
    default:
      return null;
  }
};

// Enhanced Date Range Picker Component with Quick Select
const DateRangePicker = ({ 
  date, 
  setDate, 
  placeholder 
}: { 
  date: Date | undefined; 
  setDate: (date: Date | undefined) => void; 
  placeholder: string;
}) => {
  const [month, setMonth] = useState<Date>(date || new Date());
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-[160px] justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "yyyy-MM-dd", { locale: zhCN }) : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 border-b">
          <div className="flex items-center justify-between gap-2 mb-2">
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => {
                const newMonth = new Date(month);
                newMonth.setMonth(newMonth.getMonth() - 1);
                setMonth(newMonth);
              }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center gap-1">
              <Select
                value={month.getFullYear().toString()}
                onValueChange={(year) => {
                  const newMonth = new Date(month);
                  newMonth.setFullYear(parseInt(year));
                  setMonth(newMonth);
                }}
              >
                <SelectTrigger className="w-[80px] h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => {
                    const year = new Date().getFullYear() - 5 + i;
                    return (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              
              <Select
                value={month.getMonth().toString()}
                onValueChange={(monthIndex) => {
                  const newMonth = new Date(month);
                  newMonth.setMonth(parseInt(monthIndex));
                  setMonth(newMonth);
                }}
              >
                <SelectTrigger className="w-[60px] h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => (
                    <SelectItem key={i} value={i.toString()}>
                      {i + 1}月
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => {
                const newMonth = new Date(month);
                newMonth.setMonth(newMonth.getMonth() + 1);
                setMonth(newMonth);
              }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="text-center text-sm text-muted-foreground">
            {format(month, "yyyy年M月", { locale: zhCN })}
          </div>
        </div>
        
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          month={month}
          onMonthChange={setMonth}
          initialFocus
          className="p-3"
        />
      </PopoverContent>
    </Popover>
  );
};

const Statistics = () => {
  // Date range state
  const [startDate, setStartDate] = useState<Date | undefined>((() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 3);
    return date;
  })());
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());

  // Data state
  const [loading, setLoading] = useState(true);
  const [overviewStats, setOverviewStats] = useState<OverviewStats | null>(null);
  const [commitTrendData, setCommitTrendData] = useState<CommitTrendData[]>([]);
  const [contributorData, setContributorData] = useState<ContributorData[]>([]);
  const [languageData, setLanguageData] = useState<LanguageData[]>([]);
  const [fileTypeData, setFileTypeData] = useState<FileTypeData[]>([]);

  // Load all statistics data
  const loadStatisticsData = async () => {
    try {
      setLoading(true);
      
      // 并行加载所有统计数据
      const [
        overviewResponse,
        trendsResponse,
        contributorsResponse,
        languagesResponse,
        fileTypesResponse
      ] = await Promise.all([
        statisticsApi.getOverviewStatistics(startDate, endDate),
        statisticsApi.getCommitTrends(startDate, endDate),
        statisticsApi.getContributorStatistics(startDate, endDate, 10),
        statisticsApi.getLanguageStatistics(),
        statisticsApi.getFileTypeStatistics()
      ]);

      if (overviewResponse.success) {
        setOverviewStats(overviewResponse.data);
      }

      if (trendsResponse.success) {
        setCommitTrendData(trendsResponse.data.map(item => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      }

      if (contributorsResponse.success) {
        setContributorData(contributorsResponse.data.map(item => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      }

      if (languagesResponse.success) {
        setLanguageData(languagesResponse.data);
      }

      if (fileTypesResponse.success) {
        setFileTypeData(fileTypesResponse.data);
      }

    } catch (error) {
      console.error('Failed to load statistics data:', error);
      toast({
        title: "加载失败",
        description: "无法加载统计数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when date range changes
  useEffect(() => {
    loadStatisticsData();
  }, [startDate, endDate]);

  // 创建贡献者提交分布数据（饼图用）
  const contributorCommitsData = useMemo(() => {
    if (!contributorData.length) return [];
    
    return contributorData.map(contributor => ({
      name: contributor.name,
      commits: contributor.commits,
      percentage: contributor.percentage,
      color: contributor.color
    }));
  }, [contributorData]);

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      </Layout>
    );
  }


  return (
    <Layout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-3">
            <BarChart className="w-8 h-8 text-primary" />
            <div>
              <h2 className="text-3xl font-bold text-foreground">
                仓库统计
              </h2>
              <p className="text-muted-foreground mt-1">
                深入分析代码仓库的各项数据和趋势
              </p>
            </div>
          </div>

          {/* Date Range Picker */}
          <Card className="p-4">
            <div className="space-y-3">
              {/* Quick Date Range Selector */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                <span className="text-sm font-medium text-foreground">快速选择:</span>
                <div className="flex items-center gap-2">
                  <Select
                    onValueChange={(value) => {
                      const range = getQuickDateRange(
                        quickDateRanges.find(r => r.label === value)?.days || 
                        quickDateRanges.find(r => r.label === value)?.value || ''
                      );
                      if (range) {
                        setStartDate(range.startDate);
                        setEndDate(range.endDate);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="选择时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      {quickDateRanges.map((range) => (
                        <SelectItem key={range.label} value={range.label}>
                          {range.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const now = new Date();
                      const threeMonthsAgo = new Date();
                      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                      setStartDate(threeMonthsAgo);
                      setEndDate(now);
                    }}
                  >
                    重置
                  </Button>
                </div>
              </div>
              
              {/* Custom Date Range */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                <span className="text-sm font-medium text-foreground">自定义:</span>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                  <DateRangePicker 
                    date={startDate} 
                    setDate={setStartDate} 
                    placeholder="开始日期" 
                  />
                  <span className="text-sm text-muted-foreground hidden sm:block">至</span>
                  <DateRangePicker 
                    date={endDate} 
                    setDate={setEndDate} 
                    placeholder="结束日期" 
                  />
                </div>
              </div>
              
              {/* Current Selection Display */}
              {startDate && endDate && (
                <div className="text-xs text-muted-foreground bg-muted/50 rounded px-2 py-1">
                  已选择: {format(startDate, "yyyy年M月d日", { locale: zhCN })} 至 {format(endDate, "yyyy年M月d日", { locale: zhCN })}
                  （共 {Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))} 天）
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-full">
                  <GitCommit className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{overviewStats?.totalCommits.toLocaleString() || 0}</p>
                  <p className="text-sm text-muted-foreground">总提交数</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-github-success/10 rounded-full">
                  <Users className="w-6 h-6 text-github-success" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{overviewStats?.totalContributors || 0}</p>
                  <p className="text-sm text-muted-foreground">贡献者</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-github-warning/10 rounded-full">
                  <Code className="w-6 h-6 text-github-warning" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{overviewStats?.totalCodeLines.toLocaleString() || 0}</p>
                  <p className="text-sm text-muted-foreground">代码行数</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-github-danger/10 rounded-full">
                  <FileText className="w-6 h-6 text-github-danger" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{overviewStats?.totalFiles.toLocaleString() || 0}</p>
                  <p className="text-sm text-muted-foreground">文件总数</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Language Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                语言分布
              </CardTitle>
              <CardDescription>
                代码库中各种编程语言的占比
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={languageData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {languageData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value}%`, '占比']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex flex-wrap gap-2 mt-4">
                {languageData.map((lang) => (
                  <Badge key={lang.name} variant="outline" className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: lang.color }}
                    />
                    {lang.name} {lang.value}%
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Commit Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                提交趋势
              </CardTitle>
              <CardDescription>
                {startDate && endDate 
                  ? `${format(startDate, "yyyy年M月d日", { locale: zhCN })} 至 ${format(endDate, "yyyy年M月d日", { locale: zhCN })} 的提交活动趋势`
                  : "过去3个月的提交活动趋势"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={commitTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="commits" 
                      stroke="hsl(var(--primary))" 
                      fill="hsl(var(--primary))" 
                      fillOpacity={0.2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contributors and Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Contributors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                主要贡献者
              </CardTitle>
              <CardDescription>
                {startDate && endDate 
                  ? `${format(startDate, "yyyy年M月", { locale: zhCN })} 至 ${format(endDate, "yyyy年M月", { locale: zhCN })} 的主要贡献者`
                  : "按提交数量排序的前5位贡献者"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contributorData.map((contributor, index) => (
                  <div key={contributor.name} className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <p className="font-medium text-foreground">{contributor.name}</p>
                        <Badge variant="secondary">{contributor.commits} commits</Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="text-github-success">+{contributor.additions.toLocaleString()}</span>
                        <span className="text-github-danger">-{contributor.deletions.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Contributors Contribution Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                贡献者提交占比
              </CardTitle>
              <CardDescription>
                {startDate && endDate 
                  ? `${format(startDate, "yyyy年M月", { locale: zhCN })} 至 ${format(endDate, "yyyy年M月", { locale: zhCN })} 各贡献者的提交分布情况`
                  : "过去6个月各贡献者的提交分布情况"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={contributorCommitsData}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      paddingAngle={2}
                      dataKey="commits"
                    >
                      {contributorCommitsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      formatter={(value, name) => [
                        `${value} commits (${contributorCommitsData.find(d => d.commits === value)?.percentage}%)`, 
                        '提交数'
                      ]} 
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex flex-wrap gap-2 mt-4">
                {contributorCommitsData.map((contributor) => (
                  <Badge key={contributor.name} variant="outline" className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: contributor.color }}
                    />
                    {contributor.name} {contributor.percentage}%
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* File Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              文件类型分布
            </CardTitle>
            <CardDescription>
              不同文件类型在代码库中的分布情况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fileTypeData.map((fileType) => (
                <div key={fileType.type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-foreground">{fileType.type}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">{fileType.count} 文件</span>
                      <Badge variant="outline">{fileType.percentage}%</Badge>
                    </div>
                  </div>
                  <Progress value={fileType.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Statistics;
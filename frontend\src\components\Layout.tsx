import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { GitHubLogo } from './GitHubLogo';
import { Button } from './ui/button';
import { Home, BarChart3, Settings, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface LayoutProps {
  children: React.ReactNode;
  showNav?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({ children, showNav = true }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const { toast } = useToast();

  const isActive = (path: string) => location.pathname === path;

  const handleLogout = () => {
    logout();
    toast({
      title: "退出成功",
      description: "您已安全退出系统",
    });
    navigate('/login', { replace: true });
  };

  if (!showNav) {
    return <div className="min-h-screen bg-background">{children}</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-github-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/dashboard" className="flex items-center gap-3">
                <GitHubLogo className="w-8 h-8 text-primary" />
                <h1 className="text-xl font-bold text-foreground">
                  Commit Inspector
                </h1>
              </Link>
            </div>
            
            <nav className="flex items-center gap-2">
              <Button
                variant={isActive('/dashboard') ? 'default' : 'ghost'}
                size="sm"
                asChild
                className={!isActive('/dashboard') ? 'text-foreground hover:text-primary' : ''}
              >
                <Link to="/dashboard" className="flex items-center gap-2">
                  <Home className="w-4 h-4" />
                  仪表盘
                </Link>
              </Button>
              
              <Button
                variant={isActive('/statistics') ? 'default' : 'ghost'}
                size="sm"
                asChild
                className={!isActive('/statistics') ? 'text-foreground hover:text-primary' : ''}
              >
                <Link to="/statistics" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  统计
                </Link>
              </Button>
              
              <Button
                variant={isActive('/settings') ? 'default' : 'ghost'}
                size="sm"
                asChild
                className={!isActive('/settings') ? 'text-foreground hover:text-primary' : ''}
              >
                <Link to="/settings" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  设置
                </Link>
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleLogout}
                className="flex items-center gap-2 text-foreground hover:text-primary"
              >
                <LogOut className="w-4 h-4" />
                退出
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        {children}
      </main>
    </div>
  );
};
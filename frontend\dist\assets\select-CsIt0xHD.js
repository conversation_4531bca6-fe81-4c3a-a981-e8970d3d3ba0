import{j as e,c as o}from"./index-D3FXQGgv.js";import{r as i}from"./react-vendor-DucYQ77b.js";import{x as c,y as j,z as a,B as m,E as h,F as l,G as v,L as d,H as u,J as y,K as g,M as f,N as w,Q as C}from"./radix-ui-DdAvwPj2.js";import{ak as p,al as D,am as E}from"./utils-CUkY6B8V.js";const T=w,U=C,V=i.forwardRef(({className:s,children:n,...t},r)=>e.jsxDEV(c,{ref:r,className:o("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...t,children:[n,e.jsxDEV(j,{asChild:!0,children:e.jsxDEV(p,{className:"h-4 w-4 opacity-50"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:27,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:26,columnNumber:5},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:17,columnNumber:3},void 0));V.displayName=c.displayName;const N=i.forwardRef(({className:s,...n},t)=>e.jsxDEV(a,{ref:t,className:o("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsxDEV(D,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:45,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:37,columnNumber:3},void 0));N.displayName=a.displayName;const x=i.forwardRef(({className:s,...n},t)=>e.jsxDEV(m,{ref:t,className:o("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsxDEV(p,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:62,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:54,columnNumber:3},void 0));x.displayName=m.displayName;const P=i.forwardRef(({className:s,children:n,position:t="popper",...r},b)=>e.jsxDEV(h,{children:e.jsxDEV(l,{ref:b,className:o("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:t,...r,children:[e.jsxDEV(N,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:84,columnNumber:7},void 0),e.jsxDEV(v,{className:o("p-1",t==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:85,columnNumber:7},void 0),e.jsxDEV(x,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:94,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:73,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:72,columnNumber:3},void 0));P.displayName=l.displayName;const S=i.forwardRef(({className:s,...n},t)=>e.jsxDEV(d,{ref:t,className:o("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:104,columnNumber:3},void 0));S.displayName=d.displayName;const R=i.forwardRef(({className:s,children:n,...t},r)=>e.jsxDEV(u,{ref:r,className:o("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...t,children:[e.jsxDEV("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsxDEV(y,{children:e.jsxDEV(E,{className:"h-4 w-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:126,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:125,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:124,columnNumber:5},void 0),e.jsxDEV(g,{children:n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:130,columnNumber:5},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:116,columnNumber:3},void 0));R.displayName=u.displayName;const I=i.forwardRef(({className:s,...n},t)=>e.jsxDEV(f,{ref:t,className:o("-mx-1 my-1 h-px bg-muted",s),...n},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/select.tsx",lineNumber:139,columnNumber:3},void 0));I.displayName=f.displayName;export{T as S,V as a,U as b,P as c,R as d};

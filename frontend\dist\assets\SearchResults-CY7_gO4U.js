import{u as k,j as e,L,B as g,I as A,C as d,a as f,d as B,G as K,e as T,f as G,r as j}from"./index-D3FXQGgv.js";import{i as H,e as I,r as i}from"./react-vendor-DucYQ77b.js";import{B as P}from"./badge-BHFrTODj.js";import{ad as Q,U as o,ae as z,W as U,Y as q,Z as W,af as Y}from"./utils-CUkY6B8V.js";import"./radix-ui-DdAvwPj2.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const te=()=>{const[D]=H(),v=I(),{toast:N}=k(),[a,x]=i.useState(""),[p,l]=i.useState([]),[h,C]=i.useState(!1),[V,m]=i.useState(0),[Z,w]=i.useState(1),[u,b]=i.useState(null),[S,R]=i.useState(new Set),n=D.get("q")||"",y=async(s,r=1)=>{if(!s.trim()){l([]),m(0);return}C(!0),b(null);try{const t=await j.searchRepositories(s,r,30);l(t.repositories),m(t.totalCount),w(r)}catch(t){b(t instanceof Error?t.message:"搜索失败"),l([]),m(0)}finally{C(!1)}};i.useEffect(()=>{n?(y(n,1),x(n)):(l([]),m(0),b(null),x(""))},[n]),i.useEffect(()=>{(async()=>{try{const r=await j.getFollowedRepositories({showToast:!1});R(new Set(r))}catch(r){console.error("Failed to load followed repositories:",r)}})()},[]);const E=()=>{a.trim()&&v(`/search?q=${encodeURIComponent(a.trim())}`)},F=s=>{s.key==="Enter"&&E()},$=async(s,r)=>{s.preventDefault(),s.stopPropagation();const t=`${r.owner}/${r.name}`;if(S.has(t)){N({title:"已关注",description:`您已经关注了仓库 ${t}`,variant:"default"});return}try{await j.followRepository(r.owner,r.name),R(c=>new Set([...c,t])),N({title:"关注成功",description:`已成功关注仓库 ${t}`})}catch(c){console.error("Failed to follow repository:",c),N({title:"关注失败",description:c instanceof Error?c.message:"请稍后重试",variant:"destructive"})}};return e.jsxDEV(L,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex flex-col gap-4",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:e.jsxDEV(g,{variant:"ghost",size:"sm",onClick:()=>v("/dashboard"),className:"flex items-center gap-2",children:[e.jsxDEV(Q,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:142,columnNumber:15},void 0),"返回仪表盘"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:136,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:135,columnNumber:11},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-3xl font-bold text-foreground",children:"搜索结果"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:148,columnNumber:13},void 0),e.jsxDEV("p",{className:"text-muted-foreground mt-1",children:n?`搜索 "${n}" 的结果`:"请输入搜索关键词"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:151,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:147,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex items-center gap-3 max-w-2xl",children:[e.jsxDEV("div",{className:"relative flex-1",children:[e.jsxDEV(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:159,columnNumber:15},void 0),e.jsxDEV(A,{placeholder:"搜索仓库...",value:a,onChange:s=>x(s.target.value),onKeyPress:F,className:"pl-10"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:160,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:158,columnNumber:13},void 0),e.jsxDEV(g,{onClick:E,disabled:!a.trim(),className:"flex items-center gap-2",children:[e.jsxDEV(o,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:173,columnNumber:15},void 0),"搜索"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:168,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:157,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:134,columnNumber:9},void 0),n&&e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"flex items-center gap-4",children:h?e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV(z,{className:"w-4 h-4 animate-spin"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:186,columnNumber:19},void 0),e.jsxDEV("span",{className:"text-sm text-muted-foreground",children:"搜索中..."},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:187,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:185,columnNumber:17},void 0):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(P,{variant:"outline",className:"text-sm",children:["找到 ",V," 个仓库"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:191,columnNumber:19},void 0),p.length>0&&e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:['匹配关键词: "',n,'"']},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:195,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:190,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:183,columnNumber:13},void 0),u&&e.jsxDEV(d,{children:e.jsxDEV(f,{className:"py-12 text-center",children:[e.jsxDEV(o,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:207,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-lg font-medium text-foreground mb-2",children:"搜索出错"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:208,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-muted-foreground",children:u},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:211,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:206,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:205,columnNumber:15},void 0),!h&&!u&&p.length>0?e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(s=>{const r=`${s.owner}/${s.name}`,t=S.has(r);return e.jsxDEV(d,{className:"hover:shadow-elegant transition-all duration-300 group h-full flex flex-col",children:[e.jsxDEV(B,{className:"pb-3",children:e.jsxDEV("div",{className:"flex items-start justify-between gap-3",children:[e.jsxDEV("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[e.jsxDEV(K,{className:"w-5 h-5 text-muted-foreground flex-shrink-0"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:230,columnNumber:29},void 0),e.jsxDEV("div",{className:"min-w-0 flex-1",children:[e.jsxDEV(T,{className:"text-lg group-hover:text-primary transition-colors truncate",children:[s.owner,"/",s.name]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:232,columnNumber:31},void 0),e.jsxDEV(G,{className:"text-sm line-clamp-2",children:s.description||"暂无描述"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:235,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:231,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:229,columnNumber:27},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 flex-shrink-0",children:e.jsxDEV(P,{variant:s.isActive?"default":"secondary",className:"whitespace-nowrap",children:s.isActive?"活跃":"暂停"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:241,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:240,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:228,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:227,columnNumber:23},void 0),e.jsxDEV(f,{className:"space-y-4 flex-1 flex flex-col justify-between",children:[e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center justify-between text-sm",children:[e.jsxDEV("div",{className:"flex items-center gap-2 text-muted-foreground",children:[e.jsxDEV(U,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:252,columnNumber:31},void 0),(s.stars||0).toLocaleString()]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:251,columnNumber:29},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-muted-foreground",children:[e.jsxDEV(q,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:256,columnNumber:31},void 0),s.language||"未知"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:255,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:250,columnNumber:27},void 0),e.jsxDEV("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[e.jsxDEV(W,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:262,columnNumber:29},void 0),"最后提交: ",s.lastCommit||"未知"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:261,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:249,columnNumber:25},void 0),e.jsxDEV("div",{className:"mt-auto pt-4",children:e.jsxDEV(g,{onClick:c=>$(c,s),disabled:t,className:`w-full flex items-center gap-2 ${t?"bg-github-success hover:bg-github-success/90 text-white":""}`,variant:t?"default":"outline",children:[e.jsxDEV(Y,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:278,columnNumber:29},void 0),t?"已关注":"添加关注"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:268,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:267,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:248,columnNumber:23},void 0)]},s.id,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:226,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:220,columnNumber:15},void 0):!h&&!u&&e.jsxDEV(d,{children:e.jsxDEV(f,{className:"py-12 text-center",children:[e.jsxDEV(o,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:290,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-lg font-medium text-foreground mb-2",children:"未找到匹配的仓库"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:291,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-muted-foreground",children:"尝试使用其他关键词或检查拼写"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:294,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:289,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:288,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:181,columnNumber:11},void 0),!n&&e.jsxDEV(d,{children:e.jsxDEV(f,{className:"py-12 text-center",children:[e.jsxDEV(o,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:307,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-lg font-medium text-foreground mb-2",children:"开始搜索仓库"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:308,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-muted-foreground",children:"在上方输入仓库名称、所有者或描述关键词进行搜索"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:311,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:306,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:305,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:132,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/SearchResults.tsx",lineNumber:131,columnNumber:5},void 0)};export{te as default};

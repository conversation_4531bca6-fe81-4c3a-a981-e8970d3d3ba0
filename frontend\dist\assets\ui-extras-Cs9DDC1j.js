import{r as D,R as m,b as At}from"./react-vendor-DucYQ77b.js";import{b as Oe,i as zt,a as ye,d as $t,e as ge,f as L,s as Te,g as R,h as J,j as F,k as $e,l as st,m as X,n as be,o as He,p as pt,q as Ye,r as Ht,t as Vt,u as Ae,v as Kt,w as Ut,x as Ie,y as lt,z as Xt,A as qt,B as Zt,C as yt,D as gt,E as Ve,F as Ke,G as Jt,H as Qt,I as ne,J as bt,K as Gt}from"./utils-CUkY6B8V.js";import{j as d}from"./radix-ui-DdAvwPj2.js";var dt=["light","dark"],ea="(prefers-color-scheme: dark)",ta=D.createContext(void 0),aa={setTheme:e=>{},themes:[]},Lr=()=>{var e;return(e=D.useContext(ta))!=null?e:aa};D.memo(({forcedTheme:e,storageKey:t,attribute:a,enableSystem:n,enableColorScheme:r,defaultTheme:o,value:i,attrs:l,nonce:u})=>{let f=o==="system",c=a==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(y=>`'${y}'`).join(",")})`};`:`var d=document.documentElement,n='${a}',s='setAttribute';`,p=r?dt.includes(o)&&o?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${o}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",s=(y,x=!1,w=!0)=>{let M=i?i[y]:y,_=x?y+"|| ''":`'${M}'`,T="";return r&&w&&!x&&dt.includes(y)&&(T+=`d.style.colorScheme = '${y}';`),a==="class"?x||M?T+=`c.add(${_})`:T+="null":M&&(T+=`d[s](n,${_})`),T},h=e?`!function(){${c}${s(e)}}()`:n?`!function(){try{${c}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${f})){var t='${ea}',m=window.matchMedia(t);if(m.media!==t||m.matches){${s("dark")}}else{${s("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${s(i?"x[e]":"e",!0)}}${f?"":"else{"+s(o,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${c}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${s(i?"x[e]":"e",!0)}}else{${s(o,!1,!1)};}${p}}catch(t){}}();`;return D.createElement("script",{nonce:u,dangerouslySetInnerHTML:{__html:h}})});var na=e=>{switch(e){case"success":return ia;case"info":return la;case"warning":return sa;case"error":return da;default:return null}},ra=Array(12).fill(0),oa=({visible:e})=>m.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},m.createElement("div",{className:"sonner-spinner"},ra.map((t,a)=>m.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${a}`})))),ia=m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},m.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),sa=m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},m.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),la=m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},m.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),da=m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},m.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ua=()=>{let[e,t]=m.useState(document.hidden);return m.useEffect(()=>{let a=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),e},ze=1,ca=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:a,...n}=e,r=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:ze++,o=this.toasts.find(l=>l.id===r),i=e.dismissible===void 0?!0:e.dismissible;return o?this.toasts=this.toasts.map(l=>l.id===r?(this.publish({...l,...e,id:r,title:a}),{...l,...e,id:r,dismissible:i,title:a}):l):this.addToast({title:a,...n,dismissible:i,id:r}),r},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(a=>a({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let a;t.loading!==void 0&&(a=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let n=e instanceof Promise?e:e(),r=a!==void 0;return n.then(async o=>{if(va(o)&&!o.ok){r=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${o.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${o.status}`):t.description;this.create({id:a,type:"error",message:i,description:l})}else if(t.success!==void 0){r=!1;let i=typeof t.success=="function"?await t.success(o):t.success,l=typeof t.description=="function"?await t.description(o):t.description;this.create({id:a,type:"success",message:i,description:l})}}).catch(async o=>{if(t.error!==void 0){r=!1;let i=typeof t.error=="function"?await t.error(o):t.error,l=typeof t.description=="function"?await t.description(o):t.description;this.create({id:a,type:"error",message:i,description:l})}}).finally(()=>{var o;r&&(this.dismiss(a),a=void 0),(o=t.finally)==null||o.call(t)}),a},this.custom=(e,t)=>{let a=(t==null?void 0:t.id)||ze++;return this.create({jsx:e(a),id:a,...t}),a},this.subscribers=[],this.toasts=[]}},B=new ca,fa=(e,t)=>{let a=(t==null?void 0:t.id)||ze++;return B.addToast({title:e,...t,id:a}),a},va=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",ma=fa,ha=()=>B.toasts;Object.assign(ma,{success:B.success,info:B.info,warning:B.warning,error:B.error,custom:B.custom,message:B.message,promise:B.promise,dismiss:B.dismiss,loading:B.loading},{getHistory:ha});function pa(e,{insertAt:t}={}){if(typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t==="top"&&a.firstChild?a.insertBefore(n,a.firstChild):a.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}pa(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ne(e){return e.label!==void 0}var ya=3,ga="32px",ba=4e3,wa=356,xa=14,ka=20,_a=200;function Ma(...e){return e.filter(Boolean).join(" ")}var Na=e=>{var t,a,n,r,o,i,l,u,f,c;let{invert:p,toast:s,unstyled:h,interacting:y,setHeights:x,visibleToasts:w,heights:M,index:_,toasts:T,expanded:q,removeToast:O,defaultRichColors:A,closeButton:re,style:oe,cancelButtonStyle:ce,actionButtonStyle:fe,className:H="",descriptionClassName:ie="",duration:ee,position:ve,gap:Q,loadingIcon:z,expandByDefault:g,classNames:v,icons:I,closeButtonAriaLabel:te="Close toast",pauseWhenPageIsHidden:N,cn:S}=e,[E,V]=m.useState(!1),[ae,W]=m.useState(!1),[Se,me]=m.useState(!1),[he,pe]=m.useState(!1),[jt,je]=m.useState(0),[Et,et]=m.useState(0),tt=m.useRef(null),se=m.useRef(null),Wt=_===0,Ot=_+1<=w,Y=s.type,le=s.dismissible!==!1,Tt=s.className||"",It=s.descriptionClassName||"",_e=m.useMemo(()=>M.findIndex(k=>k.toastId===s.id)||0,[M,s.id]),Lt=m.useMemo(()=>{var k;return(k=s.closeButton)!=null?k:re},[s.closeButton,re]),at=m.useMemo(()=>s.duration||ee||ba,[s.duration,ee]),Ee=m.useRef(0),de=m.useRef(0),nt=m.useRef(0),ue=m.useRef(null),[rt,Bt]=ve.split("-"),ot=m.useMemo(()=>M.reduce((k,j,C)=>C>=_e?k:k+j.height,0),[M,_e]),it=ua(),Rt=s.invert||p,We=Y==="loading";de.current=m.useMemo(()=>_e*Q+ot,[_e,ot]),m.useEffect(()=>{V(!0)},[]),m.useLayoutEffect(()=>{if(!E)return;let k=se.current,j=k.style.height;k.style.height="auto";let C=k.getBoundingClientRect().height;k.style.height=j,et(C),x(K=>K.find(U=>U.toastId===s.id)?K.map(U=>U.toastId===s.id?{...U,height:C}:U):[{toastId:s.id,height:C,position:s.position},...K])},[E,s.title,s.description,x,s.id]);let G=m.useCallback(()=>{W(!0),je(de.current),x(k=>k.filter(j=>j.toastId!==s.id)),setTimeout(()=>{O(s)},_a)},[s,O,x,de]);m.useEffect(()=>{if(s.promise&&Y==="loading"||s.duration===1/0||s.type==="loading")return;let k,j=at;return q||y||N&&it?(()=>{if(nt.current<Ee.current){let C=new Date().getTime()-Ee.current;j=j-C}nt.current=new Date().getTime()})():j!==1/0&&(Ee.current=new Date().getTime(),k=setTimeout(()=>{var C;(C=s.onAutoClose)==null||C.call(s,s),G()},j)),()=>clearTimeout(k)},[q,y,g,s,at,G,s.promise,Y,N,it]),m.useEffect(()=>{let k=se.current;if(k){let j=k.getBoundingClientRect().height;return et(j),x(C=>[{toastId:s.id,height:j,position:s.position},...C]),()=>x(C=>C.filter(K=>K.toastId!==s.id))}},[x,s.id]),m.useEffect(()=>{s.delete&&G()},[G,s.delete]);function Ft(){return I!=null&&I.loading?m.createElement("div",{className:"sonner-loader","data-visible":Y==="loading"},I.loading):z?m.createElement("div",{className:"sonner-loader","data-visible":Y==="loading"},z):m.createElement(oa,{visible:Y==="loading"})}return m.createElement("li",{"aria-live":s.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:se,className:S(H,Tt,v==null?void 0:v.toast,(t=s==null?void 0:s.classNames)==null?void 0:t.toast,v==null?void 0:v.default,v==null?void 0:v[Y],(a=s==null?void 0:s.classNames)==null?void 0:a[Y]),"data-sonner-toast":"","data-rich-colors":(n=s.richColors)!=null?n:A,"data-styled":!(s.jsx||s.unstyled||h),"data-mounted":E,"data-promise":!!s.promise,"data-removed":ae,"data-visible":Ot,"data-y-position":rt,"data-x-position":Bt,"data-index":_,"data-front":Wt,"data-swiping":Se,"data-dismissible":le,"data-type":Y,"data-invert":Rt,"data-swipe-out":he,"data-expanded":!!(q||g&&E),style:{"--index":_,"--toasts-before":_,"--z-index":T.length-_,"--offset":`${ae?jt:de.current}px`,"--initial-height":g?"auto":`${Et}px`,...oe,...s.style},onPointerDown:k=>{We||!le||(tt.current=new Date,je(de.current),k.target.setPointerCapture(k.pointerId),k.target.tagName!=="BUTTON"&&(me(!0),ue.current={x:k.clientX,y:k.clientY}))},onPointerUp:()=>{var k,j,C,K;if(he||!le)return;ue.current=null;let U=Number(((k=se.current)==null?void 0:k.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Me=new Date().getTime()-((j=tt.current)==null?void 0:j.getTime()),Yt=Math.abs(U)/Me;if(Math.abs(U)>=ka||Yt>.11){je(de.current),(C=s.onDismiss)==null||C.call(s,s),G(),pe(!0);return}(K=se.current)==null||K.style.setProperty("--swipe-amount","0px"),me(!1)},onPointerMove:k=>{var j;if(!ue.current||!le)return;let C=k.clientY-ue.current.y,K=k.clientX-ue.current.x,U=(rt==="top"?Math.min:Math.max)(0,C),Me=k.pointerType==="touch"?10:2;Math.abs(U)>Me?(j=se.current)==null||j.style.setProperty("--swipe-amount",`${C}px`):Math.abs(K)>Me&&(ue.current=null)}},Lt&&!s.jsx?m.createElement("button",{"aria-label":te,"data-disabled":We,"data-close-button":!0,onClick:We||!le?()=>{}:()=>{var k;G(),(k=s.onDismiss)==null||k.call(s,s)},className:S(v==null?void 0:v.closeButton,(r=s==null?void 0:s.classNames)==null?void 0:r.closeButton)},m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},m.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),m.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,s.jsx||m.isValidElement(s.title)?s.jsx||s.title:m.createElement(m.Fragment,null,Y||s.icon||s.promise?m.createElement("div",{"data-icon":"",className:S(v==null?void 0:v.icon,(o=s==null?void 0:s.classNames)==null?void 0:o.icon)},s.promise||s.type==="loading"&&!s.icon?s.icon||Ft():null,s.type!=="loading"?s.icon||(I==null?void 0:I[Y])||na(Y):null):null,m.createElement("div",{"data-content":"",className:S(v==null?void 0:v.content,(i=s==null?void 0:s.classNames)==null?void 0:i.content)},m.createElement("div",{"data-title":"",className:S(v==null?void 0:v.title,(l=s==null?void 0:s.classNames)==null?void 0:l.title)},s.title),s.description?m.createElement("div",{"data-description":"",className:S(ie,It,v==null?void 0:v.description,(u=s==null?void 0:s.classNames)==null?void 0:u.description)},s.description):null),m.isValidElement(s.cancel)?s.cancel:s.cancel&&Ne(s.cancel)?m.createElement("button",{"data-button":!0,"data-cancel":!0,style:s.cancelButtonStyle||ce,onClick:k=>{var j,C;Ne(s.cancel)&&le&&((C=(j=s.cancel).onClick)==null||C.call(j,k),G())},className:S(v==null?void 0:v.cancelButton,(f=s==null?void 0:s.classNames)==null?void 0:f.cancelButton)},s.cancel.label):null,m.isValidElement(s.action)?s.action:s.action&&Ne(s.action)?m.createElement("button",{"data-button":!0,"data-action":!0,style:s.actionButtonStyle||fe,onClick:k=>{var j,C;Ne(s.action)&&(k.defaultPrevented||((C=(j=s.action).onClick)==null||C.call(j,k),G()))},className:S(v==null?void 0:v.actionButton,(c=s==null?void 0:s.classNames)==null?void 0:c.actionButton)},s.action.label):null))};function ut(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Br=e=>{let{invert:t,position:a="bottom-right",hotkey:n=["altKey","KeyT"],expand:r,closeButton:o,className:i,offset:l,theme:u="light",richColors:f,duration:c,style:p,visibleToasts:s=ya,toastOptions:h,dir:y=ut(),gap:x=xa,loadingIcon:w,icons:M,containerAriaLabel:_="Notifications",pauseWhenPageIsHidden:T,cn:q=Ma}=e,[O,A]=m.useState([]),re=m.useMemo(()=>Array.from(new Set([a].concat(O.filter(N=>N.position).map(N=>N.position)))),[O,a]),[oe,ce]=m.useState([]),[fe,H]=m.useState(!1),[ie,ee]=m.useState(!1),[ve,Q]=m.useState(u!=="system"?u:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=m.useRef(null),g=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=m.useRef(null),I=m.useRef(!1),te=m.useCallback(N=>{var S;(S=O.find(E=>E.id===N.id))!=null&&S.delete||B.dismiss(N.id),A(E=>E.filter(({id:V})=>V!==N.id))},[O]);return m.useEffect(()=>B.subscribe(N=>{if(N.dismiss){A(S=>S.map(E=>E.id===N.id?{...E,delete:!0}:E));return}setTimeout(()=>{At.flushSync(()=>{A(S=>{let E=S.findIndex(V=>V.id===N.id);return E!==-1?[...S.slice(0,E),{...S[E],...N},...S.slice(E+1)]:[N,...S]})})})}),[]),m.useEffect(()=>{if(u!=="system"){Q(u);return}u==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?Q("dark"):Q("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:N})=>{Q(N?"dark":"light")})},[u]),m.useEffect(()=>{O.length<=1&&H(!1)},[O]),m.useEffect(()=>{let N=S=>{var E,V;n.every(ae=>S[ae]||S.code===ae)&&(H(!0),(E=z.current)==null||E.focus()),S.code==="Escape"&&(document.activeElement===z.current||(V=z.current)!=null&&V.contains(document.activeElement))&&H(!1)};return document.addEventListener("keydown",N),()=>document.removeEventListener("keydown",N)},[n]),m.useEffect(()=>{if(z.current)return()=>{v.current&&(v.current.focus({preventScroll:!0}),v.current=null,I.current=!1)}},[z.current]),O.length?m.createElement("section",{"aria-label":`${_} ${g}`,tabIndex:-1},re.map((N,S)=>{var E;let[V,ae]=N.split("-");return m.createElement("ol",{key:N,dir:y==="auto"?ut():y,tabIndex:-1,ref:z,className:i,"data-sonner-toaster":!0,"data-theme":ve,"data-y-position":V,"data-x-position":ae,style:{"--front-toast-height":`${((E=oe[0])==null?void 0:E.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||ga,"--width":`${wa}px`,"--gap":`${x}px`,...p},onBlur:W=>{I.current&&!W.currentTarget.contains(W.relatedTarget)&&(I.current=!1,v.current&&(v.current.focus({preventScroll:!0}),v.current=null))},onFocus:W=>{W.target instanceof HTMLElement&&W.target.dataset.dismissible==="false"||I.current||(I.current=!0,v.current=W.relatedTarget)},onMouseEnter:()=>H(!0),onMouseMove:()=>H(!0),onMouseLeave:()=>{ie||H(!1)},onPointerDown:W=>{W.target instanceof HTMLElement&&W.target.dataset.dismissible==="false"||ee(!0)},onPointerUp:()=>ee(!1)},O.filter(W=>!W.position&&S===0||W.position===N).map((W,Se)=>{var me,he;return m.createElement(Na,{key:W.id,icons:M,index:Se,toast:W,defaultRichColors:f,duration:(me=h==null?void 0:h.duration)!=null?me:c,className:h==null?void 0:h.className,descriptionClassName:h==null?void 0:h.descriptionClassName,invert:t,visibleToasts:s,closeButton:(he=h==null?void 0:h.closeButton)!=null?he:o,interacting:ie,position:N,style:h==null?void 0:h.style,unstyled:h==null?void 0:h.unstyled,classNames:h==null?void 0:h.classNames,cancelButtonStyle:h==null?void 0:h.cancelButtonStyle,actionButtonStyle:h==null?void 0:h.actionButtonStyle,removeToast:te,toasts:O.filter(pe=>pe.position==W.position),heights:oe.filter(pe=>pe.position==W.position),setHeights:ce,expandByDefault:r,gap:x,loadingIcon:w,expanded:fe,pauseWhenPageIsHidden:T,cn:q})}))})):null};const Da={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},Ca=(e,t,a)=>{let n;const r=Da[e];return typeof r=="string"?n=r:t===1?n=r.one:n=r.other.replace("{{count}}",String(t)),a!=null&&a.addSuffix?a.comparison&&a.comparison>0?n+"内":n+"前":n},Pa={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},Sa={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},ja={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},Ea={date:Oe({formats:Pa,defaultWidth:"full"}),time:Oe({formats:Sa,defaultWidth:"full"}),dateTime:Oe({formats:ja,defaultWidth:"full"})};function ct(e,t,a){const n="eeee p";return zt(e,t,a)?n:e.getTime()>t.getTime()?"'下个'"+n:"'上个'"+n}const Wa={lastWeek:ct,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:ct,other:"PP p"},Oa=(e,t,a,n)=>{const r=Wa[e];return typeof r=="function"?r(t,a,n):r},Ta={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},Ia={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},La={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},Ba={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},Ra={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Fa={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Ya=(e,t)=>{const a=Number(e);switch(t==null?void 0:t.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},Aa={ordinalNumber:Ya,era:ye({values:Ta,defaultWidth:"wide"}),quarter:ye({values:Ia,defaultWidth:"wide",argumentCallback:e=>e-1}),month:ye({values:La,defaultWidth:"wide"}),day:ye({values:Ba,defaultWidth:"wide"}),dayPeriod:ye({values:Ra,defaultWidth:"wide",formattingValues:Fa,defaultFormattingWidth:"wide"})},za=/^(第\s*)?\d+(日|时|分|秒)?/i,$a=/\d+/i,Ha={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},Va={any:[/^(前)/i,/^(公元)/i]},Ka={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},Ua={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},Xa={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},qa={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},Za={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},Ja={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},Qa={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},Ga={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},en={ordinalNumber:$t({matchPattern:za,parsePattern:$a,valueCallback:e=>parseInt(e,10)}),era:ge({matchPatterns:Ha,defaultMatchWidth:"wide",parsePatterns:Va,defaultParseWidth:"any"}),quarter:ge({matchPatterns:Ka,defaultMatchWidth:"wide",parsePatterns:Ua,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ge({matchPatterns:Xa,defaultMatchWidth:"wide",parsePatterns:qa,defaultParseWidth:"any"}),day:ge({matchPatterns:Za,defaultMatchWidth:"wide",parsePatterns:Ja,defaultParseWidth:"any"}),dayPeriod:ge({matchPatterns:Qa,defaultMatchWidth:"any",parsePatterns:Ga,defaultParseWidth:"any"})},Rr={code:"zh-CN",formatDistance:Ca,formatLong:Ea,formatRelative:Oa,localize:Aa,match:en,options:{weekStartsOn:1,firstWeekContainsDate:4}};var b=function(){return b=Object.assign||function(t){for(var a,n=1,r=arguments.length;n<r;n++){a=arguments[n];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(t[o]=a[o])}return t},b.apply(this,arguments)};function tn(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}function wt(e,t,a){for(var n=0,r=t.length,o;n<r;n++)(o||!(n in t))&&(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}function we(e){return e.mode==="multiple"}function xe(e){return e.mode==="range"}function Pe(e){return e.mode==="single"}var an={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"};function nn(e,t){return ne(e,"LLLL y",t)}function rn(e,t){return ne(e,"d",t)}function on(e,t){return ne(e,"LLLL",t)}function sn(e){return"".concat(e)}function ln(e,t){return ne(e,"cccccc",t)}function dn(e,t){return ne(e,"yyyy",t)}var un=Object.freeze({__proto__:null,formatCaption:nn,formatDay:rn,formatMonthCaption:on,formatWeekNumber:sn,formatWeekdayName:ln,formatYearCaption:dn}),cn=function(e,t,a){return ne(e,"do MMMM (EEEE)",a)},fn=function(){return"Month: "},vn=function(){return"Go to next month"},mn=function(){return"Go to previous month"},hn=function(e,t){return ne(e,"cccc",t)},pn=function(e){return"Week n. ".concat(e)},yn=function(){return"Year: "},gn=Object.freeze({__proto__:null,labelDay:cn,labelMonthDropdown:fn,labelNext:vn,labelPrevious:mn,labelWeekNumber:pn,labelWeekday:hn,labelYearDropdown:yn});function bn(){var e="buttons",t=an,a=Kt,n={},r={},o=1,i={},l=new Date;return{captionLayout:e,classNames:t,formatters:un,labels:gn,locale:a,modifiersClassNames:n,modifiers:r,numberOfMonths:o,styles:i,today:l,mode:"default"}}function wn(e){var t=e.fromYear,a=e.toYear,n=e.fromMonth,r=e.toMonth,o=e.fromDate,i=e.toDate;return n?o=F(n):t&&(o=new Date(t,0,1)),r?i=$e(r):a&&(i=new Date(a,11,31)),{fromDate:o?st(o):void 0,toDate:i?st(i):void 0}}var xt=D.createContext(void 0);function xn(e){var t,a=e.initialProps,n=bn(),r=wn(a),o=r.fromDate,i=r.toDate,l=(t=a.captionLayout)!==null&&t!==void 0?t:n.captionLayout;l!=="buttons"&&(!o||!i)&&(l="buttons");var u;(Pe(a)||we(a)||xe(a))&&(u=a.onSelect);var f=b(b(b({},n),a),{captionLayout:l,classNames:b(b({},n.classNames),a.classNames),components:b({},a.components),formatters:b(b({},n.formatters),a.formatters),fromDate:o,labels:b(b({},n.labels),a.labels),mode:a.mode||n.mode,modifiers:b(b({},n.modifiers),a.modifiers),modifiersClassNames:b(b({},n.modifiersClassNames),a.modifiersClassNames),onSelect:u,styles:b(b({},n.styles),a.styles),toDate:i});return d.jsx(xt.Provider,{value:f,children:e.children})}function P(){var e=D.useContext(xt);if(!e)throw new Error("useDayPicker must be used within a DayPickerProvider.");return e}function kt(e){var t=P(),a=t.locale,n=t.classNames,r=t.styles,o=t.formatters.formatCaption;return d.jsx("div",{className:n.caption_label,style:r.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:o(e.displayMonth,{locale:a})})}function kn(e){return d.jsx("svg",b({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:d.jsx("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function _t(e){var t,a,n=e.onChange,r=e.value,o=e.children,i=e.caption,l=e.className,u=e.style,f=P(),c=(a=(t=f.components)===null||t===void 0?void 0:t.IconDropdown)!==null&&a!==void 0?a:kn;return d.jsxs("div",{className:l,style:u,children:[d.jsx("span",{className:f.classNames.vhidden,children:e["aria-label"]}),d.jsx("select",{name:e.name,"aria-label":e["aria-label"],className:f.classNames.dropdown,style:f.styles.dropdown,value:r,onChange:n,children:o}),d.jsxs("div",{className:f.classNames.caption_label,style:f.styles.caption_label,"aria-hidden":"true",children:[i,d.jsx(c,{className:f.classNames.dropdown_icon,style:f.styles.dropdown_icon})]})]})}function _n(e){var t,a=P(),n=a.fromDate,r=a.toDate,o=a.styles,i=a.locale,l=a.formatters.formatMonthCaption,u=a.classNames,f=a.components,c=a.labels.labelMonthDropdown;if(!n)return d.jsx(d.Fragment,{});if(!r)return d.jsx(d.Fragment,{});var p=[];if(Ut(n,r))for(var s=F(n),h=n.getMonth();h<=r.getMonth();h++)p.push(Ie(s,h));else for(var s=F(new Date),h=0;h<=11;h++)p.push(Ie(s,h));var y=function(w){var M=Number(w.target.value),_=Ie(F(e.displayMonth),M);e.onChange(_)},x=(t=f==null?void 0:f.Dropdown)!==null&&t!==void 0?t:_t;return d.jsx(x,{name:"months","aria-label":c(),className:u.dropdown_month,style:o.dropdown_month,onChange:y,value:e.displayMonth.getMonth(),caption:l(e.displayMonth,{locale:i}),children:p.map(function(w){return d.jsx("option",{value:w.getMonth(),children:l(w,{locale:i})},w.getMonth())})})}function Mn(e){var t,a=e.displayMonth,n=P(),r=n.fromDate,o=n.toDate,i=n.locale,l=n.styles,u=n.classNames,f=n.components,c=n.formatters.formatYearCaption,p=n.labels.labelYearDropdown,s=[];if(!r)return d.jsx(d.Fragment,{});if(!o)return d.jsx(d.Fragment,{});for(var h=r.getFullYear(),y=o.getFullYear(),x=h;x<=y;x++)s.push(lt(Xt(new Date),x));var w=function(_){var T=lt(F(a),Number(_.target.value));e.onChange(T)},M=(t=f==null?void 0:f.Dropdown)!==null&&t!==void 0?t:_t;return d.jsx(M,{name:"years","aria-label":p(),className:u.dropdown_year,style:l.dropdown_year,onChange:w,value:a.getFullYear(),caption:c(a,{locale:i}),children:s.map(function(_){return d.jsx("option",{value:_.getFullYear(),children:c(_,{locale:i})},_.getFullYear())})})}function Nn(e,t){var a=D.useState(e),n=a[0],r=a[1],o=t===void 0?n:t;return[o,r]}function Dn(e){var t=e.month,a=e.defaultMonth,n=e.today,r=t||a||n||new Date,o=e.toDate,i=e.fromDate,l=e.numberOfMonths,u=l===void 0?1:l;if(o&&be(o,r)<0){var f=-1*(u-1);r=X(o,f)}return i&&be(r,i)<0&&(r=i),F(r)}function Cn(){var e=P(),t=Dn(e),a=Nn(t,e.month),n=a[0],r=a[1],o=function(i){var l;if(!e.disableNavigation){var u=F(i);r(u),(l=e.onMonthChange)===null||l===void 0||l.call(e,u)}};return[n,o]}function Pn(e,t){for(var a=t.reverseMonths,n=t.numberOfMonths,r=F(e),o=F(X(r,n)),i=be(o,r),l=[],u=0;u<i;u++){var f=X(r,u);l.push(f)}return a&&(l=l.reverse()),l}function Sn(e,t){if(!t.disableNavigation){var a=t.toDate,n=t.pagedNavigation,r=t.numberOfMonths,o=r===void 0?1:r,i=n?o:1,l=F(e);if(!a)return X(l,i);var u=be(a,e);if(!(u<o))return X(l,i)}}function jn(e,t){if(!t.disableNavigation){var a=t.fromDate,n=t.pagedNavigation,r=t.numberOfMonths,o=r===void 0?1:r,i=n?o:1,l=F(e);if(!a)return X(l,-i);var u=be(l,a);if(!(u<=0))return X(l,-i)}}var Mt=D.createContext(void 0);function En(e){var t=P(),a=Cn(),n=a[0],r=a[1],o=Pn(n,t),i=Sn(n,t),l=jn(n,t),u=function(p){return o.some(function(s){return He(p,s)})},f=function(p,s){u(p)||(s&&pt(p,s)?r(X(p,1+t.numberOfMonths*-1)):r(p))},c={currentMonth:n,displayMonths:o,goToMonth:r,goToDate:f,previousMonth:l,nextMonth:i,isDateDisplayed:u};return d.jsx(Mt.Provider,{value:c,children:e.children})}function ke(){var e=D.useContext(Mt);if(!e)throw new Error("useNavigation must be used within a NavigationProvider");return e}function ft(e){var t,a=P(),n=a.classNames,r=a.styles,o=a.components,i=ke().goToMonth,l=function(c){i(X(c,e.displayIndex?-e.displayIndex:0))},u=(t=o==null?void 0:o.CaptionLabel)!==null&&t!==void 0?t:kt,f=d.jsx(u,{id:e.id,displayMonth:e.displayMonth});return d.jsxs("div",{className:n.caption_dropdowns,style:r.caption_dropdowns,children:[d.jsx("div",{className:n.vhidden,children:f}),d.jsx(_n,{onChange:l,displayMonth:e.displayMonth}),d.jsx(Mn,{onChange:l,displayMonth:e.displayMonth})]})}function Wn(e){return d.jsx("svg",b({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:d.jsx("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function On(e){return d.jsx("svg",b({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:d.jsx("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var Ce=D.forwardRef(function(e,t){var a=P(),n=a.classNames,r=a.styles,o=[n.button_reset,n.button];e.className&&o.push(e.className);var i=o.join(" "),l=b(b({},r.button_reset),r.button);return e.style&&Object.assign(l,e.style),d.jsx("button",b({},e,{ref:t,type:"button",className:i,style:l}))});function Tn(e){var t,a,n=P(),r=n.dir,o=n.locale,i=n.classNames,l=n.styles,u=n.labels,f=u.labelPrevious,c=u.labelNext,p=n.components;if(!e.nextMonth&&!e.previousMonth)return d.jsx(d.Fragment,{});var s=f(e.previousMonth,{locale:o}),h=[i.nav_button,i.nav_button_previous].join(" "),y=c(e.nextMonth,{locale:o}),x=[i.nav_button,i.nav_button_next].join(" "),w=(t=p==null?void 0:p.IconRight)!==null&&t!==void 0?t:On,M=(a=p==null?void 0:p.IconLeft)!==null&&a!==void 0?a:Wn;return d.jsxs("div",{className:i.nav,style:l.nav,children:[!e.hidePrevious&&d.jsx(Ce,{name:"previous-month","aria-label":s,className:h,style:l.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:r==="rtl"?d.jsx(w,{className:i.nav_icon,style:l.nav_icon}):d.jsx(M,{className:i.nav_icon,style:l.nav_icon})}),!e.hideNext&&d.jsx(Ce,{name:"next-month","aria-label":y,className:x,style:l.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:r==="rtl"?d.jsx(M,{className:i.nav_icon,style:l.nav_icon}):d.jsx(w,{className:i.nav_icon,style:l.nav_icon})})]})}function vt(e){var t=P().numberOfMonths,a=ke(),n=a.previousMonth,r=a.nextMonth,o=a.goToMonth,i=a.displayMonths,l=i.findIndex(function(y){return He(e.displayMonth,y)}),u=l===0,f=l===i.length-1,c=t>1&&(u||!f),p=t>1&&(f||!u),s=function(){n&&o(n)},h=function(){r&&o(r)};return d.jsx(Tn,{displayMonth:e.displayMonth,hideNext:c,hidePrevious:p,nextMonth:r,previousMonth:n,onPreviousClick:s,onNextClick:h})}function In(e){var t,a=P(),n=a.classNames,r=a.disableNavigation,o=a.styles,i=a.captionLayout,l=a.components,u=(t=l==null?void 0:l.CaptionLabel)!==null&&t!==void 0?t:kt,f;return r?f=d.jsx(u,{id:e.id,displayMonth:e.displayMonth}):i==="dropdown"?f=d.jsx(ft,{displayMonth:e.displayMonth,id:e.id}):i==="dropdown-buttons"?f=d.jsxs(d.Fragment,{children:[d.jsx(ft,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),d.jsx(vt,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):f=d.jsxs(d.Fragment,{children:[d.jsx(u,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),d.jsx(vt,{displayMonth:e.displayMonth,id:e.id})]}),d.jsx("div",{className:n.caption,style:o.caption,children:f})}function Ln(e){var t=P(),a=t.footer,n=t.styles,r=t.classNames.tfoot;return a?d.jsx("tfoot",{className:r,style:n.tfoot,children:d.jsx("tr",{children:d.jsx("td",{colSpan:8,children:a})})}):d.jsx(d.Fragment,{})}function Bn(e,t,a){for(var n=a?Ve(new Date):Ke(new Date,{locale:e,weekStartsOn:t}),r=[],o=0;o<7;o++){var i=R(n,o);r.push(i)}return r}function Rn(){var e=P(),t=e.classNames,a=e.styles,n=e.showWeekNumber,r=e.locale,o=e.weekStartsOn,i=e.ISOWeek,l=e.formatters.formatWeekdayName,u=e.labels.labelWeekday,f=Bn(r,o,i);return d.jsxs("tr",{style:a.head_row,className:t.head_row,children:[n&&d.jsx("td",{style:a.head_cell,className:t.head_cell}),f.map(function(c,p){return d.jsx("th",{scope:"col",className:t.head_cell,style:a.head_cell,"aria-label":u(c,{locale:r}),children:l(c,{locale:r})},p)})]})}function Fn(){var e,t=P(),a=t.classNames,n=t.styles,r=t.components,o=(e=r==null?void 0:r.HeadRow)!==null&&e!==void 0?e:Rn;return d.jsx("thead",{style:n.head,className:a.head,children:d.jsx(o,{})})}function Yn(e){var t=P(),a=t.locale,n=t.formatters.formatDay;return d.jsx(d.Fragment,{children:n(e.date,{locale:a})})}var Ue=D.createContext(void 0);function An(e){if(!we(e.initialProps)){var t={selected:void 0,modifiers:{disabled:[]}};return d.jsx(Ue.Provider,{value:t,children:e.children})}return d.jsx(zn,{initialProps:e.initialProps,children:e.children})}function zn(e){var t=e.initialProps,a=e.children,n=t.selected,r=t.min,o=t.max,i=function(f,c,p){var s,h;(s=t.onDayClick)===null||s===void 0||s.call(t,f,c,p);var y=!!(c.selected&&r&&(n==null?void 0:n.length)===r);if(!y){var x=!!(!c.selected&&o&&(n==null?void 0:n.length)===o);if(!x){var w=n?wt([],n):[];if(c.selected){var M=w.findIndex(function(_){return L(f,_)});w.splice(M,1)}else w.push(f);(h=t.onSelect)===null||h===void 0||h.call(t,w,f,c,p)}}},l={disabled:[]};n&&l.disabled.push(function(f){var c=o&&n.length>o-1,p=n.some(function(s){return L(s,f)});return!!(c&&!p)});var u={selected:n,onDayClick:i,modifiers:l};return d.jsx(Ue.Provider,{value:u,children:a})}function Xe(){var e=D.useContext(Ue);if(!e)throw new Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}function $n(e,t){var a=t||{},n=a.from,r=a.to;return n&&r?L(r,e)&&L(n,e)?void 0:L(r,e)?{from:r,to:void 0}:L(n,e)?void 0:Ye(n,e)?{from:e,to:r}:{from:n,to:e}:r?Ye(e,r)?{from:r,to:e}:{from:e,to:r}:n?pt(e,n)?{from:e,to:n}:{from:n,to:e}:{from:e,to:void 0}}var qe=D.createContext(void 0);function Hn(e){if(!xe(e.initialProps)){var t={selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}};return d.jsx(qe.Provider,{value:t,children:e.children})}return d.jsx(Vn,{initialProps:e.initialProps,children:e.children})}function Vn(e){var t=e.initialProps,a=e.children,n=t.selected,r=n||{},o=r.from,i=r.to,l=t.min,u=t.max,f=function(h,y,x){var w,M;(w=t.onDayClick)===null||w===void 0||w.call(t,h,y,x);var _=$n(h,n);(M=t.onSelect)===null||M===void 0||M.call(t,_,h,y,x)},c={range_start:[],range_end:[],range_middle:[],disabled:[]};if(o?(c.range_start=[o],i?(c.range_end=[i],L(o,i)||(c.range_middle=[{after:o,before:i}])):c.range_end=[o]):i&&(c.range_start=[i],c.range_end=[i]),l&&(o&&!i&&c.disabled.push({after:Te(o,l-1),before:R(o,l-1)}),o&&i&&c.disabled.push({after:o,before:R(o,l-1)}),!o&&i&&c.disabled.push({after:Te(i,l-1),before:R(i,l-1)})),u){if(o&&!i&&(c.disabled.push({before:R(o,-u+1)}),c.disabled.push({after:R(o,u-1)})),o&&i){var p=J(i,o)+1,s=u-p;c.disabled.push({before:Te(o,s)}),c.disabled.push({after:R(i,s)})}!o&&i&&(c.disabled.push({before:R(i,-u+1)}),c.disabled.push({after:R(i,u-1)}))}return d.jsx(qe.Provider,{value:{selected:n,onDayClick:f,modifiers:c},children:a})}function Ze(){var e=D.useContext(qe);if(!e)throw new Error("useSelectRange must be used within a SelectRangeProvider");return e}function De(e){return Array.isArray(e)?wt([],e):e!==void 0?[e]:[]}function Kn(e){var t={};return Object.entries(e).forEach(function(a){var n=a[0],r=a[1];t[n]=De(r)}),t}var $;(function(e){e.Outside="outside",e.Disabled="disabled",e.Selected="selected",e.Hidden="hidden",e.Today="today",e.RangeStart="range_start",e.RangeEnd="range_end",e.RangeMiddle="range_middle"})($||($={}));var Un=$.Selected,Z=$.Disabled,Xn=$.Hidden,qn=$.Today,Le=$.RangeEnd,Be=$.RangeMiddle,Re=$.RangeStart,Zn=$.Outside;function Jn(e,t,a){var n,r=(n={},n[Un]=De(e.selected),n[Z]=De(e.disabled),n[Xn]=De(e.hidden),n[qn]=[e.today],n[Le]=[],n[Be]=[],n[Re]=[],n[Zn]=[],n);return e.fromDate&&r[Z].push({before:e.fromDate}),e.toDate&&r[Z].push({after:e.toDate}),we(e)?r[Z]=r[Z].concat(t.modifiers[Z]):xe(e)&&(r[Z]=r[Z].concat(a.modifiers[Z]),r[Re]=a.modifiers[Re],r[Be]=a.modifiers[Be],r[Le]=a.modifiers[Le]),r}var Nt=D.createContext(void 0);function Qn(e){var t=P(),a=Xe(),n=Ze(),r=Jn(t,a,n),o=Kn(t.modifiers),i=b(b({},r),o);return d.jsx(Nt.Provider,{value:i,children:e.children})}function Dt(){var e=D.useContext(Nt);if(!e)throw new Error("useModifiers must be used within a ModifiersProvider");return e}function Gn(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function er(e){return!!(e&&typeof e=="object"&&"from"in e)}function tr(e){return!!(e&&typeof e=="object"&&"after"in e)}function ar(e){return!!(e&&typeof e=="object"&&"before"in e)}function nr(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function rr(e,t){var a,n=t.from,r=t.to;if(n&&r){var o=J(r,n)<0;o&&(a=[r,n],n=a[0],r=a[1]);var i=J(e,n)>=0&&J(r,e)>=0;return i}return r?L(r,e):n?L(n,e):!1}function or(e){return bt(e)}function ir(e){return Array.isArray(e)&&e.every(bt)}function sr(e,t){return t.some(function(a){if(typeof a=="boolean")return a;if(or(a))return L(e,a);if(ir(a))return a.includes(e);if(er(a))return rr(e,a);if(nr(a))return a.dayOfWeek.includes(e.getDay());if(Gn(a)){var n=J(a.before,e),r=J(a.after,e),o=n>0,i=r<0,l=Ye(a.before,a.after);return l?i&&o:o||i}return tr(a)?J(e,a.after)>0:ar(a)?J(a.before,e)>0:typeof a=="function"?a(e):!1})}function Je(e,t,a){var n=Object.keys(t).reduce(function(o,i){var l=t[i];return sr(e,l)&&o.push(i),o},[]),r={};return n.forEach(function(o){return r[o]=!0}),a&&!He(e,a)&&(r.outside=!0),r}function lr(e,t){for(var a=F(e[0]),n=$e(e[e.length-1]),r,o,i=a;i<=n;){var l=Je(i,t),u=!l.disabled&&!l.hidden;if(!u){i=R(i,1);continue}if(l.selected)return i;l.today&&!o&&(o=i),r||(r=i),i=R(i,1)}return o||r}var dr=365;function Ct(e,t){var a=t.moveBy,n=t.direction,r=t.context,o=t.modifiers,i=t.retry,l=i===void 0?{count:0,lastFocused:e}:i,u=r.weekStartsOn,f=r.fromDate,c=r.toDate,p=r.locale,s={day:R,week:Ae,month:X,year:Gt,startOfWeek:function(w){return r.ISOWeek?Ve(w):Ke(w,{locale:p,weekStartsOn:u})},endOfWeek:function(w){return r.ISOWeek?yt(w):gt(w,{locale:p,weekStartsOn:u})}},h=s[a](e,n==="after"?1:-1);n==="before"&&f?h=qt([f,h]):n==="after"&&c&&(h=Zt([c,h]));var y=!0;if(o){var x=Je(h,o);y=!x.disabled&&!x.hidden}return y?h:l.count>dr?l.lastFocused:Ct(h,{moveBy:a,direction:n,context:r,modifiers:o,retry:b(b({},l),{count:l.count+1})})}var Pt=D.createContext(void 0);function ur(e){var t=ke(),a=Dt(),n=D.useState(),r=n[0],o=n[1],i=D.useState(),l=i[0],u=i[1],f=lr(t.displayMonths,a),c=r??(l&&t.isDateDisplayed(l))?l:f,p=function(){u(r),o(void 0)},s=function(w){o(w)},h=P(),y=function(w,M){if(r){var _=Ct(r,{moveBy:w,direction:M,context:h,modifiers:a});L(r,_)||(t.goToDate(_,r),s(_))}},x={focusedDay:r,focusTarget:c,blur:p,focus:s,focusDayAfter:function(){return y("day","after")},focusDayBefore:function(){return y("day","before")},focusWeekAfter:function(){return y("week","after")},focusWeekBefore:function(){return y("week","before")},focusMonthBefore:function(){return y("month","before")},focusMonthAfter:function(){return y("month","after")},focusYearBefore:function(){return y("year","before")},focusYearAfter:function(){return y("year","after")},focusStartOfWeek:function(){return y("startOfWeek","before")},focusEndOfWeek:function(){return y("endOfWeek","after")}};return d.jsx(Pt.Provider,{value:x,children:e.children})}function Qe(){var e=D.useContext(Pt);if(!e)throw new Error("useFocusContext must be used within a FocusProvider");return e}function cr(e,t){var a=Dt(),n=Je(e,a,t);return n}var Ge=D.createContext(void 0);function fr(e){if(!Pe(e.initialProps)){var t={selected:void 0};return d.jsx(Ge.Provider,{value:t,children:e.children})}return d.jsx(vr,{initialProps:e.initialProps,children:e.children})}function vr(e){var t=e.initialProps,a=e.children,n=function(o,i,l){var u,f,c;if((u=t.onDayClick)===null||u===void 0||u.call(t,o,i,l),i.selected&&!t.required){(f=t.onSelect)===null||f===void 0||f.call(t,void 0,o,i,l);return}(c=t.onSelect)===null||c===void 0||c.call(t,o,o,i,l)},r={selected:t.selected,onDayClick:n};return d.jsx(Ge.Provider,{value:r,children:a})}function St(){var e=D.useContext(Ge);if(!e)throw new Error("useSelectSingle must be used within a SelectSingleProvider");return e}function mr(e,t){var a=P(),n=St(),r=Xe(),o=Ze(),i=Qe(),l=i.focusDayAfter,u=i.focusDayBefore,f=i.focusWeekAfter,c=i.focusWeekBefore,p=i.blur,s=i.focus,h=i.focusMonthBefore,y=i.focusMonthAfter,x=i.focusYearBefore,w=i.focusYearAfter,M=i.focusStartOfWeek,_=i.focusEndOfWeek,T=function(g){var v,I,te,N;Pe(a)?(v=n.onDayClick)===null||v===void 0||v.call(n,e,t,g):we(a)?(I=r.onDayClick)===null||I===void 0||I.call(r,e,t,g):xe(a)?(te=o.onDayClick)===null||te===void 0||te.call(o,e,t,g):(N=a.onDayClick)===null||N===void 0||N.call(a,e,t,g)},q=function(g){var v;s(e),(v=a.onDayFocus)===null||v===void 0||v.call(a,e,t,g)},O=function(g){var v;p(),(v=a.onDayBlur)===null||v===void 0||v.call(a,e,t,g)},A=function(g){var v;(v=a.onDayMouseEnter)===null||v===void 0||v.call(a,e,t,g)},re=function(g){var v;(v=a.onDayMouseLeave)===null||v===void 0||v.call(a,e,t,g)},oe=function(g){var v;(v=a.onDayPointerEnter)===null||v===void 0||v.call(a,e,t,g)},ce=function(g){var v;(v=a.onDayPointerLeave)===null||v===void 0||v.call(a,e,t,g)},fe=function(g){var v;(v=a.onDayTouchCancel)===null||v===void 0||v.call(a,e,t,g)},H=function(g){var v;(v=a.onDayTouchEnd)===null||v===void 0||v.call(a,e,t,g)},ie=function(g){var v;(v=a.onDayTouchMove)===null||v===void 0||v.call(a,e,t,g)},ee=function(g){var v;(v=a.onDayTouchStart)===null||v===void 0||v.call(a,e,t,g)},ve=function(g){var v;(v=a.onDayKeyUp)===null||v===void 0||v.call(a,e,t,g)},Q=function(g){var v;switch(g.key){case"ArrowLeft":g.preventDefault(),g.stopPropagation(),a.dir==="rtl"?l():u();break;case"ArrowRight":g.preventDefault(),g.stopPropagation(),a.dir==="rtl"?u():l();break;case"ArrowDown":g.preventDefault(),g.stopPropagation(),f();break;case"ArrowUp":g.preventDefault(),g.stopPropagation(),c();break;case"PageUp":g.preventDefault(),g.stopPropagation(),g.shiftKey?x():h();break;case"PageDown":g.preventDefault(),g.stopPropagation(),g.shiftKey?w():y();break;case"Home":g.preventDefault(),g.stopPropagation(),M();break;case"End":g.preventDefault(),g.stopPropagation(),_();break}(v=a.onDayKeyDown)===null||v===void 0||v.call(a,e,t,g)},z={onClick:T,onFocus:q,onBlur:O,onKeyDown:Q,onKeyUp:ve,onMouseEnter:A,onMouseLeave:re,onPointerEnter:oe,onPointerLeave:ce,onTouchCancel:fe,onTouchEnd:H,onTouchMove:ie,onTouchStart:ee};return z}function hr(){var e=P(),t=St(),a=Xe(),n=Ze(),r=Pe(e)?t.selected:we(e)?a.selected:xe(e)?n.selected:void 0;return r}function pr(e){return Object.values($).includes(e)}function yr(e,t){var a=[e.classNames.day];return Object.keys(t).forEach(function(n){var r=e.modifiersClassNames[n];if(r)a.push(r);else if(pr(n)){var o=e.classNames["day_".concat(n)];o&&a.push(o)}}),a}function gr(e,t){var a=b({},e.styles.day);return Object.keys(t).forEach(function(n){var r;a=b(b({},a),(r=e.modifiersStyles)===null||r===void 0?void 0:r[n])}),a}function br(e,t,a){var n,r,o,i=P(),l=Qe(),u=cr(e,t),f=mr(e,u),c=hr(),p=!!(i.onDayClick||i.mode!=="default");D.useEffect(function(){var A;u.outside||l.focusedDay&&p&&L(l.focusedDay,e)&&((A=a.current)===null||A===void 0||A.focus())},[l.focusedDay,e,a,p,u.outside]);var s=yr(i,u).join(" "),h=gr(i,u),y=!!(u.outside&&!i.showOutsideDays||u.hidden),x=(o=(r=i.components)===null||r===void 0?void 0:r.DayContent)!==null&&o!==void 0?o:Yn,w=d.jsx(x,{date:e,displayMonth:t,activeModifiers:u}),M={style:h,className:s,children:w,role:"gridcell"},_=l.focusTarget&&L(l.focusTarget,e)&&!u.outside,T=l.focusedDay&&L(l.focusedDay,e),q=b(b(b({},M),(n={disabled:u.disabled,role:"gridcell"},n["aria-selected"]=u.selected,n.tabIndex=T||_?0:-1,n)),f),O={isButton:p,isHidden:y,activeModifiers:u,selectedDays:c,buttonProps:q,divProps:M};return O}function wr(e){var t=D.useRef(null),a=br(e.date,e.displayMonth,t);return a.isHidden?d.jsx("div",{role:"gridcell"}):a.isButton?d.jsx(Ce,b({name:"day",ref:t},a.buttonProps)):d.jsx("div",b({},a.divProps))}function xr(e){var t=e.number,a=e.dates,n=P(),r=n.onWeekNumberClick,o=n.styles,i=n.classNames,l=n.locale,u=n.labels.labelWeekNumber,f=n.formatters.formatWeekNumber,c=f(Number(t),{locale:l});if(!r)return d.jsx("span",{className:i.weeknumber,style:o.weeknumber,children:c});var p=u(Number(t),{locale:l}),s=function(h){r(t,a,h)};return d.jsx(Ce,{name:"week-number","aria-label":p,className:i.weeknumber,style:o.weeknumber,onClick:s,children:c})}function kr(e){var t,a,n=P(),r=n.styles,o=n.classNames,i=n.showWeekNumber,l=n.components,u=(t=l==null?void 0:l.Day)!==null&&t!==void 0?t:wr,f=(a=l==null?void 0:l.WeekNumber)!==null&&a!==void 0?a:xr,c;return i&&(c=d.jsx("td",{className:o.cell,style:r.cell,children:d.jsx(f,{number:e.weekNumber,dates:e.dates})})),d.jsxs("tr",{className:o.row,style:r.row,children:[c,e.dates.map(function(p){return d.jsx("td",{className:o.cell,style:r.cell,role:"presentation",children:d.jsx(u,{displayMonth:e.displayMonth,date:p})},Ht(p))})]})}function mt(e,t,a){for(var n=a!=null&&a.ISOWeek?yt(t):gt(t,a),r=a!=null&&a.ISOWeek?Ve(e):Ke(e,a),o=J(n,r),i=[],l=0;l<=o;l++)i.push(R(r,l));var u=i.reduce(function(f,c){var p=a!=null&&a.ISOWeek?Jt(c):Qt(c,a),s=f.find(function(h){return h.weekNumber===p});return s?(s.dates.push(c),f):(f.push({weekNumber:p,dates:[c]}),f)},[]);return u}function _r(e,t){var a=mt(F(e),$e(e),t);if(t!=null&&t.useFixedWeeks){var n=Vt(e,t);if(n<6){var r=a[a.length-1],o=r.dates[r.dates.length-1],i=Ae(o,6-n),l=mt(Ae(o,1),i,t);a.push.apply(a,l)}}return a}function Mr(e){var t,a,n,r=P(),o=r.locale,i=r.classNames,l=r.styles,u=r.hideHead,f=r.fixedWeeks,c=r.components,p=r.weekStartsOn,s=r.firstWeekContainsDate,h=r.ISOWeek,y=_r(e.displayMonth,{useFixedWeeks:!!f,ISOWeek:h,locale:o,weekStartsOn:p,firstWeekContainsDate:s}),x=(t=c==null?void 0:c.Head)!==null&&t!==void 0?t:Fn,w=(a=c==null?void 0:c.Row)!==null&&a!==void 0?a:kr,M=(n=c==null?void 0:c.Footer)!==null&&n!==void 0?n:Ln;return d.jsxs("table",{id:e.id,className:i.table,style:l.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!u&&d.jsx(x,{}),d.jsx("tbody",{className:i.tbody,style:l.tbody,children:y.map(function(_){return d.jsx(w,{displayMonth:e.displayMonth,dates:_.dates,weekNumber:_.weekNumber},_.weekNumber)})}),d.jsx(M,{displayMonth:e.displayMonth})]})}function Nr(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var Dr=Nr()?D.useLayoutEffect:D.useEffect,Fe=!1,Cr=0;function ht(){return"react-day-picker-".concat(++Cr)}function Pr(e){var t,a=e??(Fe?ht():null),n=D.useState(a),r=n[0],o=n[1];return Dr(function(){r===null&&o(ht())},[]),D.useEffect(function(){Fe===!1&&(Fe=!0)},[]),(t=e??r)!==null&&t!==void 0?t:void 0}function Sr(e){var t,a,n=P(),r=n.dir,o=n.classNames,i=n.styles,l=n.components,u=ke().displayMonths,f=Pr(n.id?"".concat(n.id,"-").concat(e.displayIndex):void 0),c=n.id?"".concat(n.id,"-grid-").concat(e.displayIndex):void 0,p=[o.month],s=i.month,h=e.displayIndex===0,y=e.displayIndex===u.length-1,x=!h&&!y;r==="rtl"&&(t=[h,y],y=t[0],h=t[1]),h&&(p.push(o.caption_start),s=b(b({},s),i.caption_start)),y&&(p.push(o.caption_end),s=b(b({},s),i.caption_end)),x&&(p.push(o.caption_between),s=b(b({},s),i.caption_between));var w=(a=l==null?void 0:l.Caption)!==null&&a!==void 0?a:In;return d.jsxs("div",{className:p.join(" "),style:s,children:[d.jsx(w,{id:f,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),d.jsx(Mr,{id:c,"aria-labelledby":f,displayMonth:e.displayMonth})]},e.displayIndex)}function jr(e){var t=P(),a=t.classNames,n=t.styles;return d.jsx("div",{className:a.months,style:n.months,children:e.children})}function Er(e){var t,a,n=e.initialProps,r=P(),o=Qe(),i=ke(),l=D.useState(!1),u=l[0],f=l[1];D.useEffect(function(){r.initialFocus&&o.focusTarget&&(u||(o.focus(o.focusTarget),f(!0)))},[r.initialFocus,u,o.focus,o.focusTarget,o]);var c=[r.classNames.root,r.className];r.numberOfMonths>1&&c.push(r.classNames.multiple_months),r.showWeekNumber&&c.push(r.classNames.with_weeknumber);var p=b(b({},r.styles.root),r.style),s=Object.keys(n).filter(function(y){return y.startsWith("data-")}).reduce(function(y,x){var w;return b(b({},y),(w={},w[x]=n[x],w))},{}),h=(a=(t=n.components)===null||t===void 0?void 0:t.Months)!==null&&a!==void 0?a:jr;return d.jsx("div",b({className:c.join(" "),style:p,dir:r.dir,id:r.id,nonce:n.nonce,title:n.title,lang:n.lang},s,{children:d.jsx(h,{children:i.displayMonths.map(function(y,x){return d.jsx(Sr,{displayIndex:x,displayMonth:y},x)})})}))}function Wr(e){var t=e.children,a=tn(e,["children"]);return d.jsx(xn,{initialProps:a,children:d.jsx(En,{children:d.jsx(fr,{initialProps:a,children:d.jsx(An,{initialProps:a,children:d.jsx(Hn,{initialProps:a,children:d.jsx(Qn,{children:d.jsx(ur,{children:t})})})})})})})}function Fr(e){return d.jsx(Wr,b({},e,{children:d.jsx(Er,{initialProps:e})}))}export{Fr as D,Br as T,Lr as j,Rr as z};

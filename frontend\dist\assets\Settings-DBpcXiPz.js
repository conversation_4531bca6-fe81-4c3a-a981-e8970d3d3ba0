import{j as e,c as l,u as F,L as z,B as S,C as u,d as f,e as N,f as p,a as g,I as x,g as P}from"./index-D3FXQGgv.js";import{r}from"./react-vendor-DucYQ77b.js";import{p as E,q as D,r as M,s as y,t as q,u as X,v as w,w as Z}from"./radix-ui-DdAvwPj2.js";import{M as J,S as K,a6 as O,a7 as Q,Z as U,a8 as W,a9 as Y,V,aa as _,ab as ee,ac as se}from"./utils-CUkY6B8V.js";import{B as d}from"./badge-BHFrTODj.js";import{S as te,a as ne,b as ie,c as re,d as a}from"./select-CsIt0xHD.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const ce=J("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=r.forwardRef(({className:n,...t},i)=>e.jsxDEV(E,{ref:i,className:l(ce(),n),...t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/label.tsx",lineNumber:16,columnNumber:3},void 0));m.displayName=E.displayName;const b=r.forwardRef(({className:n,...t},i)=>e.jsxDEV(D,{className:l("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",n),...t,ref:i,children:e.jsxDEV(M,{className:l("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/switch.tsx",lineNumber:18,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/switch.tsx",lineNumber:10,columnNumber:3},void 0));b.displayName=D.displayName;const k=r.forwardRef(({className:n,children:t,...i},c)=>e.jsxDEV(y,{ref:c,className:l("relative overflow-hidden",n),...i,children:[e.jsxDEV(q,{className:"h-full w-full rounded-[inherit]",children:t},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:15,columnNumber:5},void 0),e.jsxDEV(R,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:18,columnNumber:5},void 0),e.jsxDEV(X,{},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:19,columnNumber:5},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:10,columnNumber:3},void 0));k.displayName=y.displayName;const R=r.forwardRef(({className:n,orientation:t="vertical",...i},c)=>e.jsxDEV(w,{ref:c,orientation:t,className:l("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",n),...i,children:e.jsxDEV(Z,{className:"relative flex-1 rounded-full bg-border"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:41,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/components/ui/scroll-area.tsx",lineNumber:28,columnNumber:3},void 0));R.displayName=w.displayName;const pe=()=>{const{toast:n}=F(),[t,i]=r.useState({scanInterval:"5",enableAutoScan:!0,githubProxy:"https://github.com.cnpmjs.org",enableProxy:!1,maxRetries:"3",timeout:"30"}),[c,j]=r.useState(!1),[v,A]=r.useState([]),[I,h]=r.useState(!1),C=async()=>{h(!0);try{const s=await P.getScanLogs(1,20);s.success&&A(s.data.logs)}catch(s){console.error("加载扫描日志失败:",s)}finally{h(!1)}};r.useEffect(()=>{C()},[]);const L=()=>{n({title:"设置已保存",description:"GitHub 扫描设置已成功保存。"})},o=(s,$)=>{i(T=>({...T,[s]:$}))},G=async()=>{j(!0),n({title:"开始扫描",description:"正在扫描所有关注的仓库..."});try{const s=await P.scanAllRepositories();s.success?(n({title:"扫描完成",description:`扫描了 ${s.summary.totalRepositories} 个仓库，发现 ${s.summary.totalNewCommits} 个新提交。`}),C()):n({title:"扫描失败",description:s.message||"扫描过程中发生错误",variant:"destructive"})}catch{n({title:"扫描失败",description:"扫描过程中发生网络错误",variant:"destructive"})}finally{j(!1)}},H=s=>{switch(s){case"success":return e.jsxDEV(se,{className:"w-4 h-4 text-github-success"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:125,columnNumber:16},void 0);case"error":return e.jsxDEV(ee,{className:"w-4 h-4 text-github-danger"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:127,columnNumber:16},void 0);case"warning":return e.jsxDEV(_,{className:"w-4 h-4 text-github-warning"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:129,columnNumber:16},void 0);default:return e.jsxDEV(V,{className:"w-4 h-4 text-muted-foreground"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:131,columnNumber:16},void 0)}},B=s=>{switch(s){case"success":return e.jsxDEV(d,{variant:"success",children:"成功"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:138,columnNumber:16},void 0);case"error":return e.jsxDEV(d,{variant:"danger",children:"失败"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:140,columnNumber:16},void 0);case"warning":return e.jsxDEV(d,{variant:"warning",children:"警告"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:142,columnNumber:16},void 0);default:return e.jsxDEV(d,{variant:"secondary",children:"未知"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:144,columnNumber:16},void 0)}};return e.jsxDEV(z,{children:e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(K,{className:"w-8 h-8 text-primary"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:154,columnNumber:13},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-3xl font-bold text-foreground",children:"GitHub 扫描设置"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:156,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-muted-foreground mt-1",children:"配置 GitHub 仓库扫描的参数和代理设置"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:159,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:155,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:153,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex items-center gap-3",children:[e.jsxDEV(S,{variant:"outline",onClick:G,disabled:c,className:"flex items-center gap-2",children:[e.jsxDEV(O,{className:`w-4 h-4 ${c?"animate-spin":""}`},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:171,columnNumber:15},void 0),c?"扫描中...":"立即扫描"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:165,columnNumber:13},void 0),e.jsxDEV(S,{onClick:L,className:"flex items-center gap-2",children:[e.jsxDEV(Q,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:175,columnNumber:15},void 0),"保存设置"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:174,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:164,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:152,columnNumber:9},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxDEV(u,{children:[e.jsxDEV(f,{children:[e.jsxDEV(N,{className:"flex items-center gap-2",children:[e.jsxDEV(U,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:186,columnNumber:17},void 0),"扫描设置"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:185,columnNumber:15},void 0),e.jsxDEV(p,{children:"配置自动扫描 GitHub 仓库的时间间隔和参数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:189,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:184,columnNumber:13},void 0),e.jsxDEV(g,{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"space-y-0.5",children:[e.jsxDEV(m,{className:"text-base",children:"启用自动扫描"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:196,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"定期自动检查仓库更新"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:197,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:195,columnNumber:17},void 0),e.jsxDEV(b,{checked:t.enableAutoScan,onCheckedChange:s=>o("enableAutoScan",s)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:201,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:194,columnNumber:15},void 0),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV(m,{htmlFor:"scanInterval",children:"扫描间隔"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:208,columnNumber:17},void 0),e.jsxDEV(te,{value:t.scanInterval,onValueChange:s=>o("scanInterval",s),disabled:!t.enableAutoScan,children:[e.jsxDEV(ne,{children:e.jsxDEV(ie,{placeholder:"选择扫描间隔"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:215,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:214,columnNumber:19},void 0),e.jsxDEV(re,{children:[e.jsxDEV(a,{value:"1",children:"1 分钟"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:218,columnNumber:21},void 0),e.jsxDEV(a,{value:"5",children:"5 分钟"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:219,columnNumber:21},void 0),e.jsxDEV(a,{value:"10",children:"10 分钟"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:220,columnNumber:21},void 0),e.jsxDEV(a,{value:"15",children:"15 分钟"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:221,columnNumber:21},void 0),e.jsxDEV(a,{value:"30",children:"30 分钟"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:222,columnNumber:21},void 0),e.jsxDEV(a,{value:"60",children:"1 小时"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:223,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:217,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:209,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:207,columnNumber:15},void 0),e.jsxDEV("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV(m,{htmlFor:"maxRetries",children:"最大重试次数"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:230,columnNumber:19},void 0),e.jsxDEV(x,{id:"maxRetries",type:"number",value:t.maxRetries,onChange:s=>o("maxRetries",s.target.value),placeholder:"3",min:"1",max:"10"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:231,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:229,columnNumber:17},void 0),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV(m,{htmlFor:"timeout",children:"超时时间（秒）"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:242,columnNumber:19},void 0),e.jsxDEV(x,{id:"timeout",type:"number",value:t.timeout,onChange:s=>o("timeout",s.target.value),placeholder:"30",min:"5",max:"300"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:243,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:241,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:228,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:193,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:183,columnNumber:11},void 0),e.jsxDEV(u,{children:[e.jsxDEV(f,{children:[e.jsxDEV(N,{className:"flex items-center gap-2",children:[e.jsxDEV(W,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:261,columnNumber:17},void 0),"代理设置"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:260,columnNumber:15},void 0),e.jsxDEV(p,{children:"配置 GitHub 访问代理，用于网络受限的环境"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:264,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:259,columnNumber:13},void 0),e.jsxDEV(g,{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"space-y-0.5",children:[e.jsxDEV(m,{className:"text-base",children:"启用代理"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:271,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground",children:"通过代理服务器访问 GitHub"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:272,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:270,columnNumber:17},void 0),e.jsxDEV(b,{checked:t.enableProxy,onCheckedChange:s=>o("enableProxy",s)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:276,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:269,columnNumber:15},void 0),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV(m,{htmlFor:"githubProxy",children:"GitHub 代理地址"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:283,columnNumber:17},void 0),e.jsxDEV(x,{id:"githubProxy",value:t.githubProxy,onChange:s=>o("githubProxy",s.target.value),placeholder:"https://github.com.cnpmjs.org",disabled:!t.enableProxy},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:284,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-xs text-muted-foreground",children:"常用代理：github.com.cnpmjs.org, hub.fastgit.xyz"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:291,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:282,columnNumber:15},void 0),e.jsxDEV("div",{className:"p-3 bg-muted/50 rounded-lg",children:e.jsxDEV("div",{className:"flex items-start gap-2",children:[e.jsxDEV(Y,{className:"w-4 h-4 text-muted-foreground mt-0.5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:298,columnNumber:19},void 0),e.jsxDEV("div",{className:"text-xs text-muted-foreground",children:[e.jsxDEV("p",{className:"font-medium mb-1",children:"代理说明"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:300,columnNumber:21},void 0),e.jsxDEV("p",{children:"代理服务器将替换 github.com 域名，用于解决网络访问问题。请确保代理服务器稳定可靠。"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:301,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:299,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:297,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:296,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:268,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:258,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:181,columnNumber:9},void 0),e.jsxDEV(u,{children:[e.jsxDEV(f,{children:[e.jsxDEV(N,{className:"flex items-center gap-2",children:[e.jsxDEV(V,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:313,columnNumber:15},void 0),"扫描日志"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:312,columnNumber:13},void 0),e.jsxDEV(p,{children:"查看最近的 GitHub 仓库扫描记录和状态"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:316,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:311,columnNumber:11},void 0),e.jsxDEV(g,{children:e.jsxDEV(k,{className:"h-96",children:e.jsxDEV("div",{className:"space-y-3",children:I?e.jsxDEV("div",{className:"text-center text-muted-foreground py-8",children:"加载中..."},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:324,columnNumber:19},void 0):v.length===0?e.jsxDEV("div",{className:"text-center text-muted-foreground py-8",children:"暂无扫描记录"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:328,columnNumber:19},void 0):v.map(s=>e.jsxDEV("div",{className:"flex items-start gap-4 p-3 rounded-lg border bg-card/50",children:[e.jsxDEV("div",{className:"flex-shrink-0 mt-1",children:H(s.status)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:334,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("div",{className:"flex items-center justify-between gap-4 mb-1",children:[e.jsxDEV("div",{className:"flex items-center gap-2",children:[e.jsxDEV("span",{className:"font-medium text-foreground",children:s.repository},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:341,columnNumber:27},void 0),B(s.status)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:340,columnNumber:25},void 0),e.jsxDEV("span",{className:"text-xs text-muted-foreground whitespace-nowrap",children:s.timestamp},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:344,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:339,columnNumber:23},void 0),e.jsxDEV("p",{className:"text-sm text-muted-foreground mb-1",children:s.message},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:349,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[e.jsxDEV("span",{children:["耗时: ",s.duration,"ms"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:354,columnNumber:25},void 0),s.newCommitsCount>0&&e.jsxDEV("span",{children:["新增: ",s.newCommitsCount," 个提交"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:355,columnNumber:53},void 0),s.updatedCommitsCount>0&&e.jsxDEV("span",{children:["更新: ",s.updatedCommitsCount," 个提交"]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:356,columnNumber:57},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:353,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:338,columnNumber:21},void 0)]},s.id,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:333,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:322,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:321,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:320,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:310,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:150,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/Settings.tsx",lineNumber:149,columnNumber:5},void 0)};export{pe as default};

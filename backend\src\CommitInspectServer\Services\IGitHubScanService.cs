using CommitInspectServer.Entities;

namespace CommitInspectServer.Services;

public interface IGitHubScanService
{
    /// <summary>
    /// 扫描单个仓库，获取最新的提交信息
    /// </summary>
    /// <param name="repositoryId">仓库ID</param>
    /// <returns>扫描结果</returns>
    Task<RepositoryScanResult> ScanRepositoryAsync(Guid repositoryId);
    
    /// <summary>
    /// 扫描指定owner/name的仓库
    /// </summary>
    /// <param name="owner">仓库所有者</param>
    /// <param name="name">仓库名称</param>
    /// <returns>扫描结果</returns>
    Task<RepositoryScanResult> ScanRepositoryAsync(string owner, string name);
    
    /// <summary>
    /// 扫描所有已关注的仓库
    /// </summary>
    /// <returns>所有仓库的扫描结果</returns>
    Task<List<RepositoryScanResult>> ScanAllRepositoriesAsync();
    
    /// <summary>
    /// 获取仓库的提交历史（从GitHub API）
    /// </summary>
    /// <param name="owner">仓库所有者</param>
    /// <param name="name">仓库名称</param>
    /// <param name="since">从此时间开始获取提交</param>
    /// <param name="page">页数</param>
    /// <param name="perPage">每页数量</param>
    /// <returns>提交列表</returns>
    Task<List<GitHubCommit>> GetRepositoryCommitsAsync(string owner, string name, DateTime? since = null, int page = 1, int perPage = 100);
    
    /// <summary>
    /// 获取仓库的所有分支
    /// </summary>
    /// <param name="owner">仓库所有者</param>
    /// <param name="name">仓库名称</param>
    /// <returns>分支列表</returns>
    Task<List<GitHubBranch>> GetRepositoryBranchesAsync(string owner, string name);
    
    /// <summary>
    /// 获取指定分支的提交历史
    /// </summary>
    /// <param name="owner">仓库所有者</param>
    /// <param name="name">仓库名称</param>
    /// <param name="branch">分支名称</param>
    /// <param name="since">从此时间开始获取提交</param>
    /// <param name="page">页数</param>
    /// <param name="perPage">每页数量</param>
    /// <param name="isCommitFetched">是否已获取提交(临时补丁)</param>
    /// <returns>提交列表</returns>
    Task<List<GitHubCommit>> GetBranchCommitsAsync(string owner, string name, string branch, DateTime? since = null, int page = 1, int perPage = 100, Func<string,Task<bool>>? isCommitFetched = null);
}

public class RepositoryScanResult
{
    public Guid RepositoryId { get; set; }
    public string Owner { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int NewCommitsCount { get; set; }
    public int UpdatedCommitsCount { get; set; }
    public DateTime ScanStartTime { get; set; }
    public DateTime ScanEndTime { get; set; }
    public TimeSpan ScanDuration => ScanEndTime - ScanStartTime;
}

public class GitHubCommit
{
    public string Sha { get; set; } = string.Empty; // Keep original GitHub API field name for compatibility
    public string Message { get; set; } = string.Empty;
    public GitHubCommitAuthor Author { get; set; } = new();
    public GitHubCommitAuthor Committer { get; set; } = new();
    public DateTime Date { get; set; }
    public string Url { get; set; } = string.Empty;
    public GitHubCommitStats? Stats { get; set; }
    public List<GitHubCommitFile> Files { get; set; } = new();
}

public class GitHubCommitAuthor
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime Date { get; set; }
}

public class GitHubCommitStats
{
    public int Additions { get; set; }
    public int Deletions { get; set; }
    public int Total { get; set; }
}

public class GitHubCommitFile
{
    public string Filename { get; set; } = string.Empty;
    public int Additions { get; set; }
    public int Deletions { get; set; }
    public int Changes { get; set; }
    public string Status { get; set; } = string.Empty; // added, modified, removed
    public string? Patch { get; set; }
}

public class GitHubBranch
{
    public string Name { get; set; } = string.Empty;
    public GitHubBranchCommit Commit { get; set; } = new();
    public bool Protected { get; set; }
}

public class GitHubBranchCommit
{
    public string Sha { get; set; } = string.Empty; // Keep original GitHub API field name for compatibility
    public string Url { get; set; } = string.Empty;
}
import{j as e}from"./index-D3FXQGgv.js";import{u as n,r as o}from"./react-vendor-DucYQ77b.js";import"./radix-ui-DdAvwPj2.js";import"./utils-CUkY6B8V.js";import"./ui-extras-Cs9DDC1j.js";import"./data-Csye86cT.js";const u=()=>{const t=n();return o.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",t.pathname)},[t.pathname]),e.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("h1",{className:"text-4xl font-bold mb-4",children:"404"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/NotFound.tsx",lineNumber:17,columnNumber:9},void 0),e.jsxDEV("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/NotFound.tsx",lineNumber:18,columnNumber:9},void 0),e.jsxDEV("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/NotFound.tsx",lineNumber:19,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/NotFound.tsx",lineNumber:16,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projects/commit-inspect/frontend/src/pages/NotFound.tsx",lineNumber:15,columnNumber:5},void 0)};export{u as default};

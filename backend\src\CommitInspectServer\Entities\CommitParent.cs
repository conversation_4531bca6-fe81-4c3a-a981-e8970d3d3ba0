using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class CommitParent
{
    [Required]
    public Guid ParentCommitId { get; set; }

    [ForeignKey("ParentCommitId")]
    public virtual Commit ParentCommit { get; set; } = null!;

    [Required]
    public Guid ChildCommitId { get; set; }

    [ForeignKey("ChildCommitId")]
    public virtual Commit ChildCommit { get; set; } = null!;
}
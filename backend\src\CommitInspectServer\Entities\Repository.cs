using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class Repository
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public required string Owner { get; set; }

    [Required]
    public required string Name { get; set; }

    public string? Description { get; set; }

    public string? GithubUrl { get; set; }

    public int Stars { get; set; } = 0;

    public string? Language { get; set; }

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public virtual ICollection<Branch> Branches { get; set; } = new List<Branch>();
    public virtual ICollection<Commit> Commits { get; set; } = new List<Commit>();
    public virtual ICollection<Tag> Tags { get; set; } = new List<Tag>();
}

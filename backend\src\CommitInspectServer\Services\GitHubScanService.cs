using System.Text.Json;
using System.Net.Http.Headers;
using Microsoft.EntityFrameworkCore;
using CommitInspectServer.EfCore;
using CommitInspectServer.Entities;

namespace CommitInspectServer.Services;

public class GitHubScanService : IGitHubScanService
{
    private readonly HttpClient _httpClient;
    private readonly string _personalAccessToken;
    private readonly CommitInspectDbContext _context;
    private readonly ILogger<GitHubScanService> _logger;

    public GitHubScanService(
        HttpClient httpClient, 
        IConfiguration configuration, 
        CommitInspectDbContext context,
        ILogger<GitHubScanService> logger)
    {
        _httpClient = httpClient;
        _personalAccessToken = configuration["GitHub:PersonalAccessToken"] ?? throw new ArgumentException("GitHub Personal Access Token is required");
        _context = context;
        _logger = logger;
        
        _httpClient.BaseAddress = new Uri("https://api.github.com/");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "CommitInspectServer");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("token", _personalAccessToken);
    }

    public async Task<RepositoryScanResult> ScanRepositoryAsync(Guid repositoryId)
    {
        var repository = await _context.Repositories.FirstOrDefaultAsync(r => r.Id == repositoryId);
        if (repository == null)
        {
            return new RepositoryScanResult
            {
                RepositoryId = repositoryId,
                Success = false,
                ErrorMessage = "Repository not found",
                ScanStartTime = DateTime.UtcNow,
                ScanEndTime = DateTime.UtcNow
            };
        }

        return await ScanRepositoryAsync(repository.Owner, repository.Name);
    }

    public async Task<RepositoryScanResult> ScanRepositoryAsync(string owner, string name)
    {
        var result = new RepositoryScanResult
        {
            Owner = owner,
            Name = name,
            ScanStartTime = DateTime.UtcNow
        };

        ScanLog? scanLog = null;

        try
        {
            _logger.LogInformation("开始扫描仓库: {Owner}/{Name}", owner, name);

            // 获取数据库中的仓库信息
            var repository = await _context.Repositories
                .FirstOrDefaultAsync(r => r.Owner == owner && r.Name == name);

            if (repository == null)
            {
                result.Success = false;
                result.ErrorMessage = "Repository not found in database";
                result.ScanEndTime = DateTime.UtcNow;
                return result;
            }

            result.RepositoryId = repository.Id;

            // 创建扫描日志
            scanLog = new ScanLog
            {
                RepositoryId = repository.Id,
                StartTime = result.ScanStartTime,
                EndTime = DateTime.UtcNow, // 临时设置，稍后更新
                Status = ScanStatus.Success, // 临时设置，稍后更新
                Message = "扫描中..."
            };
            _context.ScanLogs.Add(scanLog);
            await _context.SaveChangesAsync();

            // 获取所有分支
            var branches = await GetRepositoryBranchesAsync(owner, name);
            _logger.LogInformation("发现 {Count} 个分支", branches.Count);

            // 保存分支信息到数据库
            await SaveBranchesToDatabaseAsync(repository.Id, branches);

            var newCommitsCount = 0;
            var updatedCommitsCount = 0;

            var cachedShaKeys = new List<string>();

            // 遍历所有分支进行扫描
            foreach (var branch in branches)
            {
                _logger.LogInformation("开始扫描分支: {Branch}", branch.Name);

                // 获取该分支最后一次提交的时间，用于增量更新
                var lastCommitTime = await _context.Commits
                    .Where(c => c.RepositoryId == repository.Id)
                    .OrderByDescending(c => c.CreatedAt)
                    .Select(c => c.CreatedAt)
                    .FirstOrDefaultAsync();

                // 从GitHub API获取该分支的提交
                var commits = await GetBranchCommitsAsync(owner, name, branch.Name, lastCommitTime, 1, 100, async (sha) => {
                    if(cachedShaKeys.Contains(sha)) return true;
                    if(await _context.Commits.AnyAsync(c => c.Hash == sha && c.RepositoryId == repository.Id)) return true;
                    return false;
                });
                
                _logger.LogInformation("从分支 {Branch} 获取到 {Count} 个提交", branch.Name, commits.Count);

                foreach (var githubCommit in commits)
                {

                    var existingCommit = await _context.Commits
                        .FirstOrDefaultAsync(c => c.Hash == githubCommit.Sha && c.RepositoryId == repository.Id);

                    if (existingCommit == null && !cachedShaKeys.Contains(githubCommit.Sha))
                    {
                        // 新提交
                        var commit = new Commit
                        {
                            Hash = githubCommit.Sha,
                            Message = githubCommit.Message,
                            Author = githubCommit.Author.Name,
                            AuthorEmail = githubCommit.Author.Email,
                            CreatedAt = githubCommit.Date,
                            Additions = githubCommit.Stats?.Additions ?? 0,
                            Deletions = githubCommit.Stats?.Deletions ?? 0,
                            FilesChanged = githubCommit.Files.Count,
                            RepositoryId = repository.Id
                        };

                        _context.Commits.Add(commit);
                        newCommitsCount++;

                        // 添加文件变更信息
                        foreach (var file in githubCommit.Files)
                        {
                            var status = file.Status.ToLowerInvariant() switch
                            {
                                "added" => CommitFileStatus.Added,
                                "modified" => CommitFileStatus.Modified,
                                "deleted" or "removed" => CommitFileStatus.Deleted,
                                _ => CommitFileStatus.Modified
                            };

                            var commitFile = new CommitFile
                            {
                                CommitId = commit.Id,
                                Filename = file.Filename,
                                Additions = file.Additions,
                                Deletions = file.Deletions,
                                Status = status,
                                Patch = file.Patch
                            };

                            _context.CommitFiles.Add(commitFile);
                        }
                    }

                    cachedShaKeys.Add(githubCommit.Sha);

                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("分支 {Branch} 扫描完成", branch.Name);
            }

            // 更新分支的 HeadCommit 关联
            await UpdateBranchHeadCommitsAsync(repository.Id, branches);

            // 更新仓库的最后更新时间
            repository.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            result.Success = true;
            result.NewCommitsCount = newCommitsCount;
            result.UpdatedCommitsCount = updatedCommitsCount;
            result.ScanEndTime = DateTime.UtcNow;

            // 更新扫描日志
            if (scanLog != null)
            {
                scanLog.EndTime = result.ScanEndTime;
                scanLog.Status = ScanStatus.Success;
                scanLog.Message = $"成功扫描到 {newCommitsCount} 个新提交";
                scanLog.NewCommitsCount = newCommitsCount;
                scanLog.UpdatedCommitsCount = updatedCommitsCount;
                await _context.SaveChangesAsync();
            }

            _logger.LogInformation("扫描完成: {Owner}/{Name}, 新增 {New} 个提交, 更新 {Updated} 个提交", 
                owner, name, newCommitsCount, updatedCommitsCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描仓库失败: {Owner}/{Name}", owner, name);
            
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ScanEndTime = DateTime.UtcNow;

            // 更新扫描日志为失败状态
            if (scanLog != null)
            {
                scanLog.EndTime = result.ScanEndTime;
                scanLog.Status = ScanStatus.Error;
                scanLog.Message = "扫描失败";
                scanLog.ErrorDetails = ex.Message;
                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (Exception saveEx)
                {
                    _logger.LogError(saveEx, "保存扫描日志失败");
                }
            }

            throw;
        }
    }

    public async Task<List<RepositoryScanResult>> ScanAllRepositoriesAsync()
    {
        _logger.LogInformation("开始扫描所有已关注的仓库");

        var repositories = await _context.Repositories
            .Select(r => new { r.Id, r.Owner, r.Name })
            .ToListAsync();

        var results = new List<RepositoryScanResult>();

        foreach (var repo in repositories)
        {
            try
            {
                var result = await ScanRepositoryAsync(repo.Owner, repo.Name);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描仓库失败: {Owner}/{Name}", repo.Owner, repo.Name);
                
                results.Add(new RepositoryScanResult
                {
                    RepositoryId = repo.Id,
                    Owner = repo.Owner,
                    Name = repo.Name,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ScanStartTime = DateTime.UtcNow,
                    ScanEndTime = DateTime.UtcNow
                });
            }
        }

        _logger.LogInformation("扫描完成，共扫描 {Total} 个仓库，成功 {Success} 个，失败 {Failed} 个", 
            results.Count, results.Count(r => r.Success), results.Count(r => !r.Success));

        return results;
    }

    public async Task<List<GitHubCommit>> GetRepositoryCommitsAsync(string owner, string name, DateTime? since = null, int page = 1, int perPage = 100)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"page={page}",
                $"per_page={perPage}"
            };

            if (since.HasValue)
            {
                queryParams.Add($"since={since.Value:yyyy-MM-ddTHH:mm:ssZ}");
            }

            var url = $"repos/{owner}/{name}/commits?{string.Join("&", queryParams)}";
            
            _logger.LogDebug("请求GitHub API: {Url}", url);

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var commits = JsonSerializer.Deserialize<List<GitHubCommitResponse>>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var result = new List<GitHubCommit>();

            foreach (var commitResponse in commits ?? new List<GitHubCommitResponse>())
            {
                // 获取详细的提交信息（包括文件变更）
                var detailResponse = await _httpClient.GetAsync($"repos/{owner}/{name}/commits/{commitResponse.Sha}");
                if (detailResponse.IsSuccessStatusCode)
                {
                    var detailJsonString = await detailResponse.Content.ReadAsStringAsync();
                    var detailCommit = JsonSerializer.Deserialize<GitHubCommitDetailResponse>(detailJsonString, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                    });

                    if (detailCommit != null)
                    {
                        var commit = new GitHubCommit
                        {
                            Sha = commitResponse.Sha,
                            Message = commitResponse.Commit.Message,
                            Author = new GitHubCommitAuthor
                            {
                                Name = commitResponse.Commit.Author.Name,
                                Email = commitResponse.Commit.Author.Email,
                                Date = commitResponse.Commit.Author.Date
                            },
                            Committer = new GitHubCommitAuthor
                            {
                                Name = commitResponse.Commit.Committer.Name,
                                Email = commitResponse.Commit.Committer.Email,
                                Date = commitResponse.Commit.Committer.Date
                            },
                            Date = commitResponse.Commit.Author.Date,
                            Url = commitResponse.HtmlUrl,
                            Stats = detailCommit.Stats != null ? new GitHubCommitStats
                            {
                                Additions = detailCommit.Stats.Additions,
                                Deletions = detailCommit.Stats.Deletions,
                                Total = detailCommit.Stats.Total
                            } : null,
                            Files = detailCommit.Files?.Select(f => new GitHubCommitFile
                            {
                                Filename = f.Filename,
                                Additions = f.Additions,
                                Deletions = f.Deletions,
                                Changes = f.Changes,
                                Status = f.Status,
                                Patch = f.Patch
                            }).ToList() ?? new List<GitHubCommitFile>()
                        };

                        result.Add(commit);
                    }
                }

                // 添加延迟以避免触发GitHub API限制
                await Task.Delay(100);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取仓库提交失败: {Owner}/{Name}", owner, name);
            throw new Exception($"Failed to get repository commits: {ex.Message}", ex);
        }
    }

    public async Task<List<GitHubBranch>> GetRepositoryBranchesAsync(string owner, string name)
    {
        try
        {
            var url = $"repos/{owner}/{name}/branches";
            
            _logger.LogDebug("请求GitHub API获取分支: {Url}", url);

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var branches = JsonSerializer.Deserialize<List<GitHubBranchResponse>>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return branches?.Select(b => new GitHubBranch
            {
                Name = b.Name,
                Commit = new GitHubBranchCommit
                {
                    Sha = b.Commit.Sha,
                    Url = b.Commit.Url
                },
                Protected = b.Protected
            }).ToList() ?? new List<GitHubBranch>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取仓库分支失败: {Owner}/{Name}", owner, name);
            throw new Exception($"Failed to get repository branches: {ex.Message}", ex);
        }
    }

    public async Task<List<GitHubCommit>> GetBranchCommitsAsync(string owner, string name, string branch, DateTime? since = null, int page = 1, int perPage = 100,Func<string,Task<bool>>? isCommitFetched = null)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"sha={branch}",
                $"page={page}",
                $"per_page={perPage}"
            };

            if (since.HasValue)
            {
                queryParams.Add($"since={since.Value:yyyy-MM-ddTHH:mm:ssZ}");
            }

            var url = $"repos/{owner}/{name}/commits?{string.Join("&", queryParams)}";
            
            _logger.LogDebug("请求GitHub API获取分支提交: {Url}", url);

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            
            var jsonString = await response.Content.ReadAsStringAsync();
            var commits = JsonSerializer.Deserialize<List<GitHubCommitResponse>>(jsonString, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var result = new List<GitHubCommit>();

            foreach (var commitResponse in commits ?? new List<GitHubCommitResponse>())
            {

                if(isCommitFetched != null && await isCommitFetched(commitResponse.Sha))
                {
                    continue;
                }

                // 获取详细的提交信息（包括文件变更）
                var detailResponse = await _httpClient.GetAsync($"repos/{owner}/{name}/commits/{commitResponse.Sha}");
                if (detailResponse.IsSuccessStatusCode)
                {
                    var detailJsonString = await detailResponse.Content.ReadAsStringAsync();
                    var detailCommit = JsonSerializer.Deserialize<GitHubCommitDetailResponse>(detailJsonString, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                    });

                    if (detailCommit != null)
                    {
                        var commit = new GitHubCommit
                        {
                            Sha = commitResponse.Sha,
                            Message = commitResponse.Commit.Message,
                            Author = new GitHubCommitAuthor
                            {
                                Name = commitResponse.Commit.Author.Name,
                                Email = commitResponse.Commit.Author.Email,
                                Date = commitResponse.Commit.Author.Date
                            },
                            Committer = new GitHubCommitAuthor
                            {
                                Name = commitResponse.Commit.Committer.Name,
                                Email = commitResponse.Commit.Committer.Email,
                                Date = commitResponse.Commit.Committer.Date
                            },
                            Date = commitResponse.Commit.Author.Date,
                            Url = commitResponse.HtmlUrl,
                            Stats = detailCommit.Stats != null ? new GitHubCommitStats
                            {
                                Additions = detailCommit.Stats.Additions,
                                Deletions = detailCommit.Stats.Deletions,
                                Total = detailCommit.Stats.Total
                            } : null,
                            Files = detailCommit.Files?.Select(f => new GitHubCommitFile
                            {
                                Filename = f.Filename,
                                Additions = f.Additions,
                                Deletions = f.Deletions,
                                Changes = f.Changes,
                                Status = f.Status,
                                Patch = f.Patch
                            }).ToList() ?? new List<GitHubCommitFile>()
                        };

                        result.Add(commit);
                    }
                }

                // 添加延迟以避免触发GitHub API限制
                await Task.Delay(100);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分支提交失败: {Owner}/{Name}/{Branch}", owner, name, branch);
            throw new Exception($"Failed to get branch commits: {ex.Message}", ex);
        }
    }

    private async Task SaveBranchesToDatabaseAsync(Guid repositoryId, List<GitHubBranch> githubBranches)
    {
        try
        {
            _logger.LogDebug("开始保存分支信息到数据库，仓库ID: {RepositoryId}", repositoryId);

            // 获取数据库中已存在的分支
            var existingBranches = await _context.Branches
                .Where(b => b.RepositoryId == repositoryId)
                .ToListAsync();

            var existingBranchNames = existingBranches.Select(b => b.Name).ToHashSet();
            var currentBranchNames = githubBranches.Select(b => b.Name).ToHashSet();

            // 删除在GitHub中不存在的分支
            var branchesToDelete = existingBranches.Where(b => !currentBranchNames.Contains(b.Name)).ToList();
            if (branchesToDelete.Any())
            {
                _context.Branches.RemoveRange(branchesToDelete);
                _logger.LogInformation("删除 {Count} 个不存在的分支", branchesToDelete.Count);
            }

            // 添加或更新分支
            foreach (var githubBranch in githubBranches)
            {
                var existingBranch = existingBranches.FirstOrDefault(b => b.Name == githubBranch.Name);
                
                if (existingBranch == null)
                {
                    // 新分支
                    var newBranch = new Branch
                    {
                        Name = githubBranch.Name,
                        RepositoryId = repositoryId
                        // HeadCommitId 将在提交处理完成后更新
                    };
                    
                    _context.Branches.Add(newBranch);
                    _logger.LogDebug("添加新分支: {BranchName}", githubBranch.Name);
                }
                // 现有分支不需要更新基本信息，HeadCommitId 会在后续处理
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("分支信息保存完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存分支信息失败，仓库ID: {RepositoryId}", repositoryId);
            throw;
        }
    }

    private async Task UpdateBranchHeadCommitsAsync(Guid repositoryId, List<GitHubBranch> githubBranches)
    {
        try
        {
            _logger.LogDebug("开始更新分支的HeadCommit关联，仓库ID: {RepositoryId}", repositoryId);

            var dbBranches = await _context.Branches
                .Where(b => b.RepositoryId == repositoryId)
                .ToListAsync();

            foreach (var githubBranch in githubBranches)
            {
                var dbBranch = dbBranches.FirstOrDefault(b => b.Name == githubBranch.Name);
                if (dbBranch != null)
                {
                    // 查找对应的头部提交
                    var headCommit = await _context.Commits
                        .FirstOrDefaultAsync(c => c.Hash == githubBranch.Commit.Sha && c.RepositoryId == repositoryId);

                    if (headCommit != null)
                    {
                        dbBranch.HeadCommitId = headCommit.Id;
                        _logger.LogDebug("更新分支 {BranchName} 的HeadCommit: {CommitSha}", githubBranch.Name, githubBranch.Commit.Sha);
                    }
                    else
                    {
                        _logger.LogWarning("未找到分支 {BranchName} 的头部提交: {CommitSha}", githubBranch.Name, githubBranch.Commit.Sha);
                    }
                }
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("分支HeadCommit关联更新完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新分支HeadCommit关联失败，仓库ID: {RepositoryId}", repositoryId);
            throw;
        }
    }
}

// GitHub API 响应模型
public class GitHubCommitResponse
{
    public string Sha { get; set; } = string.Empty;
    public string HtmlUrl { get; set; } = string.Empty;
    public GitHubCommitInfo Commit { get; set; } = new();
}

public class GitHubCommitInfo
{
    public string Message { get; set; } = string.Empty;
    public GitHubCommitAuthor Author { get; set; } = new();
    public GitHubCommitAuthor Committer { get; set; } = new();
}

public class GitHubCommitDetailResponse
{
    public string Sha { get; set; } = string.Empty;
    public GitHubCommitStats? Stats { get; set; }
    public List<GitHubCommitFileResponse>? Files { get; set; }
}

public class GitHubCommitFileResponse
{
    public string Filename { get; set; } = string.Empty;
    public int Additions { get; set; }
    public int Deletions { get; set; }
    public int Changes { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Patch { get; set; }
}

public class GitHubBranchResponse
{
    public string Name { get; set; } = string.Empty;
    public GitHubBranchCommitResponse Commit { get; set; } = new();
    public bool Protected { get; set; }
}

public class GitHubBranchCommitResponse
{
    public string Sha { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
}
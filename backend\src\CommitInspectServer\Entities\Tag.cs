using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CommitInspectServer.Entities;

public class Tag
{
    [Key]
    public Guid Id { get; set; } = Guid.CreateVersion7();

    [Required]
    public required string Name { get; set; }

    public bool IsAnnotated { get; set; } = false;

    public string? AnnotationMessage { get; set; }

    [Required]
    public Guid CommitId { get; set; }

    [ForeignKey("CommitId")]
    public virtual Commit Commit { get; set; } = null!;

    [Required]
    public Guid RepositoryId { get; set; }

    [ForeignKey("RepositoryId")]
    public virtual Repository Repository { get; set; } = null!;
}
using CommitInspectServer.EfCore;
using Microsoft.EntityFrameworkCore;

namespace CommitInspectServer.Services;

public class RepositoryScanBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RepositoryScanBackgroundService> _logger;
    private readonly TimeSpan _scanInterval = TimeSpan.FromHours(1);

    public RepositoryScanBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<RepositoryScanBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Repository scan background service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(_scanInterval, stoppingToken);
                
                await ScanAllRepositoriesAsync();
                
                _logger.LogInformation("Repository scan completed. Next scan in {Hours} hours", _scanInterval.TotalHours);
                
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Repository scan background service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during repository scan");
                
                // 发生错误时等待较短时间后重试
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }
    }

    private async Task ScanAllRepositoriesAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<CommitInspectDbContext>();
        var gitHubScanService = scope.ServiceProvider.GetRequiredService<IGitHubScanService>();

        _logger.LogInformation("Starting repository scan");

        var repositories = await dbContext.Repositories.ToListAsync();
        
        _logger.LogInformation("Found {Count} repositories to scan", repositories.Count);

        foreach (var repository in repositories)
        {
            try
            {
                _logger.LogInformation("Scanning repository: {Owner}/{Name}", repository.Owner, repository.Name);
                
                await gitHubScanService.ScanRepositoryAsync(repository.Owner, repository.Name);
                
                _logger.LogInformation("Successfully scanned repository: {Owner}/{Name}", repository.Owner, repository.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to scan repository: {Owner}/{Name}", repository.Owner, repository.Name);
            }
        }

        _logger.LogInformation("Repository scan batch completed");
    }
}